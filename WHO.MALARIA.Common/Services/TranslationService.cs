﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Reflection;
using System.Text.Json;
namespace WHO.MALARIA.Common.Services
{
    /// <summary>
    /// An Interface holding methods required to translate exception messages.
    /// </summary>
    public interface ITranslationService
    {
        string GetTranslatedMessage(string key);
        string GetTranslation(string key, string translationFileName = "translation");
        string GetDQATranslation(string key);
        string GetCurrentCulture();
    }

    /// <summary>
    /// A Class which can manage the translated exception to be thrown.
    /// </summary>
    public class TranslationService : ITranslationService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<TranslationService> _logger;
        private readonly ICacheDataService _cacheService;

        public TranslationService(IHttpContextAccessor httpContextAccessor,
                                  ILogger<TranslationService> logger,
                                  ICacheDataService cacheService)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _cacheService = cacheService;
        }

        /// <summary>
        ///  Returns the translated business rule message
        /// </summary>
        /// <param name="key">Key as a string</param>
        /// <returns>String of translated business rule message</returns>
        public string GetTranslatedMessage(string key)
        {
            string translationFileData = GetTranslationFileData();

            TranslationModel translationModel = JsonSerializer.Deserialize<TranslationModel>(translationFileData);

            translationModel.Exception.TryGetValue(key, out string translatedLabel);

            return translatedLabel;
        }

        /// <summary>
        /// Get translation for the given key
        /// </summary>
        /// <param name="key">Key as a string</param>
        /// <param name="translationFileName">translationFileName contains name of translation file</param>
        /// <returns>Returns translation for the given key</returns>
        public string GetTranslation(string key, string translationFileName = Constants.Common.CommonResponseTranslationFileName)
        {
            string translationFileData = GetTranslationFileData(translationFileName);

            JObject jsonObject = JObject.Parse(translationFileData);

            return Convert.ToString(jsonObject.SelectToken(key));
        }

        /// <summary>
        /// Get the path of translation file based on environment
        /// </summary>
        /// <returns>path of the file</returns>

        private string TranslationFilePath(string fileName = "translation")
        {
            string assemblyDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            string path = Path.Combine(assemblyDirectory, $"{fileName}.json");

            return path;
        }

        /// <summary>
        /// Get current culture of the user
        /// </summary>
        /// <returns>string value of the culture</returns>
        public string GetCurrentCulture() => _httpContextAccessor?.HttpContext?.Request?.Cookies[Constants.Common.I18next] == null ? Constants.Common.DefaultLanguage : _httpContextAccessor?.HttpContext?.Request?.Cookies[Constants.Common.I18next];

        /// <summary>
        /// Get translation file data
        /// </summary>
        /// <param name="fileName">Name of file</param>
        /// <returns>Translation file data</returns>
        private string GetTranslationFileData(string fileName = "translation")
        {
            fileName = $"{ fileName}.{ GetCurrentCulture()}";

            switch (fileName)
            {
                case Constants.Common.IndicatorResponseTranslationFileName + ".en":

                    return GetTranslationFileDataFromCache(fileName, Constants.TranslationFileNamesCacheKeys.IndicatorResponseEnglishTranslationJson);

                case Constants.Common.IndicatorResponseTranslationFileName + ".fr":

                    return GetTranslationFileDataFromCache(fileName, Constants.TranslationFileNamesCacheKeys.IndicatorResponseFrenchTranslationJson);

                case Constants.Common.CommonResponseTranslationFileName + ".en":

                    return GetTranslationFileDataFromCache(fileName, Constants.TranslationFileNamesCacheKeys.CommonResponseEnglishTranslationJson);

                case Constants.Common.CommonResponseTranslationFileName + ".fr":

                    return GetTranslationFileDataFromCache(fileName, Constants.TranslationFileNamesCacheKeys.CommonResponseFrenchTranslationJson);

                default:

                    return GetTranslationFileDataFromCache(fileName, Constants.TranslationFileNamesCacheKeys.CommonResponseEnglishTranslationJson);
            }
        }

        /// <summary>
        ///  Get translation file data from cache
        /// </summary>
        /// <param name="fileName">Name of file</param>
        /// <param name="cacheKey">Key of cache</param>
        /// <returns>Translation file data</returns>
        public string GetTranslationFileDataFromCache(string fileName, string cacheKey)
        {
            string languageFileData = (string)_cacheService.GetDataFromCache(cacheKey);

            if (languageFileData == null)
            {
                languageFileData = File.ReadAllText(TranslationFilePath(fileName));

                _cacheService.SetDataIntoCache(cacheKey, languageFileData);
            }

            return languageFileData;
        }

        /// <summary>
        /// Get trasaltion for dqa reports
        /// </summary>
        /// <param name="key">key in string</param>
        /// <returns>traslated key value</returns>
        public string GetDQATranslation(string key)
        {
            return GetTranslation(Constants.Common.DataQualityAnalysisTranslationJsonKey + key, Constants.Common.CommonResponseTranslationFileName);
        }
    }
}