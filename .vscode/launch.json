{"version": "0.2.0", "configurations": [{"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/WHO.MALARIA.Web/bin/Debug/net8.0/WHO.MALARIA.Web.dll", "args": [], "cwd": "${workspaceFolder}/WHO.MALARIA.Web", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:5001;http://localhost:5000"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "React App (Vite)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/WHO.MALARIA.Web/malaria-client", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "port": 3000, "restart": true, "env": {"NODE_ENV": "development"}}, {"name": "Attach to .NET Process", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}], "compounds": [{"name": "Launch Full Stack (Backend + Frontend)", "configurations": [".NET Core Launch (web)", "React App (Vite)"], "stopAll": true, "presentation": {"hidden": false, "group": "Full Stack", "order": 1}}]}