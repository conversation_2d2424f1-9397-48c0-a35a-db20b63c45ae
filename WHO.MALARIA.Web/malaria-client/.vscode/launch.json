{"version": "0.2.0", "configurations": [{"name": "Launch Chrome (Debug Mode)", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*", "webpack:///./*": "${webRoot}/*", "webpack:///./~/*": "${webRoot}/node_modules/*", "webpack:///./src/*": "${webRoot}/*"}, "preLaunchTask": "npm: dev", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/node_modules/**"], "smartStep": true, "showAsyncStacks": true}, {"name": "Attach to Chrome", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*", "webpack:///./*": "${webRoot}/*", "webpack:///./~/*": "${webRoot}/node_modules/*", "webpack:///./src/*": "${webRoot}/*"}, "skipFiles": ["<node_internals>/**", "${workspaceFolder}/node_modules/**"]}, {"name": "Debug Vite Dev Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vite", "args": ["--port", "3000"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/node_modules/**"]}], "compounds": [{"name": "Launch App + Debug", "configurations": ["Debug Vite Dev Server", "Launch Chrome (Debug Mode)"], "stopAll": true}]}