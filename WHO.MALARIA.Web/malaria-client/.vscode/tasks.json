{"version": "2.0.0", "tasks": [{"label": "npm: dev", "type": "npm", "script": "dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$", "endsPattern": "^.*ready in.*$"}}}, {"label": "npm: build", "type": "npm", "script": "build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "npm: test", "type": "npm", "script": "test", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}