# VSCode Debug Configuration Guide

This project is now configured for debugging in VSCode. Here are the available debug configurations:

## Debug Configurations

### 1. Launch Chrome (Debug Mode)
- **What it does**: Automatically starts the Vite dev server and launches Chrome with debugging enabled
- **How to use**: 
  1. Set breakpoints in your TypeScript/React code
  2. Press F5 or go to Run and Debug panel
  3. Select "Launch Chrome (Debug Mode)" and click the play button
  4. Chrome will open automatically and connect to the debugger

### 2. Attach to Chrome
- **What it does**: Attaches to an already running Chrome instance with debugging enabled
- **How to use**:
  1. Start Chrome with debugging: `google-chrome --remote-debugging-port=9222`
  2. Start your dev server: `npm run dev`
  3. Select "Attach to Chrome" configuration and run it

### 3. Debug Vite Dev Server
- **What it does**: Debugs the Node.js side (Vite dev server)
- **How to use**: Select this configuration to debug server-side issues

### 4. Launch App + Debug (Compound)
- **What it does**: Runs both the Vite dev server and Chrome debugger simultaneously
- **How to use**: This is the most comprehensive option for full-stack debugging

## Quick Start

1. **Set breakpoints** in your code by clicking in the gutter next to line numbers
2. **Press F5** or use the Run and Debug panel (Ctrl+Shift+D)
3. **Select "Launch Chrome (Debug Mode)"** for the easiest setup
4. **Your app will open in Chrome** and pause at breakpoints

## Tips

- **Source maps are enabled** for proper TypeScript debugging
- **Skip files configuration** ignores node_modules and internals
- **Smart stepping** is enabled for better debugging experience
- **Async stack traces** are shown for Promise debugging

## Troubleshooting

- If breakpoints don't work, ensure source maps are enabled in vite.config.ts
- If Chrome doesn't launch, check that port 3000 is available
- For attach mode, ensure Chrome is started with `--remote-debugging-port=9222`
