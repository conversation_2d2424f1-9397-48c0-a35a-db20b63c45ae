#!/bin/bash

# Malaria Client Development Script
# This script ensures the correct Node.js version is used for development

echo "🚀 Starting Malaria Client Development Server..."
echo "📋 Switching to Node.js 22.15.1..."

# Source nvm and switch to the correct Node.js version
source ~/.nvm/nvm.sh
nvm use 22.15.1

# Check if the switch was successful
if [ $? -eq 0 ]; then
    echo "✅ Node.js version: $(node --version)"
    echo "✅ npm version: $(npm --version)"
    echo ""
    echo "🔥 Starting Vite development server..."
    npm run dev
else
    echo "❌ Failed to switch to Node.js 22.15.1"
    echo "Please make sure Node.js 22.15.1 is installed via nvm:"
    echo "  nvm install 22.15.1"
    exit 1
fi
