#!/bin/bash

# Malaria Client Build Script
# This script ensures the correct Node.js version is used for building

echo "🏗️  Building Malaria Client for Production..."
echo "📋 Switching to Node.js 22.15.1..."

# Source nvm and switch to the correct Node.js version
source ~/.nvm/nvm.sh
nvm use 22.15.1

# Check if the switch was successful
if [ $? -eq 0 ]; then
    echo "✅ Node.js version: $(node --version)"
    echo "✅ npm version: $(npm --version)"
    echo ""
    echo "🔨 Building for production..."
    npm run build
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 Build completed successfully!"
        echo "📁 Build output is in the 'build' directory"
    else
        echo "❌ Build failed!"
        exit 1
    fi
else
    echo "❌ Failed to switch to Node.js 22.15.1"
    echo "Please make sure Node.js 22.15.1 is installed via nvm:"
    echo "  nvm install 22.15.1"
    exit 1
fi
