{"name": "maleria-client", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@babel/code-frame": "^7.14.5", "@mui/icons-material": "^6.1.9", "@progress/kendo-file-saver": "^1.1.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^26.0.22", "@types/react-dom": "^17.0.3", "axios": "^0.21.1", "classnames": "^2.3.1", "d3-geo": "^1.11.7", "hammerjs": "^2.0.8", "i18next": "^20.2.2", "i18next-browser-languagedetector": "^6.1.1", "i18next-http-backend": "^1.2.4", "js-cookie": "^2.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-ga4": "^2.1.0", "react-i18next": "^11.8.15", "sass": "^1.52.3", "terser": "^5.5.1", "topojson-client": "^3.1.0", "typescript": "^5.7.2", "uuid": "^8.3.2", "web-vitals": "^1.1.1"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "start": "vite"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@date-io/date-fns": "^3.0.0", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/material": "^6.1.9", "@mui/x-date-pickers": "^7.22.2", "@popperjs/core": "^2.9.2", "@progress/kendo-data-query": "^1.6.0", "@progress/kendo-drawing": "^1.19.1", "@progress/kendo-licensing": "^1.3.5", "@progress/kendo-react-animation": "^10.2.0", "@progress/kendo-react-charts": "^10.2.0", "@progress/kendo-react-data-tools": "^10.2.0", "@progress/kendo-react-dateinputs": "^10.2.0", "@progress/kendo-react-dropdowns": "^10.2.0", "@progress/kendo-react-grid": "^10.2.0", "@progress/kendo-react-inputs": "^10.2.0", "@progress/kendo-react-intl": "^10.2.0", "@progress/kendo-react-treeview": "^10.2.0", "@progress/kendo-theme-material": "^10.2.0", "@types/crypto-js": "^4.0.1", "@types/js-cookie": "^2.2.6", "@types/node": "^22.15.21", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-router-dom": "^5.1.7", "@types/uuid": "^8.3.3", "@vitejs/plugin-react": "^4.5.0", "babel-preset-react-app": "^10.0.1", "bootstrap": "^5.0.1", "crypto-js": "^4.0.0", "date-fns": "^2.30.0", "flag-icon-css": "^3.5.0", "framer-motion": "^11.15.0", "html-react-parser": "^5.0.20", "jsdom": "^26.1.0", "react-redux": "^9.1.2", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^7.1.1", "redux": "^5.0.1", "redux-saga": "^1.1.3", "rxjs": "^7.1.0", "vite": "^6.3.5", "vitest": "^3.1.4"}, "browser": {"crypto": false}}