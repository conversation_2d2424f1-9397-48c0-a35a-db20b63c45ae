﻿import { useEffect, useState } from "react";
import { Backdrop, CircularProgress } from "@mui/material";

import Notification from "./components/controls/Notification";

import { BaseMessageModel, ErrorModel } from "./models/ErrorModel";
import {
  loaderService,
  notificationService,
} from "./services/notificationService";
import { authService } from "./services/authService";

import PrivateRoutes from "./components/ui/PrivateRoutes";
import PublicRoutes from "./components/ui/PublicRoutes";
import ReactGA from "react-ga4";
import { userService } from "./services/userService";
import { UserRoleEnum } from "./models/Enums";
import { useDispatch } from "react-redux";
import { showSelectedCountry } from "./redux/ducks/selected-country";
import { Constants } from "./models/Constants";

const trackingId: string = authService.getTrackingId();

if (trackingId) {
  ReactGA.initialize(trackingId);
}

function App() {
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();
  const [notification, setNotification] = useState<
    BaseMessageModel | ErrorModel
  >(BaseMessageModel.init());
  const [loaders, setLoaders] = useState<Array<number>>([]);

  //Using analytics -pages that are visited the most
  useEffect(() => {
    ReactGA.send({
      hitType: "pageview",
      page: window.location.pathname + window.location.search,
    });
    const currentUser = authService.getCurrentUser();
    const isAuthenticated = authService.isAuthenticated();
    if (isAuthenticated && currentUser.userType !== UserRoleEnum.WHOAdmin) {
      getUserActiveCountries();
    }
  }, []);

  useEffect(() => {
    const subscription = notificationService
      .getMessage()
      .subscribe((message) => {
        if (
          message instanceof BaseMessageModel ||
          message instanceof ErrorModel
        ) {
          setOpen(true);
          setNotification(message as BaseMessageModel | ErrorModel);
        }
      });

    const loaderSubscription = loaderService
      .getLoading()
      .subscribe((isLoading) => {
        //Loader is going to be shown for multiple API calls and IF on a screen more than one API call triggers, the one
        //which finishes early makes the isLoading flag to false therefore the API is in the progress doesn't get the loader
        //on the screen. To resolve this problem we are maintaining the array where on every API call we push the new element
        //in the array and removes one element once API call finishes so at end when all call finishes the array becomes empty and not loader
        //is shown on to the screen.
        if (isLoading)
          setLoaders((prevState: Array<number>) => [...prevState, 1]);
        else
          setLoaders((prevState: Array<number>) =>
            prevState.slice(0, prevState.length - 1)
          );
      });

    return () => {
      setOpen(false);
      subscription.unsubscribe();
      loaderSubscription.unsubscribe();
    };
  }, []);

  // Get user active countries and add in session storage
  const getUserActiveCountries = () => {
    userService.getUserActiveCountries().then((response) => {
      sessionStorage.setItem(
        Constants.SessionStorageKey.ACTIVE_USER_COUNTRIES,
        JSON.stringify(response)
      );
      dispatch(showSelectedCountry(true));
    });
  };

  return (
    <div>
      {!!loaders.length && (
        <Backdrop className="loader" open={!!loaders.length}>
          <CircularProgress color="inherit" />
        </Backdrop>
      )}

      <Notification
        open={open}
        notification={notification}
        onCancel={() => setOpen(false)}
      />
      {authService.canRenderPrivateRoute() ? (
        <PrivateRoutes />
      ) : (
        <PublicRoutes />
      )}
    </div>
  );
}

export default App;
