import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { ChangeEvent, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import TableRow from "../../../responses/TableRow";
import { useSelector } from "react-redux";
import { NumberOfRecordingForm } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_1/Response_1";
import { Response_1, StandardizedFormsDetail, StandardizedRecordingTool } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_3/Response_1";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import useFormValidation from "../../../../../../common/useFormValidation";
import UserMessage from "../../../../../../common/UserMessage";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import ValidationRules from "./ValidationRules";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { useLocation } from "react-router-dom";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response indicator 3.2.3 */
function Indicator_3_2_3_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_2_3_Title");
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;

    const header = t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:RecordingTool");

    const rows: any = {
        recordingName: {
            field: "recordingName",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:RecordingName")
        },
        isStandardizedRecordingTools: {
            field: "isStandardizedRecordingTools",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:RecodringFormsAndTools"),
        },
        standardizedFormsDetails: {
            field: "standardizedFormsDetails",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:DetailsOnPrivatePublicUseStandardisedForm"),
        },
    };

    //Holds the validation rules and it gets changed when cannot be assessed is set to true.
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getParentResponse,
        onChangeWithKeyInArray,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(strategyId), validate);

    const errors = useSelector((state: any) => state.error);

    const isParentDataAssessed: string = response?.parentData && response?.parentData?.recordingTools[0]?.nameOfReportingToolSourceDocument;

    useEffect(() => {
        const recordingToolCount = response.parentData?.recordingTools?.length
        onValueChange(
            "parentRecordingToolCount",
            recordingToolCount
        );
    }, [response?.parentData]);

    //Process data on save and calls processDataAndSave method based on the isFinalized flag
    const onResponseSave = () => {
        processDataAndSave(false)
    }

    //Process data on finalize and calls processDataAndSave method based on the isFinalized flag
    const onResponseFinalize = () => {
        processDataAndSave(true)
    }

    //Process data set the date control value then save or finalize
    const processDataAndSave = (isFinalized: boolean) => {
        let _response = response;

        // Here response.standardizedRecordingTools is filtered and it returns the number of columns that can be shown for the NumberOfRecordingForm and its FormsDetail
        response.standardizedRecordingTools
            .filter(
                (standardizedRecordingTool: StandardizedRecordingTool) =>
                    !response.parentData.recordingTools.some(
                        (numberOfRecordingForm: NumberOfRecordingForm) => {
                            return (
                                numberOfRecordingForm.recordingToolId ===
                                standardizedRecordingTool.recordingToolId
                            );
                        }
                    )
            )
            .forEach((standardizedRecordingTool: StandardizedRecordingTool) => {
                _response.standardizedRecordingTools =
                    _response.standardizedRecordingTools.filter(
                        (srTool: StandardizedRecordingTool) =>
                            srTool.recordingToolId !==
                            standardizedRecordingTool.recordingToolId
                    );
                _response.standardizedFormsDetails =
                    _response.standardizedFormsDetails.filter(
                        (sfDetail: StandardizedFormsDetail) =>
                            standardizedRecordingTool.recordingToolId !==
                            sfDetail.recordingToolId
                    );
            });

        onValueChange(
            "standardizedRecordingTools",
            _response.standardizedRecordingTools
        );
        onValueChange(
            "standardizedFormsDetails",
            _response.standardizedFormsDetails
        );

        // triggers on click of finalize button, performs validations and then action is performed
        if (isFinalized) {
            setTrueFlagOnFinalizeButtonClick();
            const isFormValid = validate(response);
            if (isFormValid)
                onFinalize();
        } else {
            onSave();
        }
    };

    useEffect(() => {
        getParentResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    // Get control value with recording tool id and control name for different controls(Textbox and Radio)
    const generateControlValue = (
        recordingToolId: string,
        controlName: string,
        controlDataArrayName: string
    ) => {
        let value: string | null | boolean = null;
        if (
            response[controlDataArrayName].find(
                (controlDataName: StandardizedRecordingTool | StandardizedFormsDetail) => controlDataName.recordingToolId === recordingToolId
            ) !== undefined &&
            response[controlDataArrayName].find(
                (controlDataName: StandardizedRecordingTool | StandardizedFormsDetail) => controlDataName.recordingToolId === recordingToolId
            )[controlName] !== undefined
        ) {
            value = response[controlDataArrayName].find(
                (controlDataName: StandardizedRecordingTool | StandardizedFormsDetail) => controlDataName.recordingToolId === recordingToolId
            )[controlName];
        }
        return value;
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed && isParentDataAssessed ? (
                <div className="response-wrapper">
                    {
                        // Here response.standardizedRecordingTools is filtered and it returns the number of columns that can be shown for the NumberOfRecordingForm and its FormsDetail
                        response.standardizedRecordingTools.filter(
                            (standardizedFormsDetail: StandardizedRecordingTool) =>
                                !response.parentData.recordingTools.some(
                                    (numberOfRecordingForm: NumberOfRecordingForm) => {
                                        return (
                                            numberOfRecordingForm.recordingToolId ===
                                            standardizedFormsDetail.recordingToolId
                                        );
                                    }
                                )
                        )?.length > 0 ? (
                            <span className="text-danger">
                                {t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:SaveWarning"
                                )}
                            </span>
                        ) : (
                            <></>
                        )
                    }

                    {
                        // Show error message if user does not select 'Yes' or 'No' and provide details for each recording tool to finalize the indicator
                        !!Object.keys(errors).length &&
                        <span className="Mui-error d-flex mb-2">
                            * {t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:ResponseError")}
                        </span>
                    }
                    <Table>
                        <>
                            <TableHead>
                                {response.parentData.recordingTools.map(
                                    (
                                        numberOfRecordingForm: NumberOfRecordingForm,
                                        index: number
                                    ) =>
                                        // Here response.parentData.recordingTools is array and in UI first column is header with no label and to not miss this condition
                                        index === 0 ? (
                                            <>
                                                <TableHeaderCell>
                                                    <span></span>
                                                </TableHeaderCell>
                                                <TableHeaderCell>
                                                    <span>{`${header} ${index + 1}`}</span>
                                                </TableHeaderCell>
                                            </>
                                        ) : (
                                            <TableHeaderCell>
                                                <span>{`${header} ${index + 1}`}</span>
                                            </TableHeaderCell>
                                        )
                                )}
                            </TableHead>
                            <TableBody>
                                <>
                                    {Object.keys(rows).map(
                                        (modelKeyName: string, rowIndex: number) => (
                                            <TableRow id={`${modelKeyName}_${rowIndex + 1}`}>
                                                <>
                                                    <TableCell width="600px">
                                                        <>{rows[modelKeyName].label}</>
                                                    </TableCell>
                                                    {response.parentData.recordingTools.map(
                                                        (
                                                            numberOfRecordingForm: NumberOfRecordingForm,
                                                            columnIndex: number
                                                        ) =>
                                                            rowIndex === 0 ? (
                                                                <TableCell>
                                                                    <TextBox
                                                                        id={`nameOfReportingToolSourceDocument${columnIndex + 1
                                                                            }`}
                                                                        variant="outlined"
                                                                        disabled={true}
                                                                        fullWidth
                                                                        value={
                                                                            numberOfRecordingForm?.nameOfReportingToolSourceDocument
                                                                        }
                                                                    />
                                                                </TableCell>
                                                            ) : rowIndex === 1 ? (
                                                                <TableCell>
                                                                    <>
                                                                        <RadioButtonGroup
                                                                            id={`standardizedRecordingTools${numberOfRecordingForm?.nameOfReportingToolSourceDocument
                                                                                }_${columnIndex + 1}`}
                                                                            name={
                                                                                numberOfRecordingForm.recordingToolId
                                                                            }
                                                                            row
                                                                            color="primary"
                                                                            options={[
                                                                                new MultiSelectModel(
                                                                                    true,
                                                                                    t(
                                                                                        "indicators-responses:Common:Yes"
                                                                                    )
                                                                                ),
                                                                                new MultiSelectModel(
                                                                                    false,
                                                                                    t(
                                                                                        "indicators-responses:Common:No"
                                                                                    )
                                                                                ),
                                                                            ]}
                                                                            value={generateControlValue(
                                                                                numberOfRecordingForm?.recordingToolId,
                                                                                "areStandardizedAcrossAllService",
                                                                                "standardizedRecordingTools"
                                                                            )}
                                                                            onChange={(
                                                                                e: React.ChangeEvent<HTMLInputElement>
                                                                            ) =>
                                                                                onChangeWithKeyInArray(
                                                                                    e,
                                                                                    "standardizedRecordingTools",
                                                                                    columnIndex,
                                                                                    "recordingToolId",
                                                                                    "areStandardizedAcrossAllService"
                                                                                )
                                                                            }
                                                                            error={errors[`standardizedRecordingTools[${rowIndex}].areStandardizedAcrossAllService`] && errors[`standardizedRecordingTools[${rowIndex}].areStandardizedAcrossAllService`]}
                                                                        />
                                                                    </>
                                                                </TableCell>
                                                            ) : rowIndex === 2 ? (
                                                                <TableCell>
                                                                    <TextBox
                                                                        id={`standardizedFormsDetails${numberOfRecordingForm?.nameOfReportingToolSourceDocument
                                                                            }_${columnIndex + 1}`}
                                                                        name={
                                                                            numberOfRecordingForm.recordingToolId
                                                                        }
                                                                        variant="outlined"
                                                                        fullWidth
                                                                        multiline
                                                                        rows={2}
                                                                        value={generateControlValue(
                                                                            numberOfRecordingForm?.recordingToolId,
                                                                            "details",
                                                                            "standardizedFormsDetails"
                                                                        )}
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                        ) =>
                                                                            onChangeWithKeyInArray(
                                                                                e,
                                                                                "standardizedFormsDetails",
                                                                                columnIndex,
                                                                                "recordingToolId",
                                                                                "details"
                                                                            )
                                                                        }
                                                                        error={errors[`standardizedFormsDetails$[${rowIndex}].details`] && errors[`standardizedFormsDetails[${rowIndex}].details`]}
                                                                    />
                                                                </TableCell>
                                                            ) : (
                                                                <></>
                                                            )
                                                    )}
                                                </>
                                            </TableRow>
                                        )
                                    )}
                                </>
                            </TableBody>
                        </>
                    </Table>
                </div>
            ) : (
                <>
                    <div className="response-wrapper d-flex w-100 flex-column">
                        {isParentDataAssessed === null &&
                            <UserMessage
                                renderContent={
                                    <>
                                        <b>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:NoRecordingToolFound")}</b>
                                        <p>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:IndicatorParentDependentMessage")}</p>
                                    </>
                                }
                            />
                        }
                        {response.cannotBeAssessed &&
                            <TextBox
                                id="cannotBeAssessedReason"
                                name="cannotBeAssessedReason"
                                label={t(
                                    "indicators-responses:Common:IndicatorNoAssessReasons"
                                )}
                                multiline
                                rows={10}
                                variant="outlined"
                                fullWidth
                                value={response?.cannotBeAssessedReason}
                                onChange={onChange}
                                error={
                                    errors["cannotBeAssessedReason"] &&
                                    errors["cannotBeAssessedReason"]
                                }
                                helperText={
                                    errors["cannotBeAssessedReason"] &&
                                    errors["cannotBeAssessedReason"]
                                }
                            />
                        }
                    </div>
                </>
            )}
            <SaveFinalizeButton onSave={onResponseSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_2_3_Response;
