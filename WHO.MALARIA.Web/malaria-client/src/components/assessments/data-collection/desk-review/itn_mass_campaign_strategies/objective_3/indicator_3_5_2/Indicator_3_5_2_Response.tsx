﻿import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import React, { ChangeEvent, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@mui/material";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { Response_2 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_5_2/Response_2";
import { MonitoringFrequency } from "../../../../../../../models/Enums";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import parse from "html-react-parser";
import Dropdown from "../../../../../../controls/Dropdown";

/** Renders the response for indicator 3.5.2 */
function Indicator_3_5_2_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_5_2_Title");

    const headers = [
        {
            field: "",
            label: "",
        },
        {
            field: "dataCleaning",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:DataCleaning"
            ),
        },
        {
            field: "dataReviewMeeting",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:DataReviewMeetings"
            ),
        },
        {
            field: "dataQualityAssessment",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:DataQualityAssessments"
            ),
        },
        {
            field: "monitoringDataQuality",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:MonitoringDataQuality"
            ),
        },
    ];

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        onChangeWithKey,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_2>(Response_2.init(), validate);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    // Triggered whenever the dropdown control values are changed and update the denominator value based on nationalValidationActivity
    const onDropdownClick = (value: string, modelKeyName: string) => {
        let frequencyDenominator: number | null = null;

        switch (value) {
            case MonitoringFrequency.Annually:
                frequencyDenominator = 1;
                break;
            case MonitoringFrequency.Biannually:
                frequencyDenominator = 2;
                break;
            case MonitoringFrequency.Quarterly:
                frequencyDenominator = 4;
                break;
            case MonitoringFrequency.Monthly:
                frequencyDenominator = 12;
                break;
            case MonitoringFrequency.Weekly:
                frequencyDenominator = 52;
                break;
        }

        if (modelKeyName === "nationalValidationActivity") {
            onValueChange("frequencyDenominator", frequencyDenominator);
        }

        const frequencyValidation = {
            ...response[modelKeyName],
            "dataQualityAssessment": value
        }
        onValueChange(modelKeyName, frequencyValidation);
    };

    const rowsTranslationKeys: any = {
        nationalValidationActivity: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:NationalFrequencyValidation"),
        regionalValidationActivity: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:RegionalFrequencyValidation"),
        districtValidationActivity: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:DistrictFrequencyValidation"),
    };

    /** Excluded property that are not used in Array Map */
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "dataQualityActivity",
        "frequencyDenominator",
        "nationalLevelDataAssessed",
        "malariaSurveillanceQuality",
        "metNotMetStatus",
    ];

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="mb-4">
                        {/*Show error message if not all radio buttons are selected for Data quality assurance procedure in place*/}
                        {!!Object.keys(errors).length && (
                            <span className="Mui-error d-flex mb-2">
                                *{" "}
                                {t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:ResponseError"
                                )}
                            </span>
                        )}
                        <Table>
                            <>
                                <TableHeader
                                    headers={headers.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        <TableRow>
                                            <>
                                                <TableCell>
                                                    <>
                                                        {t(
                                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:DataQualityAssurance"
                                                        )}
                                                    </>
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="dataCleaning"
                                                        name="dataCleaning"
                                                        row
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={
                                                            response["dataQualityActivity"]
                                                                ?.dataCleaning
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onChangeWithKey(
                                                                e,
                                                                "dataQualityActivity"
                                                            );
                                                        }}
                                                        error={
                                                            errors[
                                                            "dataQualityActivity.dataCleaning"
                                                            ] &&
                                                            errors[
                                                            "dataQualityActivity.dataCleaning"
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="dataReviewMeeting"
                                                        name="dataReviewMeeting"
                                                        row
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={
                                                            response["dataQualityActivity"]
                                                                ?.dataReviewMeeting
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onChangeWithKey(
                                                                e,
                                                                "dataQualityActivity"
                                                            );
                                                        }}
                                                        error={
                                                            errors[
                                                            "dataQualityActivity.dataReviewMeeting"
                                                            ] &&
                                                            errors[
                                                            "dataQualityActivity.dataReviewMeeting"
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="dataQualityAssessment"
                                                        name="dataQualityAssessment"
                                                        row
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={
                                                            response["dataQualityActivity"]
                                                                ?.dataQualityAssessment
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onChangeWithKey(
                                                                e,
                                                                "dataQualityActivity"
                                                            );
                                                        }}
                                                        error={
                                                            errors[
                                                            "dataQualityActivity.dataQualityAssessment"
                                                            ] &&
                                                            errors[
                                                            "dataQualityActivity.dataQualityAssessment"
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="dataQualityIndicator"
                                                        name="dataQualityIndicator"
                                                        row
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={
                                                            response["dataQualityActivity"]
                                                                ?.dataQualityIndicator
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onChangeWithKey(
                                                                e,
                                                                "dataQualityActivity"
                                                            );
                                                        }}
                                                        error={
                                                            errors[
                                                            "dataQualityActivity.dataQualityIndicator"
                                                            ] &&
                                                            errors[
                                                            "dataQualityActivity.dataQualityIndicator"
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>

                                        {Object.keys(response)
                                            .filter((key) => !excludedProperties.includes(key))
                                            .map((modelKeyName: string, index: number) => (
                                                <TableRow key={`row_${modelKeyName}_${index}`}>
                                                    <>
                                                        <TableCell>{rowsTranslationKeys[modelKeyName]}</TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id={`dataCleaning_${index}_${Math.random()}`}
                                                                name="dataCleaning"
                                                                placeholder={t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:egMonthly")}
                                                                fullWidth
                                                                multiline
                                                                rows={3}
                                                                value={
                                                                    response[modelKeyName]?.dataCleaning
                                                                }
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => {
                                                                    onChangeWithKey(e, modelKeyName);
                                                                }}
                                                                error={
                                                                    errors[`${modelKeyName}.dataCleaning`] &&
                                                                    errors[`${modelKeyName}.dataCleaning`]
                                                                }
                                                                helperText={
                                                                    errors[`${modelKeyName}.dataCleaning`] &&
                                                                    errors[`${modelKeyName}.dataCleaning`]
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id={`dataReviewMeeting_{index}_${Math.random()}`}
                                                                name="dataReviewMeeting"
                                                                placeholder={t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:egMonthly")}
                                                                fullWidth
                                                                multiline
                                                                rows={3}
                                                                value={
                                                                    response[modelKeyName]?.dataReviewMeeting
                                                                }
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => {
                                                                    onChangeWithKey(e, modelKeyName);
                                                                }}
                                                                error={
                                                                    errors[`${modelKeyName}.dataReviewMeeting`] &&
                                                                    errors[`${modelKeyName}.dataReviewMeeting`]
                                                                }
                                                                helperText={
                                                                    errors[`${modelKeyName}.dataReviewMeeting`] &&
                                                                    errors[`${modelKeyName}.dataReviewMeeting`]
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Dropdown
                                                                id="dataQualityAssessment"
                                                                name="dataQualityAssessment"
                                                                variant="outlined"
                                                                size="small"
                                                                label={t(
                                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:MonitoringFrequency"
                                                                )}
                                                                value={response[modelKeyName]?.dataQualityAssessment}
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        MonitoringFrequency.Annually,
                                                                        t(
                                                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:Annually"
                                                                        )
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        MonitoringFrequency.Biannually,
                                                                        t(
                                                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:Biannually"
                                                                        )
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        MonitoringFrequency.Quarterly,
                                                                        t(
                                                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:Quarterly"
                                                                        )
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        MonitoringFrequency.Monthly,
                                                                        t(
                                                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:Monthly"
                                                                        )
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        MonitoringFrequency.Weekly,
                                                                        t(
                                                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:Weekly"
                                                                        )
                                                                    ),
                                                                ]}
                                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                                    onDropdownClick(e.currentTarget.value, modelKeyName)
                                                                }
                                                                error={
                                                                    errors[`${modelKeyName}.dataQualityAssessment`] &&
                                                                    errors[`${modelKeyName}.dataQualityAssessment`]
                                                                }
                                                                helperText={
                                                                    errors[`${modelKeyName}.dataQualityAssessment`] &&
                                                                    errors[`${modelKeyName}.dataQualityAssessment`]
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id={`dataQualityIndicator_{index}_${Math.random()}`}
                                                                name="dataQualityIndicator"
                                                                fullWidth
                                                                multiline
                                                                rows={3}
                                                                value={
                                                                    response[modelKeyName]?.dataQualityIndicator
                                                                }
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => {
                                                                    onChangeWithKey(e, modelKeyName);
                                                                }}
                                                                error={
                                                                    errors[`${modelKeyName}.dataQualityIndicator`] &&
                                                                    errors[`${modelKeyName}.dataQualityIndicator`]
                                                                }
                                                                helperText={
                                                                    errors[`${modelKeyName}.dataQualityIndicator`] &&
                                                                    errors[`${modelKeyName}.dataQualityIndicator`]
                                                                }
                                                            />
                                                        </TableCell>
                                                    </>
                                                </TableRow>
                                            ))}
                                    </>
                                </TableBody>

                            </>
                        </Table>
                    </div>

                    <div className="row">
                        <div className="col-md-6">
                            <Table>
                                <>
                                    <thead>
                                        <>
                                            <th>
                                                {parse(t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:FrequencyOfValidationNumerator"
                                                ))}
                                            </th>
                                            <th>
                                                {parse(t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:FrequencyOfValidationDenominator"
                                                ))}
                                            </th>
                                        </>
                                    </thead>
                                    <TableBody>
                                        <>
                                            <TableRow>
                                                <>
                                                    <TableCell>
                                                        <TextBox
                                                            id="nationalLevelDataAssessed"
                                                            name="nationalLevelDataAssessed"
                                                            label={t("indicators-responses:Common:Number")}
                                                            type="number"
                                                            className="col-form-control inputfocus"
                                                            InputLabelProps={{ required: true, shrink: true }}
                                                            inputProps={{
                                                                max: response.frequencyDenominator,
                                                                min: 0,
                                                            }}
                                                            maxLength={1}
                                                            fullWidth
                                                            value={response?.nationalLevelDataAssessed}
                                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                                onChange(e);
                                                            }}
                                                            error={
                                                                errors["nationalLevelDataAssessed"] &&
                                                                errors["nationalLevelDataAssessed"]
                                                            }
                                                            helperText={
                                                                errors["nationalLevelDataAssessed"] &&
                                                                errors["nationalLevelDataAssessed"]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <span>
                                                            {response?.frequencyDenominator}
                                                        </span>
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        </>
                                    </TableBody>
                                </>
                            </Table>
                        </div>
                    </div>

                    <div className="row">
                        <div className="col-sm-6">
                            <TextBox
                                id="malariaSurveillanceQuality"
                                name="malariaSurveillanceQuality"
                                label={t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_2:TextBoxCommentPlaceholder"
                                )}
                                multiline
                                rows={10}
                                variant="outlined"
                                fullWidth
                                InputLabelProps={{ shrink: true }}
                                value={response?.malariaSurveillanceQuality}
                                onChange={onChange}
                            />
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_5_2_Response;