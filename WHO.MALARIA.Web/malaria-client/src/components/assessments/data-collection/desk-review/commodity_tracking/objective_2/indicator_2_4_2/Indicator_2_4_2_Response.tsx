﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import React, { ChangeEvent, useEffect, useRef } from "react";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import TableCell from "../../../responses/TableCell";
import { Add, Delete } from "@mui/icons-material";
import {
  Response_1,
  EquipmentAvailability,
} from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_4_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import parse from "html-react-parser";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 2.4.2 response for desk review */
const Indicator_2_4_2_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t("indicators-responses:app:DR_Objective_2_Indicator_2_4_2_Title");
  const { calculateProportionRate } = useCalculation();

  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
  const validate = useFormValidation(validationRulesRef.current);

  const {
    response,
    onCannotBeAssessed,
    onChangeOfArrayWithIndex,
    onSave,
    onFinalize,
    onValueChange,
    getResponse,
    onChange,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCapture<Response_1>(
    new Response_1(false, null, null, [EquipmentAvailability.init()]),
    validate
  );

  useEffect(() => {
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

  }, [response?.cannotBeAssessed]);

  const errors = useSelector((state: any) => state.error);

  // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
  let isProportionRateValidForNationalReq: boolean = true;
  let isProportionRateValidForSubNationalReq: boolean = true;
  let isProportionRateValidForServiceDelReq: boolean = true;

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid && isProportionRateValidForNationalReq && isProportionRateValidForSubNationalReq && isProportionRateValidForServiceDelReq) {
      onFinalize();
    }
  };

  //Triggers onChange of cannotBeAssessed checkbox 
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current =
      evt.currentTarget.checked
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

    onCannotBeAssessed(evt);
  }

  // Triggered whenever 'Delete Row' button is clicked.
  const onRowDelete = () => {
    onValueChange(
      "equipmentAvailabilities",
      response.equipmentAvailabilities.filter(
        (index: number) =>
          index < response.equipmentAvailabilities.length - 1
      )
    );
  };

  // Triggered whenever 'Add Row' button is clicked.
  const onRowAdd = () => {
    onValueChange("equipmentAvailabilities", [...response.equipmentAvailabilities, EquipmentAvailability.init()]);
  };

  //Method creates the arrays for two different property to calculate the proportion rate
  const calculateProportionRateForProperty = (
    modelKeyNameOne: string,
    modelKeyNameTwo: string
  ) => {
    let columnOneSum: number = 0;
    let columnTwoSum: number = 0;

    response.equipmentAvailabilities.map((item: any) => {
      columnOneSum += item[modelKeyNameOne];
      columnTwoSum += item[modelKeyNameTwo];
    });

    return calculateProportionRate(columnOneSum, columnTwoSum);
  };

  //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
  const calculateProportionRateProperty = (propertyName1: string, propertyName2: string) => {
    const proportionRateValue = calculateProportionRateForProperty(propertyName1, propertyName2);

    const proportionRateExceptionContent =
      <span className="Mui-error d-flex mb-2">
        * {t("indicators-responses:Common:ResponseProportionError")}
      </span>

    switch (propertyName1) {
      case "nationalRequired":
        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
          isProportionRateValidForNationalReq = true;
          return proportionRateValue + '%';
        }

        isProportionRateValidForNationalReq = false;
        return proportionRateExceptionContent;

      case "subNationalRequired":
        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
          isProportionRateValidForSubNationalReq = true;
          return proportionRateValue + '%';
        }

        isProportionRateValidForSubNationalReq = false;
        return proportionRateExceptionContent;

      case "serviceDeliveryRequired":
        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
          isProportionRateValidForServiceDelReq = true;
          return proportionRateValue + '%';
        }

        isProportionRateValidForServiceDelReq = false;
        return proportionRateExceptionContent;
    }

  }

  return (
    <>
      <div className="response-assess-wrapper">
        <Checkbox
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response.cannotBeAssessed}
        />
      </div>

      {!response.cannotBeAssessed ? (
        <div className="response-wrapper">
          <div className="response-content">
            <div className="row">
              <div className="col-xs-12 col-md-12">
                <p>
                  {parse(t("indicators-responses:DRObjective_2_Responses:Indicator_2_4_2:CommodityResponseDesc"))}
                </p>
                <p>
                  {t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_2:ConsiderAllEquipmemt"
                  )}
                </p>
                <p>
                  {t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_2:ListEquipment"
                  )}
                </p>

                <div className="mt-3 table-responsive">
                  <table className="app-table">
                    <thead>
                      <th>
                        <div></div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:Common:NationalRequired"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:Common:CurrentlyAvailable"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:Common:SubNationalRequired"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:Common:CurrentlyAvailable"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:Common:ServiceDeliveryRequired"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:Common:CurrentlyAvailable"
                          )}
                        </div>
                      </th>
                    </thead>
                    <tbody>
                      <>
                        {Object.keys(response.equipmentAvailabilities).map(
                          (modelKeyName: string, index: number) => {
                            return (
                              <tr id={`row${modelKeyName}_${index}`}>
                                <TableCell>
                                  <TextBox
                                    className="col-form-control inputfocus"
                                    type="string"
                                    id="equipment"
                                    name="equipment"
                                    value={
                                      response.equipmentAvailabilities[index]
                                        ?.equipment
                                    }
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>
                                    ) =>
                                      onChangeOfArrayWithIndex(
                                        e,
                                        "equipmentAvailabilities",
                                        index
                                      )
                                    }
                                    placeholder={`${t(
                                      "indicators-responses:DRObjective_2_Responses:Indicator_2_4_2:Equipment"
                                    )} ${index + 1}`}
                                    error={
                                      errors[
                                      `equipmentAvailabilities[${index}].equipment`
                                      ] &&
                                      errors[
                                      `equipmentAvailabilities[${index}].equipment`
                                      ]
                                    }
                                    helperText={
                                      errors[
                                      `equipmentAvailabilities[${index}].equipment`
                                      ] &&
                                      errors[
                                      `equipmentAvailabilities[${index}].equipment`
                                      ]
                                    }
                                  />
                                </TableCell>
                                <TableCell>
                                  <TextBox
                                    className="col-form-control inputfocus"
                                    type="number"
                                    id="nationalRequired"
                                    name="nationalRequired"
                                    value={
                                      response.equipmentAvailabilities[index]
                                        ?.nationalRequired
                                    }
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>
                                    ) => {
                                      onChangeOfArrayWithIndex(
                                        e,
                                        "equipmentAvailabilities",
                                        index
                                      );
                                    }}
                                    error={
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalRequired`
                                      ] &&
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalRequired`
                                      ]
                                    }
                                    helperText={
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalRequired`
                                      ] &&
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalRequired`
                                      ]
                                    }
                                  />
                                </TableCell>
                                <TableCell>
                                  <TextBox
                                    className="col-form-control inputfocus"
                                    type="number"
                                    id="nationalCurrentlyAvailable"
                                    name="nationalCurrentlyAvailable"
                                    value={
                                      response.equipmentAvailabilities[index]
                                        ?.nationalCurrentlyAvailable
                                    }
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>
                                    ) => {
                                      onChangeOfArrayWithIndex(
                                        e,
                                        "equipmentAvailabilities",
                                        index
                                      );
                                    }}
                                    error={
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalCurrentlyAvailable`
                                      ] &&
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalCurrentlyAvailable`
                                      ]
                                    }
                                    helperText={
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalCurrentlyAvailable`
                                      ] &&
                                      errors[
                                      `equipmentAvailabilities[${index}].nationalCurrentlyAvailable`
                                      ]
                                    }
                                  />
                                </TableCell>
                                <TableCell>
                                  <TextBox
                                    className="col-form-control inputfocus"
                                    type="number"
                                    id="subNationalRequired"
                                    name="subNationalRequired"
                                    value={
                                      response.equipmentAvailabilities[index]
                                        ?.subNationalRequired
                                    }
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>
                                    ) =>
                                      onChangeOfArrayWithIndex(
                                        e,
                                        "equipmentAvailabilities",
                                        index
                                      )
                                    }
                                  />
                                </TableCell>
                                <TableCell>
                                  <TextBox
                                    className="col-form-control inputfocus"
                                    type="number"
                                    id="subNationalCurrentlyAvailable"
                                    name="subNationalCurrentlyAvailable"
                                    value={
                                      response.equipmentAvailabilities[index]
                                        ?.subNationalCurrentlyAvailable
                                    }
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>
                                    ) =>
                                      onChangeOfArrayWithIndex(
                                        e,
                                        "equipmentAvailabilities",
                                        index
                                      )
                                    }
                                  />
                                </TableCell>
                                <TableCell>
                                  <TextBox
                                    className="col-form-control inputfocus"
                                    type="number"
                                    id="serviceDeliveryRequired"
                                    name="serviceDeliveryRequired"
                                    value={
                                      response.equipmentAvailabilities[index]
                                        ?.serviceDeliveryRequired
                                    }
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>
                                    ) =>
                                      onChangeOfArrayWithIndex(
                                        e,
                                        "equipmentAvailabilities",
                                        index
                                      )
                                    }
                                  />
                                </TableCell>
                                <TableCell>
                                  <TextBox
                                    className="col-form-control inputfocus"
                                    type="number"
                                    id="serviceDeliveryCurrentlyAvailable"
                                    name="serviceDeliveryCurrentlyAvailable"
                                    value={
                                      response.equipmentAvailabilities[index]
                                        ?.serviceDeliveryCurrentlyAvailable
                                    }
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>
                                    ) =>
                                      onChangeOfArrayWithIndex(
                                        e,
                                        "equipmentAvailabilities",
                                        index
                                      )
                                    }
                                  />
                                </TableCell>
                              </tr>
                            );
                          })}
                        <tr className="text-center">
                          <td colSpan={4}>
                            {Object.keys(
                              response.equipmentAvailabilities as [
                                EquipmentAvailability
                              ]
                            ).length > 1 && (
                                <IconButton onClick={onRowDelete}>
                                  <Delete />
                                </IconButton>
                              )}
                            <IconButton onClick={onRowAdd}>
                              <Add />
                            </IconButton>
                          </td>
                        </tr>
                      </>
                    </tbody>

                    <tfoot>
                      <>
                        <tr>
                          <td>
                            {t(
                              "indicators-responses:Common:ProportionAvailable"
                            )}
                          </td>
                          <td></td>
                          <TableCell>
                            <label>
                              {calculateProportionRateProperty(
                                "nationalRequired",
                                "nationalCurrentlyAvailable"
                              )}
                            </label>
                          </TableCell>
                          <td></td>
                          <TableCell>
                            <label>
                              {calculateProportionRateProperty(
                                "subNationalRequired",
                                "subNationalCurrentlyAvailable"
                              )}
                            </label>
                          </TableCell>
                          <td></td>
                          <TableCell>
                            <label>
                              {calculateProportionRateProperty(
                                "serviceDeliveryRequired",
                                "serviceDeliveryCurrentlyAvailable"
                              )}
                            </label>
                          </TableCell>
                        </tr>
                      </>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="response-wrapper d-flex">
          <TextBox
            id="cannotBeAssessedReason"
            name="cannotBeAssessedReason"
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant="outlined"
            fullWidth
            onChange={onChange}
            value={response?.cannotBeAssessedReason || ""}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}
      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
    </>
  );
};

export default Indicator_2_4_2_Response;
