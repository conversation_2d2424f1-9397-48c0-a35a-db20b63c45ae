﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import React, { ChangeEvent, useEffect, useRef } from "react";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import TableCell from "../../../responses/TableCell";
import { Add, Delete } from "@mui/icons-material";
import { Response_1, SurveillanceStaff } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_4_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 2.4.1 response for desk review */
const Indicator_2_4_1_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t("indicators-responses:app:DR_Objective_2_Indicator_2_4_1_Title");
  const { calculateProportionRate } = useCalculation();

  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
  const validate = useFormValidation(validationRulesRef.current);

  const {
    response,
    onCannotBeAssessed,
    onChangeOfArrayWithIndex,
    onSave,
    onFinalize,
    onValueChange,
    getResponse,
    onChange,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCapture<Response_1>(
    new Response_1(false, null, null, [SurveillanceStaff.init()]),
    validate
  );

  useEffect(() => {
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

  }, [response?.cannotBeAssessed]);

  const errors = useSelector((state: any) => state.error);

  // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
  let isProportionRateValidForNationalReq: boolean = true;
  let isProportionRateValidForSubNationalReq: boolean = true;
  let isProportionRateValidForServiceDelReq: boolean = true;

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid && isProportionRateValidForNationalReq && isProportionRateValidForSubNationalReq && isProportionRateValidForServiceDelReq) {
      onFinalize();
    }
  };

  //Triggers onChange of cannotBeAssessed checkbox 
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current =
      evt.currentTarget.checked
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

    onCannotBeAssessed(evt);
  }

  // Triggered whenever 'Delete Row' button is clicked.
  const onRowDelete = () => {
    onValueChange(
      "surveillanceStaffs",
      response.surveillanceStaffs.slice(
        0, response.surveillanceStaffs.length - 1
      )
    );
  };

  // Triggered whenever 'Add Row' button is clicked.
  const onRowAdd = () => {
    onValueChange("surveillanceStaffs", [...response.surveillanceStaffs, SurveillanceStaff.init()]);
  };

  //Method creates the arrays for two different property to calculate the proportion rate
  const calculateProportionRateForProperty = (
    modelKeyNameOne: string,
    modelKeyNameTwo: string
  ) => {
    let columnOneSum: number = 0;
    let columnTwoSum: number = 0;

    Object.keys(response.surveillanceStaffs).map((key: string) => {
      columnOneSum += response.surveillanceStaffs[key][modelKeyNameOne];
      columnTwoSum += response.surveillanceStaffs[key][modelKeyNameTwo];
    });

    return calculateProportionRate(columnOneSum, columnTwoSum);
  };

  //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
  const calculateProportionRateProperty = (propertyName1: string, propertyName2: string) => {
    const proportionRateValue = calculateProportionRateForProperty(propertyName1, propertyName2);

    const proportionRateExceptionContent =
      <span className="Mui-error d-flex mb-2">
        * {t("indicators-responses:Common:ResponseProportionError")}
      </span>

    switch (propertyName1) {
      case "nationalRequired":
        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
          isProportionRateValidForNationalReq = true;
          return proportionRateValue + '%';
        }

        isProportionRateValidForNationalReq = false;
        return proportionRateExceptionContent;

      case "subNationalRequired":
        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
          isProportionRateValidForSubNationalReq = true;
          return proportionRateValue + '%';
        }

        isProportionRateValidForSubNationalReq = false;
        return proportionRateExceptionContent;

      case "serviceDeliveryRequired":
        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
          isProportionRateValidForServiceDelReq = true;
          return proportionRateValue + '%';
        }

        isProportionRateValidForServiceDelReq = false;
        return proportionRateExceptionContent;
    }

  }

  return (
    <>
      <div className="response-assess-wrapper">
        <Checkbox
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response.cannotBeAssessed}
        />
      </div>

      {!response.cannotBeAssessed ? (
        <div className="response-wrapper">
          <div className="response-content">
            <div className="row">
              <div className="col-xs-12 col-md-12">
                <p>
                  {t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:IptpWhoAndHowManyStaff"
                  )}{" "}
                </p>
                <p>
                  {t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:HowManyStaff"
                  )}{" "}
                </p>
                <p>
                  {t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:ConsiderAllStaff"
                  )}{" "}
                </p>
                <p>
                  {t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:IptpStaffRole"
                  )}
                </p>

                <div className="mt-3 table-responsive">
                  <table className="app-table">
                    <thead>
                      <th>
                        <div></div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:NationalRequired"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:CurrentlyAvailable"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:SubNationalRequired"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:CurrentlyAvailable"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:ServiceDeliveryRequired"
                          )}
                        </div>
                      </th>
                      <th>
                        <div className="fw-bold">
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:CurrentlyAvailable"
                          )}
                        </div>
                      </th>
                    </thead>
                    <tbody>
                      <>
                        {Object.keys(
                          response.surveillanceStaffs as [SurveillanceStaff]
                        ).map((modelKeyName: string, index: number) => {
                          return (
                            <tr id={`row${modelKeyName}_${index}`} className={!!Object.keys(errors).length ? "app-error" : ""}>
                              <TableCell>
                                <TextBox
                                  className="col-form-control inputfocus"
                                  type="string"
                                  id="staffRole"
                                  name="staffRole"
                                  value={
                                    response.surveillanceStaffs[index]
                                      ?.staffRole
                                  }
                                  onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                  ) =>
                                    onChangeOfArrayWithIndex(
                                      e,
                                      "surveillanceStaffs",
                                      index
                                    )
                                  }
                                  placeholder={`${t(
                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:StaffRole"
                                  )} ${index + 1}`}
                                  error={
                                    errors[
                                    `surveillanceStaffs[${index}].staffRole`
                                    ] &&
                                    errors[
                                    `surveillanceStaffs[${index}].staffRole`
                                    ]
                                  }
                                  helperText={
                                    errors[
                                    `surveillanceStaffs[${index}].staffRole`
                                    ] &&
                                    errors[
                                    `surveillanceStaffs[${index}].staffRole`
                                    ]
                                  }
                                />
                              </TableCell>
                              <TableCell>
                                <TextBox
                                  className="col-form-control inputfocus"
                                  type="number"
                                  name="nationalRequired"
                                  id="nationalRequired"
                                  value={
                                    response.surveillanceStaffs[index]
                                      ?.nationalRequired
                                  }
                                  onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                  ) => {
                                    onChangeOfArrayWithIndex(
                                      e,
                                      "surveillanceStaffs",
                                      index
                                    );
                                  }}
                                  error={
                                    errors[
                                    `surveillanceStaffs[${index}].nationalRequired`
                                    ] &&
                                    errors[
                                    `surveillanceStaffs[${index}].nationalRequired`
                                    ]
                                  }
                                  helperText={
                                    errors[
                                    `surveillanceStaffs[${index}].nationalRequired`
                                    ] &&
                                    errors[
                                    `surveillanceStaffs[${index}].nationalRequired`
                                    ]
                                  }
                                />
                              </TableCell>
                              <TableCell>
                                <TextBox
                                  className="col-form-control inputfocus"
                                  type="number"
                                  name="nationalCurrentlyAvailable"
                                  id="nationalCurrentlyAvailable"
                                  value={
                                    response.surveillanceStaffs[index]
                                      ?.nationalCurrentlyAvailable
                                  }
                                  onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                  ) => {
                                    onChangeOfArrayWithIndex(
                                      e,
                                      "surveillanceStaffs",
                                      index
                                    );
                                  }}
                                  error={
                                    errors[
                                    `surveillanceStaffs[${index}].nationalCurrentlyAvailable`
                                    ] &&
                                    errors[
                                    `surveillanceStaffs[${index}].nationalCurrentlyAvailable`
                                    ]
                                  }
                                  helperText={
                                    errors[
                                    `surveillanceStaffs[${index}].nationalCurrentlyAvailable`
                                    ] &&
                                    errors[
                                    `surveillanceStaffs[${index}].nationalCurrentlyAvailable`
                                    ]
                                  }
                                />
                              </TableCell>
                              <TableCell>
                                <TextBox
                                  className="col-form-control inputfocus"
                                  type="number"
                                  name="subNationalRequired"
                                  id="subNationalRequired"
                                  value={
                                    response.surveillanceStaffs[index]
                                      ?.subNationalRequired
                                  }
                                  onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                  ) =>
                                    onChangeOfArrayWithIndex(
                                      e,
                                      "surveillanceStaffs",
                                      index
                                    )
                                  }
                                />
                              </TableCell>
                              <TableCell>
                                <TextBox
                                  className="col-form-control inputfocus"
                                  type="number"
                                  name="subNationalCurrentlyAvailable"
                                  id="subNationalCurrentlyAvailable"
                                  value={
                                    response.surveillanceStaffs[index]
                                      ?.subNationalCurrentlyAvailable
                                  }
                                  onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                  ) =>
                                    onChangeOfArrayWithIndex(
                                      e,
                                      "surveillanceStaffs",
                                      index
                                    )
                                  }
                                />
                              </TableCell>
                              <TableCell>
                                <TextBox
                                  className="col-form-control inputfocus"
                                  type="number"
                                  name="serviceDeliveryRequired"
                                  id="serviceDeliveryRequired"
                                  value={
                                    response.surveillanceStaffs[index]
                                      ?.serviceDeliveryRequired
                                  }
                                  onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                  ) =>
                                    onChangeOfArrayWithIndex(
                                      e,
                                      "surveillanceStaffs",
                                      index
                                    )
                                  }
                                />
                              </TableCell>
                              <TableCell>
                                <TextBox
                                  name="serviceDeliveryCurrentlyAvailable"
                                  id="serviceDeliveryCurrentlyAvailable"
                                  className="col-form-control inputfocus"
                                  type="number"
                                  value={
                                    response.surveillanceStaffs[index]
                                      ?.serviceDeliveryCurrentlyAvailable
                                  }
                                  onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                  ) =>
                                    onChangeOfArrayWithIndex(
                                      e,
                                      "surveillanceStaffs",
                                      index
                                    )
                                  }
                                />
                              </TableCell>
                            </tr>
                          );
                        })}
                        <tr className="text-center">
                          <td colSpan={4}>
                            {Object.keys(
                              response.surveillanceStaffs as [SurveillanceStaff]
                            ).length > 1 && (
                                <IconButton onClick={onRowDelete}>
                                  <Delete />
                                </IconButton>
                              )}
                            <IconButton onClick={onRowAdd}>
                              <Add />
                            </IconButton>
                          </td>
                        </tr>
                      </>
                    </tbody>

                    <tfoot>
                      <>
                        <tr>
                          <td>
                            {t(
                              "indicators-responses:DRObjective_2_Responses:Indicator_2_4_1:ProportionAvailable"
                            )}
                          </td>
                          <td></td>
                          <TableCell>
                            <label>
                              {calculateProportionRateProperty(
                                "nationalRequired",
                                "nationalCurrentlyAvailable"
                              )}
                            </label>
                          </TableCell>
                          <td></td>
                          <TableCell>
                            <label>
                              {calculateProportionRateProperty(
                                "subNationalRequired",
                                "subNationalCurrentlyAvailable"
                              )}
                            </label>
                          </TableCell>
                          <td></td>
                          <TableCell>
                            <label>
                              {calculateProportionRateProperty(
                                "serviceDeliveryRequired",
                                "serviceDeliveryCurrentlyAvailable"
                              )}
                            </label>
                          </TableCell>
                        </tr>
                      </>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="response-wrapper d-flex">
          <TextBox
            id="cannotBeAssessedReason"
            name="cannotBeAssessedReason"
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant="outlined"
            fullWidth
            onChange={onChange}
            value={response?.cannotBeAssessedReason || ""}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}
      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
    </>
  );
};

export default Indicator_2_4_1_Response;