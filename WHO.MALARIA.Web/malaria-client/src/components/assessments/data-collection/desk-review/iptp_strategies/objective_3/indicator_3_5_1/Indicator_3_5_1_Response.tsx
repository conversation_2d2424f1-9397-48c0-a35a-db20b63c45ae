﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from "@mui/material";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Tooltip from "../../../../../../controls/Tooltip";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import InfoIcon from "@mui/icons-material/Info";

/** Renders the response indicator 3.5.1 */
function Indicator_3_5_1_Response() {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_3_Indicator_3_5_1_Title"
  );

  const headers = [
    { field: "", label: "" },
    {
      field: "dataCleaning",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataCleaning"
      ),
    },
    {
      field: "dataReviewMeetings",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataReviewMeetings"
      ),
    },
    {
      field: "dataQualityAssessments",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualityAssessments"
      ),
    },
    {
      field: "monitoringDataQuality",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MonitoringDataQuality"
      ),
    },
  ];

  const columns = [
    {
      field: "frequencyValidation",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:FrequencyValidation"
      ),
      placeholderFirst: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:egMonthly"
      ),
      placeholderSecond: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:egMonthly"
      ),
    },
    {
      field: "dataValidated",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataValidated"
      ),
      placeholderFirst: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MultipleDatasets"
      ),
      placeholderSecond: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:egRegisters"
      ),
    },
    {
      field: "toolsAndMethods",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ToolsAndMethods"
      ),
      placeholderFirst: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:NameOfTools"
      ),
      placeholderSecond: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualiyMeasures"
      ),
    },
    {
      field: "levelOfValidation",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:LevelOfValidation"
      ),
      placeholderFirst: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
      ),
      placeholderSecond: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
      ),
      placeholderThird: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
      ),
      placeholderFourth: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
      ),
    },
    {
      field: "personResponsible",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:PersonResponsible"
      ),
      placeholderFirst: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
      ),
      placeholderSecond: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
      ),
      placeholderThird: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
      ),
      placeholderFourth: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
      ),
    },
    {
      field: "followUpAction",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:FollowUpAction"
      ),
    },
    {
      field: "linkCopyReports",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:LinkCopyReports"
      ),
      placeholderFirst: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AuditTrails"
      ),
      placeholderSecond: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MeetingReport"
      ),
      placeholderThird: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AssessmentResults"
      ),
      placeholderFourth: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:GraphicsTables"
      ),
    },
  ];

  return (
    <>
      <div className="response-wrapper">
        <p>
          {t(
            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ResponseDesc"
          )}
        </p>
        <p className="fst-italic">
          {t(
            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ResponseTitle"
          )}
        </p>
        <div>
          <Table>
            <>
              <TableHeader
                headers={headers.map((header: any) => header.label)}
              />
              <TableBody>
                <>
                  <TableRow>
                    <>
                      <TableCell>
                        <>
                          {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualityAssurance"
                          )}
                        </>
                      </TableCell>
                      <TableCell>
                        <RadioButtonGroup
                          id="surveillancestrategies"
                          name="surveillancestrategies"
                          row
                          color="primary"
                          options={[
                            new MultiSelectModel(
                              true,
                              t("indicators-responses:Common:Yes")
                            ),
                            new MultiSelectModel(
                              false,
                              t("indicators-responses:Common:No")
                            ),
                          ]}
                          value={true}
                        />
                      </TableCell>
                      <TableCell>
                        <RadioButtonGroup
                          id="surveillancestrategies"
                          name="surveillancestrategies"
                          row
                          color="primary"
                          options={[
                            new MultiSelectModel(
                              true,
                              t("indicators-responses:Common:Yes")
                            ),
                            new MultiSelectModel(
                              false,
                              t("indicators-responses:Common:No")
                            ),
                          ]}
                          value={true}
                        />
                      </TableCell>
                      <TableCell>
                        <RadioButtonGroup
                          id="surveillancestrategies"
                          name="surveillancestrategies"
                          row
                          color="primary"
                          options={[
                            new MultiSelectModel(
                              true,
                              t("indicators-responses:Common:Yes")
                            ),
                            new MultiSelectModel(
                              false,
                              t("indicators-responses:Common:No")
                            ),
                          ]}
                          value={true}
                        />
                      </TableCell>
                      <TableCell>
                        <RadioButtonGroup
                          id="surveillancestrategies"
                          name="surveillancestrategies"
                          row
                          color="primary"
                          options={[
                            new MultiSelectModel(
                              true,
                              t("indicators-responses:Common:Yes")
                            ),
                            new MultiSelectModel(
                              false,
                              t("indicators-responses:Common:No")
                            ),
                          ]}
                          value={true}
                        />
                      </TableCell>
                    </>
                  </TableRow>
                  {columns.map((column: any, index: number) => (
                    <TableRow key={`row_${index}`}>
                      <>
                        <TableCell>
                          <>{column.label}</>
                        </TableCell>
                        <TableCell>
                          <TextBox
                            id="reason"
                            name="reason"
                            variant="outlined"
                            multiline
                            rows={3}
                            fullWidth
                            placeholder={column.placeholderFirst}
                          />
                        </TableCell>
                        <TableCell>
                          <TextBox
                            id="reason"
                            name="reason"
                            variant="outlined"
                            multiline
                            rows={3}
                            fullWidth
                            placeholder={column.placeholderSecond}
                          />
                        </TableCell>
                        <TableCell>
                          <TextBox
                            id="reason"
                            name="reason"
                            variant="outlined"
                            multiline
                            rows={3}
                            fullWidth
                            placeholder={column.placeholderThird}
                          />
                        </TableCell>
                        <TableCell>
                          <TextBox
                            id="reason"
                            name="reason"
                            variant="outlined"
                            multiline
                            rows={3}
                            fullWidth
                            placeholder={column.placeholderFourth}
                          />
                        </TableCell>
                      </>
                    </TableRow>
                  ))}
                </>
              </TableBody>
              <TableFooter>
                <>
                  <TableCell>
                    <>
                      <p>
                        {t(
                          "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ToolTipTitle"
                        )}

                        <IconButton className="grid-icon-button">
                          <Tooltip
                            content={t(
                              "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ToolTipDetails"
                            )}
                            isHtml
                          >
                            <InfoIcon fontSize="small" />
                          </Tooltip>
                        </IconButton>
                      </p>
                    </>
                  </TableCell>
                  <TableCell>
                    <span>10 %</span>
                  </TableCell>
                  <TableCell>
                    <span>20 %</span>
                  </TableCell>
                  <TableCell>
                    <span>10 %</span>
                  </TableCell>
                  <TableCell>
                    <span>20 %</span>
                  </TableCell>
                </>
              </TableFooter>
            </>
          </Table>
        </div>
      </div>

      <div className="response-action-wrapper">
        <div className="button-action-section d-flex justify-content-center p-3">
          <Button
            type="submit"
            className={classNames("btn", "app-btn-secondary")}
          >
            {t("indicators-responses:Common:Save")}
          </Button>

          <Button className={classNames("btn", "app-btn-primary")}>
            {t("indicators-responses:Common:Finalize")}
          </Button>
        </div>
      </div>
    </>
  );
}

export default Indicator_3_5_1_Response;
