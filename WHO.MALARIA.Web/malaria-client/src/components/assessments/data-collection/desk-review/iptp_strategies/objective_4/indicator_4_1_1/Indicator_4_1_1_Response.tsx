﻿import { <PERSON><PERSON>, <PERSON>con<PERSON>utton } from "@mui/material";
import React, { ChangeEvent, useEffect, useRef } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import useCalculation from "../../../responses/useCalculation";
import TableCell from "../../../responses/TableCell";
import TableFooter from "../../../responses/TableFooter";
import Checkbox from "../../../../../../controls/Checkbox";
import InfoIcon from "@mui/icons-material/Info";
import {
  Response_1,
  GoverenanceStructure,
} from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_1_1/Response_1";
import Tooltip from "../../../../../../controls/Tooltip";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 4.1.1 response for desk review */
const Indicator_4_1_1_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);
  const { calculatePercentageOfYesNo } = useCalculation();
  document.title = t(
    "indicators-responses:app:DR_Objective_4_Indicator_4_1_1_Title"
  );

  /** Excluded property that are not used in Array Map */
  const excludedProperties: Array<string> = [
    "cannotBeAssessed",
    "cannotBeAssessedReason",
  ];

  const headers = [
    {
      field: "structures",
      label: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:Structures"
      ),
    },
    {
      field: "inPlace",
      label: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:InPlace"
      ),
    },
    {
      field: "details",
      label: t(
        "indicators-responses:Common:Details"
      ),
    },
  ];

  const columns = [
    {
      field: "subnationalWorkPlan",
      label: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:SubnationalWorkPlan"
      ),
      placeholderFirstColumn: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:DocumentedDetailsPlaceholder"
      ),
    },
    {
      field: "subnationalImplementationActivities",
      label: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:SubnationalImplementationActivities"
      ),
      placeholderFirstColumn: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:DocumentedDetailsPlaceholder"
      ),
    },
    {
      field: "monitorDiseaseTrends",
      label: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:MonitorDiseaseTrends"
      ),
      placeholderFirstColumn: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:DocumentedDetailsPlaceholder"
      ),
    },
    {
      field: "otherGovernanceStructures",
      label: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:OtherGovernanceStructures"
      ),
      placeholderFirstColumn: t(
        "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:ExternalBodiesDetailsPlaceholder"
      ),
    },
  ];

  const goverenanceStructures = columns.map(
    (column: any) => new GoverenanceStructure(column.field)
  );
  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

  const validate = useFormValidation(validationRulesRef.current);

  const {
    response,
    onChange,
    onCannotBeAssessed,
    onChangeWithIndex,
    onSave,
    onFinalize,
    getResponse,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCapture<Response_1>(
    new Response_1(false, null, null, goverenanceStructures),
    validate
  );

  const errors = useSelector((state: any) => state.error);

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid) {
      onFinalize();
    }
  };

  ///Triggers onChange of cannotBeAssessed checkbox 
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current =
      evt.currentTarget.checked
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

    onCannotBeAssessed(evt);
  }

  useEffect(() => {
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

  }, [response?.cannotBeAssessed]);

  //Method creates an array of properties that have checked "Yes"
  const calculateCheckedForProperty = () => {
    const propertyArray: boolean[] = Object.keys(
      response?.goverenanceStructures
    )
      //Calculate percentage till the second last so excluding "3" element which is "otherGovernanceStructures" from propertyArray row
      .filter((key: string) => !excludedProperties.includes(key) && key !== "3")
      .map((key: string) => {
        //Property except the one at place "3" is required
        if (key !== "3") {
          return response?.goverenanceStructures[key].inPlace;
        }
      });

    return calculatePercentageOfYesNo(propertyArray);
  };

  return (
    <>
      <div className="response-assess-wrapper">
        <Checkbox
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response?.cannotBeAssessed}
        />
      </div>

      {!response.cannotBeAssessed ? (
        <div className="response-wrapper">
          <p>
            {t("indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:IPTpResponseDesc")}
          </p>
          {
            // User does not select 'Yes' or 'No' for any 'In Place' structure.
            !!Object.keys(errors).length && (
              <span className="Mui-error d-flex mb-2">
                *{" "}
                {t("indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:ResponseError")}
              </span>
            )
          }
          <Table>
            <>
              <TableHeader
                headers={headers.map((header: any) => header.label)}
              />
              <TableBody>
                <>
                  {columns.map((column: any, index: number) => (
                    <TableRow key={`column_${column.field}_${index}`}>
                      <>
                        <TableCell>
                          <>{column.label}</>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id="inPlace"
                            name="inPlace"
                            color="primary"
                            options={[
                              new MultiSelectModel(
                                true,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                false,
                                t("indicators-responses:Common:No")
                              ),
                            ]}
                            value={
                              response?.goverenanceStructures[index]?.inPlace
                            }
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => onChangeWithIndex(e,
                              "goverenanceStructures",
                              index)}
                            error={
                              errors[
                              `goverenanceStructures[${index}].inPlace`
                              ] &&
                              errors[`goverenanceStructures[${index}].inPlace`]
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <TextBox
                            id={`${column.field}_${index}_${Math.random()}`}
                            name="details"
                            placeholder={column.placeholderFirstColumn}
                            fullWidth
                            multiline
                            rows={3}
                            value={
                              response?.goverenanceStructures[index]?.details
                            }
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) =>
                              onChangeWithIndex(
                                e,
                                "goverenanceStructures",
                                index
                              )
                            }
                            error={
                              errors[
                              `goverenanceStructures[${index}].details`
                              ] &&
                              errors[`goverenanceStructures[${index}].details`]
                            }
                            helperText={
                              errors[
                              `goverenanceStructures[${index}].details`
                              ] &&
                              errors[`goverenanceStructures[${index}].details`]
                            }
                          />
                        </TableCell>
                      </>
                    </TableRow>
                  ))}
                </>
              </TableBody>
              <TableFooter>
                <>
                  <TableCell>
                    <>
                      <span>
                        {t(
                          "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:TableFooterProportions"
                        )}

                        <IconButton className="grid-icon-button">
                          <Tooltip
                            content={t(
                              "indicators-responses:DRObjective_4_Responses:Indicator_4_1_1:TableFooterProportionsTooltip"
                            )}
                            isHtml
                          >
                            <InfoIcon fontSize="small" />
                          </Tooltip>
                        </IconButton>
                      </span>
                    </>
                  </TableCell>
                  <TableCell colSpan={2}>
                    <>
                      <label>{calculateCheckedForProperty()}%</label>
                    </>
                  </TableCell>
                </>
              </TableFooter>
            </>
          </Table>
        </div>
      ) : (
        <div className="response-wrapper d-flex">
          <TextBox
            id="cannotBeAssessedReason"
            name="cannotBeAssessedReason"
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant="outlined"
            fullWidth
            value={response?.cannotBeAssessedReason}
            onChange={onChange}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}

      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

    </>
  );
};

export default Indicator_4_1_1_Response;
