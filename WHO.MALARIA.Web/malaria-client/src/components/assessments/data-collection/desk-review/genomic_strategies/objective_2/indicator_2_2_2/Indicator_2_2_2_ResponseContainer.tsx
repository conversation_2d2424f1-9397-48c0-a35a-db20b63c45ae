﻿import { useEffect, useRef, useState, ChangeEvent } from "react";
import { <PERSON><PERSON> } from "@mui/material";
import TextBox from "../../../../../../controls/TextBox";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import WHOStepper from "../../../../../../controls/WHOStepper";
import { StepperModel } from "../../../../../../../models/StepperModel";
import Indicator_2_2_2_StepA from "./Indicator_2_2_2_StepA";
import Indicator_2_2_2_StepB from "./Indicator_2_2_2_StepB";
import { useDispatch } from "react-redux";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import Response_1 from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_1";
import useIndicatorResponseCaptureForTabs from "../../../../desk-review/responses/useIndicatorResponseCaptureForTabs";
import useFormValidation from "../../../../../../common/useFormValidation";
import { ValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Response for indicator 2.2.2 */
function Indicator_2_2_2_ResponseContainer() {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_2_Indicator_2_2_2_Title"
  );
  const [currentStep, setCurrentStep] = useState<number>(0);
  const dispatch = useDispatch();
  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

  const validate = useFormValidation(validationRulesRef.current);
  const errors = useSelector((state: any) => state.error);

  const {
    response,
    updateStep_A,
    updateStep_B,
    onSave,
    onCannotBeAssessed,
    onFinalize,
    onChange,
    getResponse,
    onValueChange,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCaptureForTabs<Response_1>(
    Response_1.init(),
    validate
  );

  useEffect(() => {
    dispatch(updateStepIndex(0));
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

  }, [response?.cannotBeAssessed]);

  //Triggers onChange of cannotBeAssessed checkbox 
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current =
      evt.currentTarget.checked
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

    onCannotBeAssessed(evt);
  }

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid) {
      onFinalize();
    }
  };

  const steps: Array<StepperModel> = [
    new StepperModel(0, "A", <></>),
    new StepperModel(1, "B", <></>),
  ];

  // Triggers when step is changed
  const onStepChange = (currentStep: number) => {
    setCurrentStep(currentStep);
    dispatch(updateStepIndex(currentStep));
  };

  const renderStepComponent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Indicator_2_2_2_StepA
            step_A={response?.step_A}
            updateStep_A={updateStep_A}
          />
        );
      case 1:
        return (
          <Indicator_2_2_2_StepB
            step_B={response?.step_B}
            updateStep_B={updateStep_B}
          />
        );
    }
  };

  return (
    <>
      <div className="response-assess-wrapper">
        <Checkbox
          id="cannotBeAssessed"
          name="cannotBeAssessed"
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response?.cannotBeAssessed}
        />
      </div>
      {!response?.cannotBeAssessed ? (
        <>
          {!!Object.keys(errors).length && (
            <span className="Mui-error d-flex mb-2">
              *
              {t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseError"
              )}
            </span>
          )}
          <WHOStepper
            alternativeLabel={false}
            enableStepClick
            steps={steps}
            activeStep={currentStep}
            onStepChange={onStepChange}
          >
            <div className="p-2">{renderStepComponent()}</div>
          </WHOStepper>
        </>
      ) : (
        <div className="response-wrapper d-flex">
          <TextBox
            id="cannotBeAssessedReason"
            name="cannotBeAssessedReason"
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant="outlined"
            fullWidth
            value={response?.cannotBeAssessedReason}
            onChange={onChange}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}

      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

    </>
  );
}

export default Indicator_2_2_2_ResponseContainer;
