﻿import { But<PERSON> } from "@mui/material";
import React from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import parse from "html-react-parser";
type Indicator2_5_1Props = {};

/** Renders the indicator 2.5.1 response for desk review */
const Indicator_2_5_1_Response = (props: Indicator2_5_1Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t("indicators-responses:app:DR_Objective_1_Indicator_2_5_1_Title");

  return (
    <>
      <div className="response-wrapper">
        <p>
          {t(
            "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:ResponseDesc"
          )}
        </p>

        <div className="row mt-5">
          <div className="col-md-12">
            <TextBox
              id="subnationalAreas"
              name="subnationalAreas"
              label={parse(t("indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:SubnationalAreas"))}
              placeholder={t("indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:SubnationalAreasPlaceholder")}
              fullWidth
              multiline
              rows={10}
              InputLabelProps={{
                shrink: true,
              }}
              className="lp-text inputfocus"
            />
          </div>
        </div>
        <div className="row mt-5">
          <div className="col-md-12">
            <TextBox
              id="fundingAreas"
              name="fundingAreas"
              label={parse(t("indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:FundingAreas"))}
              placeholder={t("indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:FundingAreasPlaceholder")}
              fullWidth
              multiline
              rows={10}
              InputLabelProps={{
                shrink: true,
              }}
              className="lp-text inputfocus"
            />
          </div>
        </div>
      </div>

      <div className="response-action-wrapper">
        <div className="button-action-section d-flex justify-content-center p-3">
          <Button
            className={classNames("btn", "app-btn-secondary")}
          >
            {t("indicators-responses:Common:Save")}
          </Button>

          <Button className={classNames("btn", "app-btn-primary")}>
            {t("indicators-responses:Common:Finalize")}
          </Button>
        </div>
      </div>
    </>
  );
};

export default Indicator_2_5_1_Response;
