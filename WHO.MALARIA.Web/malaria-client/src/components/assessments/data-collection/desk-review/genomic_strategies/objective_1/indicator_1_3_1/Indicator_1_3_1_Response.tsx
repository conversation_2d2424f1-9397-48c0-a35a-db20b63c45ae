import { <PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { ChangeEvent, useRef } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import parse from "html-react-parser";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import {
    DataUseResponse,
    Response_1,
} from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_1/Response_1";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";
import { useEffect } from "react";
import ValidationRules from "./ValidationRules";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 1.3.1 */
function Indicator_1_3_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_3_1_Title");

    const headers = [
        {
            field: "dataUse",
            label: t("indicators-responses:Common:DataUse"),
        },
        {
            field: "evidence",
            label: t("indicators-responses:Common:Evidence"),
        },
        {
            field: "details",
            label: t("indicators-responses:Common:DocumentsDetails"),
        },
        {
            field: "links",
            label: t("indicators-responses:Common:Links"),
        },
    ];

    const rows = [
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:NationalStrategicPlanning"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:SubnationalStrategicPlanning"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:StratificationPrioritizationInterventions"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:MalariaPolicy"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:AdvocateForPolicy"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:MonitorProgramPerformance"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:AllocationOfResources"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:GenomicDiagnostics"),
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:GenomicTreatment"),
    ];

    const healthManagementStructure = rows.map(() => new DataUseResponse());
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithIndex,
        getResponse,
        onSave,
        onFinalize,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(healthManagementStructure), validate);

    // triggers on click of finalize buttons, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p className="fw-lighter">
                        {parse(
                            t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:GenomicResponseDesc"
                            )
                        )}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:ITPIResponseTitle"
                        )}
                    </p>
                    <p className="fw-lighter">
                        {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:DocumentsDetailsDesc")}
                    </p>

                    {/*Show error message if not all radio buttons are selected for Data quality control check in place*/}
                    {errors["healthManagementStructure[0].evidence"] && (
                        <span className="Mui-error d-flex mb-2">
                            * {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:ResponseError")}
                        </span>
                    )}

                    <div className="mt-3">
                        <Table width="100%" className="app-table">
                            <>
                                <TableHeader
                                    headers={headers.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {rows.map((row: string, index: number) => (
                                            <TableRow key={`row_${index}`}>
                                                <>
                                                    <TableCell>{row}</TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="evidence"
                                                            name="evidence"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                                new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                            ]}
                                                            value={
                                                                response?.healthManagementStructure[index]
                                                                    ?.evidence
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onChangeWithIndex(
                                                                    e,
                                                                    "healthManagementStructure",
                                                                    index
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `healthManagementStructure[${index}].evidence`
                                                                ] &&
                                                                errors[
                                                                `healthManagementStructure[${index}].evidence`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`details_${index}_${Math.random()}`}
                                                            name="details"
                                                            value={response?.healthManagementStructure[index]?.details}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onChangeWithIndex(
                                                                    e,
                                                                    "healthManagementStructure",
                                                                    index
                                                                )
                                                            }
                                                            fullWidth
                                                            error={errors[`healthManagementStructure[${index}].details`] &&
                                                                errors[`healthManagementStructure[${index}].details`]}
                                                            helperText={errors[`healthManagementStructure[${index}].details`] &&
                                                                errors[`healthManagementStructure[${index}].details`]}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`links_${index}_${Math.random()}`}
                                                            name="links"
                                                            value={response?.healthManagementStructure[index]?.links}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onChangeWithIndex(
                                                                    e,
                                                                    "healthManagementStructure",
                                                                    index
                                                                )
                                                            }
                                                            fullWidth
                                                            error={errors[`healthManagementStructure[${index}].links`] &&
                                                                errors[`healthManagementStructure[${index}].links`]}
                                                            helperText={errors[`healthManagementStructure[${index}].links`] &&
                                                                errors[`healthManagementStructure[${index}].links`]}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                    </>
                                </TableBody>
                            </>
                        </Table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                        helperText={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_1_3_1_Response;