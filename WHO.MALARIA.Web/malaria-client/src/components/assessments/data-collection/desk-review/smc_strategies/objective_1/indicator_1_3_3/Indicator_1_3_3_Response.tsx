﻿import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { ChangeEvent, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_3/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableRow from "../../../responses/TableRow";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import useCalculationProportionalRate from "../../../useCalculationProportionalRate";

/** Renders the response for indicator 1.3.3 */
function Indicator_1_3_3_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_3_3_Title");
    const { calculatePercentage } = useCalculation();
    const { calculateProportionRateProperty, isProportionRateValid } = useCalculationProportionalRate();

    //Excluded property that are not used in Array Map 
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "healthSystemLevelValidationRuleKey",
        "metNotMetStatus",
    ];

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithKey,
        getResponse,
        onSave,
        onFinalize,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);

    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid && isProportionRateValid()) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const rowsData: any = {
        nationalLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:NationalLevel"
        ),
        regionalLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:RegionalLevel"
        ),
        districtLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:DistrictLevel"
        ),
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p className="fw-lighter">
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:SMCResponseDesc"
                        )}
                    </p>
                    <p>
                        {parse(t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:SMCNoteToCompleteTheAssessment"
                        ))}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:SMCMeetingsExpected"
                        )}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:NoteToCompleteTheAssessment"
                        )}
                    </p>
                    <p>{t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:MeetingsExpectedDesc")}</p>

                    {/*Show error message if at least one of the health system level is not completed*/}
                    {errors["healthSystemLevelValidationRuleKey"] && (
                        <span className="Mui-error d-flex mb-2">
                            *
                            {t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:ResponseError"
                            )}
                        </span>
                    )}
                    <div className="mt-3">
                        <table width="60%" className="app-table">
                            <thead>
                                <th>
                                    <div className="fw-bold">
                                        {t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:HealthSystemLevel"
                                        )}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {parse(t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:IPTIMeetingsOccurred"
                                        ))}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {parse(t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:IPTIMeetingsExpected"
                                        ))}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:Rate"
                                        )}
                                    </div>
                                </th>
                            </thead>
                            <tbody>
                                <>
                                    {Object.keys(response)
                                        .filter((key) => !excludedProperties.includes(key))
                                        .map((modelKeyName: string, index: number) => (
                                            <TableRow key={`row_${modelKeyName}_${index}`}>
                                                <>
                                                    <TableCell>{rowsData[modelKeyName]}</TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="noOfMeetingsOccured"
                                                            type="number"
                                                            name="noOfMeetingsOccured"
                                                            maxLength={3}
                                                            fullWidth
                                                            inputProps={{
                                                                max: 100,
                                                                min: 0,
                                                            }}
                                                            value={
                                                                response[modelKeyName]?.noOfMeetingsOccured
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={
                                                                errors[`${modelKeyName}.noOfMeetingsOccured`] &&
                                                                errors[`${modelKeyName}.noOfMeetingsOccured`]
                                                            }
                                                            helperText={
                                                                errors[`${modelKeyName}.noOfMeetingsOccured`] &&
                                                                errors[`${modelKeyName}.noOfMeetingsOccured`]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="noOfMeetingsExpected"
                                                            type="number"
                                                            maxLength={3}
                                                            fullWidth
                                                            inputProps={{
                                                                max: 100,
                                                                min: 0,
                                                            }}
                                                            name="noOfMeetingsExpected"
                                                            value={
                                                                response[modelKeyName]?.noOfMeetingsExpected
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={
                                                                errors[
                                                                `${modelKeyName}.noOfMeetingsExpected`
                                                                ] &&
                                                                errors[`${modelKeyName}.noOfMeetingsExpected`]
                                                            }
                                                            helperText={
                                                                errors[
                                                                `${modelKeyName}.noOfMeetingsExpected`
                                                                ] &&
                                                                errors[`${modelKeyName}.noOfMeetingsExpected`]
                                                            }
                                                        />
                                                    </TableCell>

                                                    <TableCell>
                                                        <label>
                                                            {calculateProportionRateProperty(
                                                                response[modelKeyName]?.noOfMeetingsOccured,
                                                                response[modelKeyName]?.noOfMeetingsExpected,
                                                                response
                                                            )}
                                                        </label>
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                </>
                            </tbody>
                        </table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_1_3_3_Response;