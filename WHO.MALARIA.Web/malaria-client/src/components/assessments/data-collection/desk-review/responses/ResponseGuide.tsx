import React from "react";
import { ClickAwayListener, Drawer } from "@mui/material";
import CloseDrawerIcon from "@mui/icons-material/LastPage";
import OpenDrawerIcon from "@mui/icons-material/FirstPage";
import classNames from "classnames";
import classes from "./guide.module.scss";
import { useSelector, useDispatch } from "react-redux";
import { showUserGuide, setTabClick } from "../../../../../redux/ducks/user-guide-drawer";

type GuideProps = {
    children: React.ReactElement;
};

/** Renders the response guide to help users filling up the response
 * @params props: GuideProps
 */
function ResponseGuide(props: GuideProps) {
    const { children } = props;
    const dispatch = useDispatch();

    const { isUserGuideShown, isClickedOnTab } = useSelector((state: any) => state.userGuideDrawer);

    // Handles the on drawer click behavior(open/close)
    const onDrawerClick = () => {
        dispatch(showUserGuide(!isUserGuideShown));
    };

    // Handles the on click away listener for the drawer
    const onClickAway = () => {
        // Checks if user has clicked away on the screen except the steppers/tabs and checks if user guide section is open 
        if (isClickedOnTab === false && isUserGuideShown === true) {
            dispatch(showUserGuide(false));
        } else if (isClickedOnTab === true) {
            dispatch(setTabClick(false));
        }
    }

    return (
        <div className="assess-drawer-section">
            <ClickAwayListener onClickAway={() => onClickAway()}>
                <Drawer
                    variant="permanent"
                    className={classNames(classes.drawer, {
                        [classes.drawerOpen]: isUserGuideShown,
                        [classes.drawerClose]: !isUserGuideShown,
                    })}
                    classes={{
                        paper: classNames({
                            [classes.drawerOpen]: isUserGuideShown,
                            [classes.drawerClose]: !isUserGuideShown,
                        }),
                    }}
                >
                    <div>
                        <span
                            className={classNames(classes.drawer, classes.drawerToggle)}
                            onClick={onDrawerClick}
                        >
                            {isUserGuideShown ? <OpenDrawerIcon /> : <CloseDrawerIcon />}
                        </span>
                    </div>
                    <div className="drawerContent">{children}</div>
                </Drawer>
            </ClickAwayListener>
        </div>
    );
}

export default ResponseGuide;