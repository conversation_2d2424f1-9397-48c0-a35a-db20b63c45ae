﻿import React, { useState } from "react";
import { DialogAction } from "../../../../../models/Enums";
import IconButton from "@mui/material/IconButton";
import CancelIcon from "@mui/icons-material/Close";
import { useNavigate, useLocation } from "react-router-dom";
import ResponseGuide from "./ResponseGuide";
import Breadcrumbs from "./Breadcrumbs";
import useIndicatorResponseLoader from "./useIndicatorResponseLoader";
import useGuide from "./useGuide";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateStepIndex } from "../../../../../redux/ducks/indicator-guide";
import { UserAssessmentPermission } from "../../../../../models/PermissionModel";
import useAssessmentPermissions from "../../../useAssessmentPermissions";
import useConfirmationDialogOnBackButtonClick from "../../../../common/useConfirmationDialogOnBackButtonClick";

type AssessIndicatorsProps = {
  onDialogClose?: (action: DialogAction) => void;
};

/** Renders the Assess Indicators screen for desk review */
const IndicatorResponseContainer = (props: AssessIndicatorsProps) => {
  const location: any = useLocation();
  const strategyId: string = location?.state?.strategyId;
  const assessmentId = location?.state?.assessmentId;
  const { generateGuide } = useGuide(strategyId);
  const indicatorName = location?.state?.indicatorName;
  const sequence = location?.state?.sequence;
  const permissions: UserAssessmentPermission = useSelector(
    (state: any) => state.userPermission.assessment
  );
  const { renderIndicatorResponseComponent } = useIndicatorResponseLoader();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [subObjectiveName, setSubObjectiveName] = useState<string>("");
  // Called custom hook to get the assessment permissions based on the assessmentId
  useAssessmentPermissions(assessmentId);
  const { onBackButtonClick, ConfirmationDialog } =
    useConfirmationDialogOnBackButtonClick(
      "/assessment/data-collection/desk-review"
    );

  const canSaveOrFinalizeDRIndicatorResponse =
    !permissions?.canSaveOrFinalizeDRIndicatorResponse || false;

  useEffect(() => {
    setSubObjectiveName(location?.state?.subObjectiveName);
    //Clean up stuff
    return () => {
      dispatch(updateStepIndex(null));
    };
  }, [location?.state?.subObjectiveName]);

  // triggers whenever user takes an action on 'Close'
  const onClose = (evt: React.MouseEvent<HTMLButtonElement>) => {
    const state = location?.state;
    navigate("/assessment/data-collection/desk-review", {
      state: {
        ...state,
      },
    });
  };

  return (
    <>
      <section className="page-full-section page-full-assess-section">
        <div className="assess-header-section d-flex pt-2 pb-2">
          <Breadcrumbs
            parentLabel={subObjectiveName}
            label={`${sequence} ${indicatorName}`}
            onNavigate={onBackButtonClick}
          />
          <div className="ml-auto">
            <IconButton onClick={onClose} title="Close" className="close-modal">
              <CancelIcon />
            </IconButton>
          </div>
        </div>

        <ResponseGuide>
          <>{generateGuide(sequence)}</>
        </ResponseGuide>

        <div className="page-response-section">
          <fieldset
            className={
              canSaveOrFinalizeDRIndicatorResponse ? " disableContent" : ""
            }
          >
            {renderIndicatorResponseComponent(sequence, indicatorName)}
          </fieldset>
        </div>
        {ConfirmationDialog}
      </section>
    </>
  );
};

export default IndicatorResponseContainer;
