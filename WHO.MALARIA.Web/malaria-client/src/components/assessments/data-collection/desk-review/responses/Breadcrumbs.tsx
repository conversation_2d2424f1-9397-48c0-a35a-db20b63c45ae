import {
    Breadcrumbs as <PERSON><PERSON><PERSON>crum<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    Typo<PERSON>,
} from "@mui/material";
import LeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import useConfirmationDialogOnBackButtonClick from "../../../../common/useConfirmationDialogOnBackButtonClick";

type BreadcrumbsProps = {
    label: string;
    parentLabel: string;
    onNavigate: () => void;
};

/** Renders the breadcrumbs for indicator responses
 * @param props: BreadcrumbsProps
 */
function Breadcrumbs(props: BreadcrumbsProps) {
    const { label, parentLabel, onNavigate } = props;
    const { onBackButtonClick, ConfirmationDialog } = useConfirmationDialogOnBackButtonClick("/assessments");

    return (
        <>
            <IconButton onClick={onNavigate}>
                <LeftIcon />
            </IconButton>
            <MatBreadcrumbs
                aria-label="breadcrumb"
                className="breadcrumb-wrapper d-flex"
            >
                <Link className="link-text" onClick={onNavigate}>
                    {parentLabel}
                </Link>
                <Typography color="textPrimary" className="active-breadcrumb">
                    {label}
                </Typography>
            </MatBreadcrumbs>
            {ConfirmationDialog}
        </>
    );
}

export default Breadcrumbs;