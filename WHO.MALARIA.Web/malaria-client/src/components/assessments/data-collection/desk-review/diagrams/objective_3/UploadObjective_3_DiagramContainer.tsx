﻿import React, { useEffect, useState } from "react";
import IconButton from "@mui/material/IconButton";
import CancelIcon from "@mui/icons-material/Close";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import ResponseGuide from "../../responses/ResponseGuide";
import Breadcrumbs from "../../responses/Breadcrumbs";
import WHOStepper from "../../../../../controls/WHOStepper";
import { StepperModel } from "../../../../../../models/StepperModel";
import UploadDiagram_Step_A from ".././objective_3/UploadDiagram_Step_A";
import UploadDiagram_Step_B from ".././objective_3/UploadDiagram_Step_B";
import classNames from "classnames";
import { Button } from "@mui/material";
import { UtilityHelper } from "../../../../../../utils/UtilityHelper";
import DiagramUploadModel, {
  DiagramDetail,
} from "../../../../../../models/DeskReview/Objective_3/UploadDiagram";
import { assessmentService } from "../../../../../../services/assessmentService";
import useFormValidation from "../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useDispatch, useSelector } from "react-redux";
import { UserAssessmentPermission } from "../../../../../../models/PermissionModel";
import { DeskReviewAssessmentResponseStatus } from "../../../../../../models/Enums";
import useAssessmentPermissions from "../../../../useAssessmentPermissions";
import { showUserGuide } from "../../../../../../redux/ducks/user-guide-drawer";

export type DiagramProps = {
  diagramDetails: Array<DiagramDetail>;
  onFileUpload: (tabIndex: number, file: File | null) => void;
  onDiagramDetailChange: (
    tabIndex: number,
    modelKeyName: string,
    value: string
  ) => void;
};

/** Renders the Desk Review Upload Diagram Container for Objective 3 and all components underneath */
const UploadObjective_3_DiagramContainer = () => {
  const { t } = useTranslation();
  const location: any = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [diagram, setDiagram] = useState<DiagramUploadModel>(
    DiagramUploadModel.init([
      new DiagramDetail("", null, "", null, null, null, 0),
      new DiagramDetail("", null, "", null, null, null, 1),
    ])
  );
  const validate = useFormValidation(ValidationRules);
  const userPermission: UserAssessmentPermission = useSelector(
    (state: any) => state.userPermission.assessment
  );
  const errors = useSelector((state: any) => state.error);

  //WHO Steppers
  const [currentStep, setCurrentStep] = useState<number>(0);

  document.title = t("app.UploadDiagramTitle");
  const assessmentId: string = location?.state?.assessmentId;
  const strategyId: string = location?.state?.strategyId;
  const howToAssess: string = t(
    "Assessment.DataCollection.DeskReviewDiagram.howToAssess"
  );
  const whatYouNeed: string = t(
    "Assessment.DataCollection.DeskReviewDiagram.whatYouNeed"
  );
  // Called custom hook to get the assessment permissions based on the assessmentId
  useAssessmentPermissions(assessmentId);

  useEffect(() => {
    assessmentService
      .getObjectiveDiagrams(assessmentId, strategyId)
      .then((data: DiagramUploadModel) => {
        const diagramDetails = [...diagram.diagramDetails];

        if (data.diagramDetails) {
          for (const detail of data.diagramDetails) {
            diagramDetails[detail.order - 1] = detail;
          }
        }

        const _diagram: DiagramUploadModel = {
          dataAccess: data.dataAccess,
          dataAnalysis: data.dataAnalysis,
          dataQualityAssurance: data.dataQualityAssurance,
          dataRecording: data.dataRecording,
          dataReporting: data.dataReporting,
          status: data.status,
          diagramDetails,
        };

        setDiagram(_diagram);
      });
  }, []);

  //Triggers on file uplaod and update the state with a uploaded diagram
  const onFileUpload = (index: number, file: File | null) => {
    const diagramDetails = [...diagram.diagramDetails];
    diagramDetails[index].file = file;
    setDiagram((prevState: DiagramUploadModel) => ({
      ...prevState,
      diagramDetails,
    }));
  };

  //Triggers on other diagram details controls and updates the state
  const onDiagramDetailChange = (
    index: number,
    modelKeyName: string,
    value: string
  ) => {
    const diagramDetails = [...diagram.diagramDetails];
    diagramDetails[index][modelKeyName] = value;
    setDiagram((prevState: DiagramUploadModel) => ({
      ...prevState,
      diagramDetails,
    }));
  };

  //Triggers on step B's controls value change.
  const onStepBValueChange = (propertyName: string, value: string) =>
    setDiagram((prevState: DiagramUploadModel) => ({
      ...prevState,
      [propertyName]: value,
    }));

  //Create form data object and assigns values to the keys from the state.
  const createFormData = (status: number): FormData => {
    const formData = new FormData();
    formData.append("status", String(status));
    formData.append("assessmentId", assessmentId);
    formData.append("strategyId", strategyId);
    formData.append("dataAccess", diagram.dataAccess || "");
    formData.append("dataQualityAssurance", diagram.dataQualityAssurance || "");
    formData.append("dataRecording", diagram.dataRecording || "");
    formData.append("dataReporting", diagram.dataReporting || "");
    formData.append("dataAnalysis", diagram.dataAnalysis || "");

    if (diagram.diagramDetails) {
      for (let index = 0; index < diagram.diagramDetails.length; index++) {
        formData.append(
          `diagramDetails[${index}].id`,
          diagram.diagramDetails[index].id
        );
        formData.append(
          `diagramDetails[${index}].file`,
          diagram.diagramDetails[index].file || ""
        );
        formData.append(
          `diagramDetails[${index}].dataFlow`,
          diagram.diagramDetails[index].dataFlow || ""
        );
        formData.append(
          `diagramDetails[${index}].plannedChanges`,
          diagram.diagramDetails[index].plannedChanges || ""
        );
        formData.append(
          `diagramDetails[${index}].modifyingProcess`,
          diagram.diagramDetails[index].modifyingProcess || ""
        );
      }
    }

    return formData;
  };

  //Trigers on save of the diagrams
  const onSave = (status: number) => {
    const isFormValid = validate(diagram);
    if (isFormValid) {
      assessmentService
        .uploadObjectiveDiagram(createFormData(status))
        .then((diagramFileIds: Array<string>) => {
          if (diagramFileIds) {
            const diagramDetails = [...diagram.diagramDetails];

            for (let index = 0; index < diagramFileIds.length; index++) {
              diagramDetails[index].id = diagramFileIds[index] || "";
            }

            setDiagram((prevState: DiagramUploadModel) => ({
              ...prevState,
              ...diagramDetails,
              status: status,
            }));
          }
        });
    }
  };

  // Handles the click event of breadcrumb
  const onNavigate = () => {
    const state = location?.state;
    navigate("/assessment/data-collection/desk-review", { state });
    dispatch(showUserGuide(true));
  };

  // triggers whenever user takes an action on 'Close'
  const onClose = () => {
    const state = location?.state;
    navigate("/assessment/data-collection/desk-review", { state });
  };

  //Steps for WHO Stepper
  const steps: Array<StepperModel> = [
    new StepperModel(0, "A"),
    new StepperModel(1, "B"),
  ];

  // Triggers when step is changed
  const onStepChange = (currentStep: number) => {
    setCurrentStep(currentStep);
  };

  // renders upload step components
  const renderStepComponent = () => {
    switch (currentStep) {
      case 0:
        return (
          <UploadDiagram_Step_A
            diagramDetails={diagram.diagramDetails}
            onDiagramDetailChange={onDiagramDetailChange}
            onFileUpload={onFileUpload}
          />
        );
      case 1:
        return (
          <UploadDiagram_Step_B
            dataAccess={diagram.dataAccess}
            dataAnalysis={diagram.dataAnalysis}
            dataQualityAssurance={diagram.dataQualityAssurance}
            dataRecording={diagram.dataRecording}
            dataReporting={diagram.dataReporting}
            onValueChange={onStepBValueChange}
          />
        );
    }
  };

  return (
    <>
      <section className="page-full-section page-full-assess-section">
        <div className="assess-header-section d-flex pt-2 pb-2">
          <Breadcrumbs
            parentLabel={t(
              "Assessment.DataCollection.DeskReviewDiagram.TechnicalProcess"
            )}
            label={t(
              "Assessment.DataCollection.DeskReviewDiagram.UploadDiagram"
            )}
            onNavigate={onNavigate}
          />

          <div className="ml-auto">
            <IconButton onClick={onClose} title="Close" className="close-modal">
              <CancelIcon />
            </IconButton>
          </div>
        </div>

        <ResponseGuide>
          {UtilityHelper.getHowToAssessGuidelines(howToAssess, "", whatYouNeed)}
        </ResponseGuide>

        <div className="page-response-section">
          <>
            <div className="response-assess-wrapper h-45"></div>
            {!!Object.keys(errors).length && (
              <span className="Mui-error d-flex mb-2">
                *{t("Exception.AtleastOneDiagramIsRequired")}
              </span>
            )}
            <WHOStepper
              enableStepClick
              steps={steps}
              activeStep={currentStep}
              alternativeLabel={false}
              nonLinear={true}
              onStepChange={onStepChange}
            >
              <>{renderStepComponent()}</>
            </WHOStepper>
          </>

          {userPermission?.canSaveOrFinalizeDRIndicatorResponse && (
            <div className="response-action-wrapper">
              <div className="button-action-section d-flex justify-content-center p-3">
                {diagram.status !=
                  DeskReviewAssessmentResponseStatus.Completed && (
                  <Button
                    className={classNames("btn", "app-btn-secondary")}
                    onClick={() =>
                      onSave(DeskReviewAssessmentResponseStatus.InProgress)
                    }
                  >
                    {t("indicators-responses:Common:Save")}
                  </Button>
                )}
                <Button
                  className={classNames("btn", "app-btn-primary")}
                  onClick={() =>
                    onSave(DeskReviewAssessmentResponseStatus.Completed)
                  }
                >
                  {t("indicators-responses:Common:Finalize")}
                </Button>
              </div>
            </div>
          )}
        </div>
      </section>
    </>
  );
};

export default UploadObjective_3_DiagramContainer;
