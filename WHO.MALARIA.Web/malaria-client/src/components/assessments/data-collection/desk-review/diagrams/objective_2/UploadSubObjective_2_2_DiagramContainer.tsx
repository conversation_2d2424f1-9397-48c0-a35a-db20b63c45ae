﻿import React, { useEffect, useState } from "react";
import {
  DeskReviewAssessmentResponseStatus,
  DialogAction,
} from "../../../../../../models/Enums";
import IconButton from "@mui/material/IconButton";
import CancelIcon from "@mui/icons-material/Close";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import ResponseGuide from "../../responses/ResponseGuide";
import Breadcrumbs from "../../responses/Breadcrumbs";
import classNames from "classnames";
import { Button } from "@mui/material";
import { UtilityHelper } from "../../../../../../utils/UtilityHelper";
import UploadDiagramTab from "./UploadDiagramTab";
import DiagramUploadModel, {
  DiagramFile,
} from "../../../../../../models/DeskReview/Objective_2/DiagramUploadModel";
import useFormValidation from "../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useDispatch, useSelector } from "react-redux";
import { UserAssessmentPermission } from "../../../../../../models/PermissionModel";
import { assessmentService } from "../../../../../../services/assessmentService";
import useAssessmentPermissions from "../../../../useAssessmentPermissions";
import { showUserGuide } from "../../../../../../redux/ducks/user-guide-drawer";

/** Renders the Desk Review Upload Diagram Container for Objective 2_2 and all components underneath */
const UploadSubObjective_2_2_DiagramContainer = () => {
  const { t } = useTranslation();
  document.title = t("app.UploadDiagramTitle");
  const location: any = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const assessmentId = location?.state?.assessmentId;
  const strategyId: string = location?.state?.strategyId;
  const howToAssess = t(
    "Assessment.DataCollection.DeskReviewObjetive2Diagram.howToAssess"
  );
  const whatYouNeed = t(
    "Assessment.DataCollection.DeskReviewObjetive2Diagram.whatYouNeed"
  );
  // Called custom hook to get the assessment permissions based on the assessmentId
  useAssessmentPermissions(assessmentId);

  const [diagram, setDiagram] = useState<DiagramUploadModel>(
    DiagramUploadModel.init([
      new DiagramFile(
        "",
        null,
        1,
        DeskReviewAssessmentResponseStatus.InProgress
      ),
      new DiagramFile(
        "",
        null,
        2,
        DeskReviewAssessmentResponseStatus.InProgress
      ),
    ])
  );
  const validate = useFormValidation(ValidationRules);
  const userPermission: UserAssessmentPermission = useSelector(
    (state: any) => state.userPermission.assessment
  );
  const errors = useSelector((state: any) => state.error);

  useEffect(() => {
    assessmentService
      .getSubObjectiveDiagrams(assessmentId, strategyId)
      .then((diagrams: Array<DiagramFile>) => {
        if (diagrams) {
          const diagramFiles = [...diagram.diagramFiles];
          let status: number = DeskReviewAssessmentResponseStatus.InProgress;
          for (const diagram of diagrams) {
            diagramFiles[diagram.order - 1] = diagram;
            if (
              diagram.status == DeskReviewAssessmentResponseStatus.Completed
            ) {
              status = diagram.status;
            }
          }

          setDiagram((prevState: DiagramUploadModel) => ({
            ...prevState,
            diagramFiles,
            status: status,
          }));
        }
      });
  }, []);

  //Triggers on file uplaod and update the state with a uploaded diagram
  const onFileUpload = (tabIndex: number, file: File | null) => {
    const diagramDetails = [...diagram.diagramFiles];
    diagramDetails[tabIndex].file = file;
    setDiagram((prevState: DiagramUploadModel) => ({
      ...prevState,
      diagramDetails,
    }));
  };

  //Create form data object and assigns values to the keys from the state.
  const createFormData = (status: number): FormData => {
    const formData = new FormData();

    formData.append("status", String(status));
    formData.append("assessmentId", assessmentId);
    formData.append("strategyId", strategyId);

    if (diagram.diagramFiles) {
      for (let index = 0; index < diagram.diagramFiles.length; index++) {
        formData.append(
          `diagramFiles[${index}].id`,
          diagram.diagramFiles[index].id
        );
        formData.append(
          `diagramFiles[${index}].file`,
          diagram.diagramFiles[index].file || ""
        );
        formData.append(
          `diagramFiles[${index}].order`,
          diagram.diagramFiles[index].order.toString() || ""
        );
      }
    }

    return formData;
  };

  //Trigers on save of the diagrams
  const onSave = (status: number) => {
    const isFormValid = validate(diagram);
    if (isFormValid) {
      assessmentService
        .uploadSubObjectiveDiagram(createFormData(status))
        .then((diagramFileIds: Array<string>) => {
          if (diagramFileIds) {
            const diagramFiles = [...diagram.diagramFiles];

            for (let index = 0; index < diagramFileIds.length; index++) {
              diagramFiles[index].id = diagramFileIds[index] || "";
            }
            setDiagram((prevState: DiagramUploadModel) => ({
              ...prevState,
              ...diagramFiles,
              status: status,
            }));
          }
        });
    }
  };

  // Handles the click event of breadcrumb
  const onNavigate = () => {
    const state = location?.state;
    navigate("/assessment/data-collection/desk-review", { state });
    dispatch(showUserGuide(true));
  };

  // triggers whenever user takes an action on 'Close'
  const onClose = (evt: React.MouseEvent<HTMLButtonElement>) => {
    const state = location?.state;
    navigate("/assessment/data-collection/desk-review", { state });
  };

  return (
    <>
      <section className="page-full-section page-full-assess-section">
        <div className="assess-header-section d-flex pt-2 pb-2">
          <Breadcrumbs
            parentLabel={t(
              "Assessment.DataCollection.DeskReviewDiagram.InformationSystem"
            )}
            label={t(
              "Assessment.DataCollection.DeskReviewDiagram.UploadDiagram"
            )}
            onNavigate={onNavigate}
          />

          <div className="ml-auto">
            <IconButton onClick={onClose} title="Close" className="close-modal">
              <CancelIcon />
            </IconButton>
          </div>
        </div>

        <ResponseGuide>
          {UtilityHelper.getHowToAssessGuidelines(howToAssess, "", whatYouNeed)}
        </ResponseGuide>

        <div className="page-response-section">
          {!!Object.keys(errors).length && (
            <span className="Mui-error d-flex mb-2">
              *{t("Exception.AtleastOneDiagramIsRequired")}
            </span>
          )}

          <UploadDiagramTab
            onFileUpload={onFileUpload}
            diagramFiles={diagram.diagramFiles}
          />

          {userPermission?.canSaveOrFinalizeDRIndicatorResponse && (
            <div className="response-action-wrapper">
              <div className="button-action-section d-flex justify-content-center p-3">
                {diagram.status !=
                  DeskReviewAssessmentResponseStatus.Completed && (
                  <Button
                    className={classNames("btn", "app-btn-secondary")}
                    onClick={() =>
                      onSave(DeskReviewAssessmentResponseStatus.InProgress)
                    }
                  >
                    {t("indicators-responses:Common:Save")}
                  </Button>
                )}
                <Button
                  className={classNames("btn", "app-btn-primary")}
                  onClick={() =>
                    onSave(DeskReviewAssessmentResponseStatus.Completed)
                  }
                >
                  {t("indicators-responses:Common:Finalize")}
                </Button>
              </div>
            </div>
          )}
        </div>
      </section>
    </>
  );
};

export default UploadSubObjective_2_2_DiagramContainer;
