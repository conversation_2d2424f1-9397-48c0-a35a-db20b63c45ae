import { IconButton } from "@mui/material";
import { useTranslation } from "react-i18next";
import InfoIcon from "@mui/icons-material/Info";
import Tooltip from "../../../controls/Tooltip";
import { MetNotMetEnum } from "../../../../models/Enums";

interface MetNotMetStatusProps{
    status:string | null;
    tooltip:string;
}
export const MetNotMetStatus = (props:MetNotMetStatusProps) => {
  const { t } = useTranslation(["indicators-responses"]);

  return (
    <>
      <div className="response-status-wrapper">
        <span
          className={
            props.status === MetNotMetEnum.Met
              ? "text-success"
              : props.status === MetNotMetEnum.PartiallyMet
              ? "text-warning"
              : "text-danger"
          }
        >
          <b>
            {props.status === MetNotMetEnum.Met
              ? t("indicators-responses:Common:Met")
              : props.status === MetNotMetEnum.PartiallyMet
              ? t("indicators-responses:Common:PartiallyMet")
              : t("indicators-responses:Common:NotMet")}
          </b>
        </span>
        <IconButton className="grid-icon-button">
          <Tooltip
            content={props.tooltip}
            isHtml
          >
            <InfoIcon fontSize="default" />
          </Tooltip>
        </IconButton>
      </div>
    </>
  );
};
