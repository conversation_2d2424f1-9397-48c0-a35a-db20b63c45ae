﻿import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableRow from "../../../responses/TableRow";
import { Step_B_Response } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_1";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router";
import { TableFooter } from "@mui/material";
import { useSelector } from "react-redux";

type Indicator_2_1_4_Props = {
    step_b: Step_B_Response;
    updateStep_B: (step_b: any) => void;
    onChange: React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
    calProportionOfCaseClassfnDefnTimeFrameValues: () => number;
    getMetNotMetStatus: () => void;
};

/** Renders the indicator 2_1_4 Step B response for elimination strategy for desk review */
const Indicator_2_1_4_B = (props: Indicator_2_1_4_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const {
        step_b,
        updateStep_B,
        calProportionOfCaseClassfnDefnTimeFrameValues, getMetNotMetStatus
    } = props;
    const errors = useSelector((state: any) => state.error);

    // Triggered whenever the textbox control values are changed and update response
    const onValueChange = (
        fieldName: string,
        value: string | boolean,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_b,
            [modelKeyName]: {
                ...step_b[modelKeyName],
                [fieldName]: value,
            },
        };
        updateStep_B(_response);
    };

    // Excluded property that are not used in Array Map
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "numberOfCriteria",
    ];

    const headers = [
        { field: "", label: "" },
        { field: "", label: "" },
        {
            field: "timeframe",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:Timeframe"
            ),
        },
        { field: "", label: "" },
    ];

    const headerSecondary = [
        { field: "", label: "" },
        {
            field: "nationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:NationalLevel"
            ),
        },
        {
            field: "subNationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:SubnationalLevel"
            ),
        },
        {
            field: "timeframeOk",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:TimeframeOk"
            ),
        },
    ];

    const columns: any = {
        notification: {
            field: "notification",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:Notification"
            ),
        },
        caseInvestigation: {
            field: "caseInvestigation",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CaseInvestigation"
            ),
        },
        caseClassification: {
            field: "caseClassification ",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CaseClassification"
            ),
        },
        fociInvestigation: {
            field: "fociInvestigation",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:FociInvestigation"
            ),
        },
        response: {
            field: "response",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:Response"
            ),
        },
    }

    return (
        <>
            <div className="response-wrapper">
                <div className="response-content">
                    <div className="row">
                        <div className="col-xs-12 col-md-12">
                            <p>
                                {t(
                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ListTheMandate"
                                )}
                            </p>

                            <Table>
                                <>
                                    <TableHeader
                                        headers={headers.map((header: any) => header.label)}
                                    />
                                    <TableHeader
                                        headers={headerSecondary.map((header: any) => header.label)}
                                    />
                                    <TableBody>
                                        <>
                                            {Object.keys(step_b)
                                                .filter((key) => !excludedProperties.includes(key))
                                                .map((modelKeyName: string, index: number) => (
                                                    <>
                                                        <TableRow key={`row_${columns.field}_${index}`}>
                                                            <>
                                                                <TableCell>
                                                                    <span>{columns[modelKeyName]?.label}</span>
                                                                </TableCell>
                                                                <TableCell>
                                                                    <TextBox
                                                                        id={`${columns.field}_${index}`}
                                                                        name="national"
                                                                        fullWidth
                                                                        value={step_b[modelKeyName]?.national}
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                        ) =>
                                                                            onValueChange(
                                                                                e.target.name,
                                                                                e.currentTarget.value,
                                                                                modelKeyName
                                                                            )
                                                                        }
                                                                        error={
                                                                            errors[
                                                                            `step_B.${modelKeyName}.national`
                                                                            ] &&
                                                                            errors[
                                                                            `step_B.${modelKeyName}.national`
                                                                            ]
                                                                        }
                                                                        helperText={
                                                                            errors[
                                                                            `step_B.${modelKeyName}.national`
                                                                            ] &&
                                                                            errors[
                                                                            `step_B.${modelKeyName}.national`
                                                                            ]
                                                                        }
                                                                    />
                                                                </TableCell>
                                                                <TableCell>
                                                                    <TextBox
                                                                        id={`${columns.field}_${index}`}
                                                                        name="subNational"
                                                                        fullWidth
                                                                        value={step_b[modelKeyName]?.subNational}
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                        ) =>
                                                                            onValueChange(
                                                                                e.target.name,
                                                                                e.currentTarget.value,
                                                                                modelKeyName
                                                                            )
                                                                        }
                                                                        error={
                                                                            errors[
                                                                            `step_B.${modelKeyName}.subNational`
                                                                            ] &&
                                                                            errors[
                                                                            `step_B.${modelKeyName}.subNational`
                                                                            ]
                                                                        }
                                                                        helperText={
                                                                            errors[
                                                                            `step_B.${modelKeyName}.subNational`
                                                                            ] &&
                                                                            errors[
                                                                            `step_B.${modelKeyName}.subNational`
                                                                            ]
                                                                        }
                                                                    />
                                                                </TableCell>
                                                                <TableCell>
                                                                    <RadioButtonGroup
                                                                        id="timeframeOk"
                                                                        name="timeframeOk"
                                                                        row
                                                                        color="primary"
                                                                        options={[
                                                                            new MultiSelectModel(
                                                                                true,
                                                                                t("indicators-responses:Common:Yes")
                                                                            ),
                                                                            new MultiSelectModel(
                                                                                false,
                                                                                t("indicators-responses:Common:No")
                                                                            ),
                                                                        ]}
                                                                        value={step_b[modelKeyName]?.timeframeOk}
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                        ) => {
                                                                            onValueChange(
                                                                                e.target.name,
                                                                                e.currentTarget.value === "false"
                                                                                    ? false
                                                                                    : true,
                                                                                modelKeyName
                                                                            );
                                                                            getMetNotMetStatus();
                                                                        }
                                                                        }
                                                                        error={
                                                                            errors[
                                                                            `step_B.${modelKeyName}.timeframeOk`
                                                                            ] &&
                                                                            errors[
                                                                            `step_B.${modelKeyName}.timeframeOk`
                                                                            ]
                                                                        }
                                                                        helperText={
                                                                            errors[
                                                                            `step_B.${modelKeyName}.timeframeOk`
                                                                            ] &&
                                                                            errors[
                                                                            `step_B.${modelKeyName}.timeframeOk`
                                                                            ]
                                                                        }
                                                                    />
                                                                </TableCell>
                                                            </>
                                                        </TableRow>
                                                    </>
                                                ))}
                                        </>
                                    </TableBody>
                                    <TableFooter>
                                        <>
                                            <TableCell>
                                                <>
                                                    {t(
                                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ProportionCriteriaMet"
                                                    )}
                                                </>
                                            </TableCell>
                                            <TableCell colSpan={4}>
                                                <label>
                                                    {calProportionOfCaseClassfnDefnTimeFrameValues()}%
                                                </label>
                                            </TableCell>
                                        </>
                                    </TableFooter>
                                </>
                            </Table>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Indicator_2_1_4_B;