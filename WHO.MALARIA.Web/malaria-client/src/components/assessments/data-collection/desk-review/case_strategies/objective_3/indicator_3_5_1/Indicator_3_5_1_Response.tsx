﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "@mui/material";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Tooltip from "../../../../../../controls/Tooltip";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import InfoIcon from "@mui/icons-material/Info";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_5_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { useEffect, useRef } from "react";
import parse from "html-react-parser";
import ValidationRules from "./ValidationRules";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import useCalculation from "../../../responses/useCalculation";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";

/** Renders the response indicator 3.5.1 */
function Indicator_3_5_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculatePercentageOfYesNo } = useCalculation();
    document.title = t(
        "indicators-responses:app:DR_Objective_3_Indicator_3_5_1_Title"
    );

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const headers = [
        {
            field: "",
            label: "",
        },
        {
            field: "dataCleaning",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataCleaning"
            ),
        },
        {
            field: "dataReviewMeetings",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataReviewMeetings"
            ),
        },
        {
            field: "dataQualityAssessments",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualityAssessments"
            ),
        },
        {
            field: "monitoringDataQuality",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MonitoringDataQuality"
            ),
        },
    ];

    const columns: any = {
        dataValidated: {
            field: "dataValidated",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataValidated"
            ),
            placeholderFirst: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MultipleDatasets"
            ),
            placeholderSecond: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:egRegisters"
            ),
        },
        toolsAndMethods: {
            field: "toolsAndMethods",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ToolsAndMethods"
            ),
            placeholderFirst: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:NameOfTools"
            ),
            placeholderSecond: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualiyMeasures"
            ),
        },
        validationLevel: {
            field: "levelOfValidation",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:LevelOfValidation"
            ),
            placeholderFirst: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
            ),
            placeholderSecond: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
            ),
            placeholderThird: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
            ),
            placeholderFourth: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AdminLevel"
            ),
        },
        personResponsible: {
            field: "personResponsible",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:PersonResponsible"
            ),
            placeholderFirst: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
            ),
            placeholderSecond: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
            ),
            placeholderThird: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
            ),
            placeholderFourth: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DistrictLead"
            ),
        },
        followUpAction: {
            field: "followUpAction",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:FollowUpAction"
            ),
        },
        outputMaterialCopyLink: {
            field: "linkCopyReports",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:LinkCopyReports"
            ),
            placeholderFirst: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AuditTrails"
            ),
            placeholderSecond: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MeetingReport"
            ),
            placeholderThird: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:AssessmentResults"
            ),
            placeholderFourth: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:GraphicsTables"
            ),
        },
    };

    const excludedProperties: Array<string> = [
        "dataQualityAssuranceProcedureInPlace",
        "metNotMetStatus",
    ];

    const {
        response,
        onChangeWithKey,
        onSave,
        onFinalize,
        getResponse,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init());

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    //Method creates an array of properties that have checked "Yes"
    const calculateDataQualityAssuranceProcedureInPlacePercentage = () => {
        const propertyArray: boolean[] = Object.values(
            response.dataQualityAssuranceProcedureInPlace
        );

        return calculatePercentageOfYesNo(propertyArray);
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {parse(
                        t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ResponseDesc"
                        )
                    )}
                </p>
                <p className="fst-italic">
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ResponseTitle"
                    )}
                </p>

                <div>
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    <TableRow>
                                        <>
                                            <TableCell>
                                                <>
                                                    {t(
                                                        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualityAssurance"
                                                    )}
                                                </>
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="dataCleaning"
                                                    name="dataCleaning"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={
                                                        response["dataQualityAssuranceProcedureInPlace"]
                                                            ?.dataCleaning
                                                    }
                                                    onChange={(
                                                        e: React.ChangeEvent<HTMLInputElement>
                                                    ) => {
                                                        onChangeWithKey(
                                                            e,
                                                            "dataQualityAssuranceProcedureInPlace"
                                                        );
                                                    }}
                                                    error={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataCleaning'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataCleaning']
                                                    }
                                                    helperText={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataCleaning'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataCleaning']
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="dataReviewMeetings"
                                                    name="dataReviewMeetings"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={
                                                        response["dataQualityAssuranceProcedureInPlace"]
                                                            ?.dataReviewMeetings
                                                    }
                                                    onChange={(
                                                        e: React.ChangeEvent<HTMLInputElement>
                                                    ) => {
                                                        onChangeWithKey(
                                                            e,
                                                            "dataQualityAssuranceProcedureInPlace"
                                                        );
                                                    }}
                                                    error={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataReviewMeetings'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataReviewMeetings']
                                                    }
                                                    helperText={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataReviewMeetings'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataReviewMeetings']
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="dataQualityAssessments"
                                                    name="dataQualityAssessments"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={
                                                        response["dataQualityAssuranceProcedureInPlace"]
                                                            ?.dataQualityAssessments
                                                    }
                                                    onChange={(
                                                        e: React.ChangeEvent<HTMLInputElement>
                                                    ) => {
                                                        onChangeWithKey(
                                                            e,
                                                            "dataQualityAssuranceProcedureInPlace"
                                                        );
                                                    }}
                                                    error={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityAssessments'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityAssessments']
                                                    }
                                                    helperText={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityAssessments'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityAssessments']
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="dataQualityIndicators"
                                                    name="dataQualityIndicators"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={
                                                        response["dataQualityAssuranceProcedureInPlace"]
                                                            ?.dataQualityIndicators
                                                    }
                                                    onChange={(
                                                        e: React.ChangeEvent<HTMLInputElement>
                                                    ) => {
                                                        onChangeWithKey(
                                                            e,
                                                            "dataQualityAssuranceProcedureInPlace"
                                                        );
                                                    }}
                                                    error={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityIndicators'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityIndicators']
                                                    }
                                                    helperText={
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityIndicators'] &&
                                                        errors['dataQualityAssuranceProcedureInPlace.dataQualityIndicators']
                                                    }
                                                />
                                            </TableCell>
                                        </>
                                    </TableRow>

                                    {Object.keys(response)
                                        .filter((key) => !excludedProperties.includes(key))
                                        .map((row: string, index: any) => (
                                            <TableRow key={`row_${columns.field}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{columns[row].label}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="dataCleaning"
                                                            variant="outlined"
                                                            multiline
                                                            rows={3}
                                                            fullWidth
                                                            placeholder={columns[row].placeholderFirst}
                                                            value={response[row]?.dataCleaning}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, row)}
                                                            error={
                                                                errors[`${row}.dataCleaning`] &&
                                                                errors[`${row}.dataCleaning`]
                                                            }
                                                            helperText={
                                                                errors[`${row}.dataCleaning`] &&
                                                                errors[`${row}.dataCleaning`]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="dataReviewMeetings"
                                                            variant="outlined"
                                                            multiline
                                                            rows={3}
                                                            fullWidth
                                                            placeholder={columns[row].placeholderSecond}
                                                            value={response[row]?.dataReviewMeetings}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, row)}
                                                            error={
                                                                errors[`${row}.dataReviewMeetings`] &&
                                                                errors[`${row}.dataReviewMeetings`]
                                                            }
                                                            helperText={
                                                                errors[`${row}.dataReviewMeetings`] &&
                                                                errors[`${row}.dataReviewMeetings`]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="dataQualityAssessments"
                                                            variant="outlined"
                                                            multiline
                                                            rows={3}
                                                            fullWidth
                                                            placeholder={columns[row].placeholderThird}
                                                            value={response[row]?.dataQualityAssessments}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, row)}
                                                            error={
                                                                errors[`${row}.dataQualityAssessments`] &&
                                                                errors[`${row}.dataQualityAssessments`]
                                                            }
                                                            helperText={
                                                                errors[`${row}.dataQualityAssessments`] &&
                                                                errors[`${row}.dataQualityAssessments`]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="dataQualityIndicators"
                                                            variant="outlined"
                                                            multiline
                                                            rows={3}
                                                            fullWidth
                                                            placeholder={columns[row].placeholderFourth}
                                                            value={response[row]?.dataQualityIndicators}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, row)}
                                                            error={
                                                                errors[`${row}.dataQualityIndicators`] &&
                                                                errors[`${row}.dataQualityIndicators`]
                                                            }
                                                            helperText={
                                                                errors[`${row}.dataQualityIndicators`] &&
                                                                errors[`${row}.dataQualityIndicators`]
                                                            }
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                </>
                            </TableBody>
                            <TableFooter>
                                <>
                                    <TableCell>
                                        <>
                                            {t(
                                                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ToolTipTitle"
                                            )}

                                            <IconButton className="grid-icon-button">
                                                <Tooltip
                                                    content={t(
                                                        "indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:ToolTipDetails"
                                                    )}
                                                    isHtml
                                                >
                                                    <InfoIcon fontSize="small" />
                                                </Tooltip>
                                            </IconButton>
                                        </>
                                    </TableCell>
                                    <TableCell colSpan={4}>
                                        <label>
                                            {calculateDataQualityAssuranceProcedureInPlacePercentage()}
                                            %
                                        </label>
                                    </TableCell>
                                </>
                            </TableFooter>
                        </>
                    </Table>
                </div>
            </div>
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_5_1_Response;