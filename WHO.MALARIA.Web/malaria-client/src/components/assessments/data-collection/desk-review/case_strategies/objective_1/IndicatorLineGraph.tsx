﻿import React, { ChangeEvent, useState, useEffect } from "react";
import <PERSON><PERSON><PERSON> from "../../../../../controls/LineChart";
import {
    Button,
    IconButton,
    Table,
    TableBody,
    TableCell,
} from "@mui/material";
import TextBox from "../../../../../controls/TextBox";
import { Delete, Add } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import TableHeader from "../../responses/TableHeader";
import { KeyValuePair } from "../../../../../../models/DeskReview/KeyValueType";

//Interface for IndicatorLineGraph
interface LineGraphProps {
    description?: React.ReactElement;
    lineGraphXAxisText: string;
    lineGraphYAxisText: string;
    graphTitle?: string;
    renderTextbox?: boolean;
    keyValuePairs: Array<KeyValuePair<any, any>>;
    details: string;
    showGraphOnLoad?: boolean;
    onUpdatekeyValuePairs: (keyValuePairs: Array<KeyValuePair<any, any>>) => void;
    onDetailChange?: (details: string) => void;
    renderKeysInLabel?: boolean;
    renderAddRemoveButton?: boolean;
    /**Must contain errors in an array of key value pair  */
    keyValuePairsErrors?: any;
    /**Must contain error in a string format for the detail textbox*/
    errorInDetails?: string;
    getMetNotMetStatusFn?: () => void;
    className?: string;
}

/** Renders the response for indicator 1.1.1 Line Graph */
const IndicatorLineGraph = (props: LineGraphProps) => {
    const {
        keyValuePairs,
        details,
        showGraphOnLoad,
        onUpdatekeyValuePairs,
        onDetailChange,
        graphTitle,
        getMetNotMetStatusFn,
    } = props;

    const { t } = useTranslation(["indicators-responses"]);

    const {
        lineGraphXAxisText,
        lineGraphYAxisText,
        renderTextbox,
        renderKeysInLabel,
        renderAddRemoveButton,
        keyValuePairsErrors,
        errorInDetails,
        className
    } = props;

    //line chart title
    const lineChartTitle = graphTitle
        ? graphTitle
        : t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:NationalLevelEstimate"
        );

    // line chart x-axis tiles
    const [lineChartxAxisTitles, setlineChartxAxisTitles] = useState<
        Array<string | number>
    >([]);

    // Line chart data array
    const [lineChartData, setLineChartData] = useState<Array<Array<number>>>([]);

    // Line chart xAxisTitle
    const [xAxisTitle, setXAxisTitle] = useState("");

    // Line chart type
    const lineChartType = "line";

    useEffect(() => {
        //Generate the graph only when "showGraphOnLoad" is set to TRUE
        showGraphOnLoad && generateLineGraphHandler();
    }, [showGraphOnLoad]);

    //OnChange Handler for the table inputs
    const onChangeHandler = (
        e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
        index: number
    ) => {
        const values = [...keyValuePairs];

        //Update KEY if key textbox's value changes else change the VALUE
        if (e.target.name === "key") {
            values[index].key = e.target.value ? +e.target.value : null;
        } else {
            values[index].value = e.target.value;
        }

        onUpdatekeyValuePairs(values);
    };

    //Triggers whenever 'Details' textarea's value changes
    const detailsChangeHandler = (
        e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        if (onDetailChange) onDetailChange(e.target.value);
    };

    // Triggered whenever 'Delete Row' button is clicked.
    const onRowDelete = () => {
        onUpdatekeyValuePairs(keyValuePairs.slice(0, keyValuePairs.length - 1));
    };

    // Triggered whenever 'Add Row' button is clicked.
    const onRowAdd = () => {
        const values = [...keyValuePairs, { key: null, value: null }];
        onUpdatekeyValuePairs(values);
    };

    //Generate graph handler
    const generateLineGraphHandler = () => {
        setXAxisTitle(lineGraphXAxisText);
        const rows = graphPayload() || [];
        const yearData = lineGraphXAxisText===t(`translation:DataQualityAnalysis.Month`)? rows.map((item) => t(`translation:Month:${item.year}`)) : rows.map((item) => item.year);
        const estimateData = rows.map((item) => item.estimate);
        setlineChartxAxisTitles(yearData);
        setLineChartData([estimateData]);
    };

    //Returns the data for the row of table
    const graphPayload = () => {
        const payload = [];
        const yearNodes = keyValuePairs.map(
            (item: KeyValuePair<string | number, string | number>) => item.key
        );
        const estimateNodes = keyValuePairs.map(
            (item: KeyValuePair<string | number, string | number>) => item.value || ""
        );

        if (yearNodes && estimateNodes) {
            for (let index = 0; index < yearNodes.length; index++) {
                payload.push({
                    year: renderKeysInLabel ? yearNodes[index] : +yearNodes[index],
                    estimate: +estimateNodes[index],
                });
            }

            return payload;
        }
    };

    return (
        <>
            {props.description}
            <div className="row graph-wrapper">
                <div className="col-xs-12 col-md-4">
                    <Table width="100%" className={className ? "app-table app-table-scroll" : "app-table app-table-sticky-scroll"}>
                        <>
                            <TableHeader headers={[lineGraphXAxisText, lineGraphYAxisText]} />
                            <TableBody>
                                <>
                                    {keyValuePairs.map((KeyValuePair, index: number) => {
                                        return (
                                            <tr key={index}>
                                                <TableCell>
                                                    {renderKeysInLabel ? (
                                                        <>{t(`translation:Month:${KeyValuePair.key}`)}</>
                                                    ) : (
                                                        <TextBox
                                                            key={`key_${index}`}
                                                            id={`key_${index}`}
                                                            className="col-form-control inputfocus"
                                                            maxLength={4}
                                                            type="string"
                                                            name="key"
                                                            placeholder={t("indicators-responses:Common:Year")}
                                                            value={KeyValuePair.key}
                                                            error={
                                                                keyValuePairsErrors &&
                                                                keyValuePairsErrors[index]?.key
                                                            }
                                                            helperText={
                                                                keyValuePairsErrors &&
                                                                keyValuePairsErrors[index]?.key
                                                            }
                                                            onChange={(
                                                                e: ChangeEvent<
                                                                    HTMLInputElement | HTMLTextAreaElement
                                                                >
                                                            ) => {
                                                                onChangeHandler(e, index);
                                                                if (getMetNotMetStatusFn) getMetNotMetStatusFn();
                                                            }}
                                                        />
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        key={`value_${index}`}
                                                        id={`value_${index}`}
                                                        className="col-form-control inputfocus"
                                                        fullWidth
                                                        inputProps={{ max: 100, min: 0 }}
                                                        maxLength={3}
                                                        type="number"
                                                        name="value"
                                                        placeholder={t("indicators-responses:Common:Percent")}
                                                        value={KeyValuePair.value}
                                                        error={
                                                            keyValuePairsErrors &&
                                                            keyValuePairsErrors[index]?.value
                                                        }
                                                        helperText={
                                                            keyValuePairsErrors &&
                                                            keyValuePairsErrors[index]?.value
                                                        }
                                                        onChange={(
                                                            e: ChangeEvent<
                                                                HTMLInputElement | HTMLTextAreaElement
                                                            >
                                                        ) => {
                                                            onChangeHandler(e, index);
                                                            if (getMetNotMetStatusFn) getMetNotMetStatusFn();
                                                        }}
                                                    />
                                                </TableCell>
                                            </tr>
                                        );
                                    })}
                                    {renderAddRemoveButton && (
                                        <tr>
                                            <td className="">
                                                {keyValuePairs.length > 1 && (
                                                    <IconButton onClick={onRowDelete}>
                                                        <Delete />
                                                    </IconButton>
                                                )}
                                                <IconButton onClick={onRowAdd}>
                                                    <Add />
                                                </IconButton>
                                            </td>
                                        </tr>
                                    )}
                                </>
                            </TableBody>
                        </>
                    </Table>
                    <Button
                        className="btn app-btn-secondary app-btn-full"
                        onClick={generateLineGraphHandler}
                    >
                        {t("indicators-responses:Common:GenerateGraph")}
                    </Button>
                </div>
                <div className="col-xs-12 col-md-8">
                    {/* LINE CHART*/}
                    <LineChart
                        chartTitle={lineChartTitle}
                        xAxisArr={lineChartxAxisTitles}
                        lineData={lineChartData}
                        xAxisTitle={xAxisTitle}
                        lineChartType={lineChartType}
                    />
                </div>
                <div className="col-xs-12 col-md-12 mt-3">
                    {renderTextbox && keyValuePairs.length > 1 && (
                        <TextBox
                            label={t("indicators-responses:Common:ReasonForChanges")}
                            fullWidth
                            multiline
                            rows={5}
                            maxLength={1000}
                            value={details}
                            error={errorInDetails ? true : false}
                            helperText={errorInDetails}
                            onChange={detailsChangeHandler}
                        />
                    )}
                </div>
            </div>
        </>
    );
};

export default IndicatorLineGraph;
