﻿import { IconButton } from "@mui/material";
import React, { useEffect, useRef, ChangeEvent, useState } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import TableFooter from "../../../responses/TableFooter";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import {
    Response_1,
    GuidelineDetails,
} from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_3_1/Response_1";
import { HealthSector as ParentHealthSector, PublicHealthSector as ParentPublicHealthSector, PrivateFormalHealthSector as ParentPrivateFormalHealthSector, ReportingDetails } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules, { GuidelineValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import { HealthSector } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_3_1/Response_2";

/** Renders the indicator 2.3.1 response for desk review case strategy */
const Indicator_2_3_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculatePercentageOfYesNo } = useCalculation();

    document.title = t(
        "indicators-responses:app:DR_Objective_2_Indicator_2_3_1_Title"
    );

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);
    const [isFinalizeBtnClick, setIsFinalizeBtnClick] = useState<boolean>(false);

    const {
        response,
        onChange,
        onValueChange,
        onCannotBeAssessed,
        onChangeWithKey,
        getParentResponse,
        onSave,
        onFinalize,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    useEffect(() => {
        getParentResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    useEffect(() => {
        if (response["parentData"]) {
            validationRulesRef.current = response.parentData.cannotBeAssessed === true ? GuidelineValidationRules : ValidationRules;
        }
    }, [response["parentData"]]);

    const headersGuidlines = [
        {
            field: "",
            label: "",
        },
        {
            field: "guideline1",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline1"
            ),
        },
        {
            field: "guideline2",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline2"
            ),
        },
        {
            field: "guideline3",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline3"
            ),
        },
        {
            field: "guideline4",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline4"
            ),
        },
    ];

    const headers = [
        {
            field: "healthSectors",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:HealthSectors"
            ),
        },
        {
            field: "receivedGuidelines",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ReceivedGuidelines"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Details"
            ),
        },
        {
            field: "receivedGuidelines",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ReceivedGuidelines"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Details"
            ),
        },
        {
            field: "receivedGuidelines",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ReceivedGuidelines"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Details"
            ),
        },
        {
            field: "receivedGuidelines",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ReceivedGuidelines"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Details"
            ),
        },
    ];

    const columnsGuidelinesTextbox = [
        {
            field: "guideLineName",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:GuidelineName"
            ),
        },
        {
            field: "linkToCopy",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:LinkToCopy"
            ),
        },
    ];

    const columnsGuidelinesRadio = [
        {
            field: "internetAvailability",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:IsReliableConnection"
            ),
        },
    ];

    //Calculates the proportion for the first guideline received. It checks if the parent health sector reported the malaria cases only then consider the first received guideline for that particular health sector.
    const calculateProportion = (modelKeyName: string) => {
        const parentHealtSector = response?.parentData?.healthSector;
        if (!parentHealtSector) {
            return 0;
        }

        const values: Array<boolean> = [];

        const parentPublicHealthSector: ParentPublicHealthSector = parentHealtSector.publicHealthSector;
        const parentPrivateFormal: ParentPrivateFormalHealthSector = parentHealtSector.privateFormal;
        const parentPrivateInformal: ReportingDetails = parentHealtSector.privateInformal;
        const parentCommunityHealthSector: ReportingDetails = parentHealtSector.community;

        if (parentPublicHealthSector) {
            const publicSector = response?.healthSector?.publicHealthSector;

            // if (parentPublicHealthSector?.publicReportingDetails?.reportingMalaria === true) {
            //     values.push(publicSector?.publicReportingDetails[modelKeyName]);
            // }

            if (parentPublicHealthSector?.healthFacility?.reportingMalaria === true) {
                values.push(publicSector?.healthFacility[modelKeyName]);
            }

            if (parentPublicHealthSector?.laboratory?.reportingMalaria === true) {
                values.push(publicSector?.laboratory[modelKeyName]);
            }

            if (parentPublicHealthSector?.hospital?.reportingMalaria === true) {
                values.push(publicSector?.hospital[modelKeyName]);
            }
        }

        if (parentPrivateFormal) {
            const privateFormal = response?.healthSector?.privateFormal;

            // if (parentPrivateFormal?.privateFormal?.reportingMalaria === true) {
            //     values.push(privateFormal?.privateFormal[modelKeyName]);
            // }

            if (parentPrivateFormal?.healthFacility?.reportingMalaria === true) {
                values.push(privateFormal?.healthFacility[modelKeyName]);
            }

            if (parentPrivateFormal?.faithBasedClinic?.reportingMalaria === true) {
                values.push(privateFormal?.faithBasedClinic[modelKeyName]);
            }

            if (parentPrivateFormal?.ngoClinic?.reportingMalaria === true) {
                values.push(privateFormal?.ngoClinic[modelKeyName]);
            }

            if (parentPrivateFormal?.laboratory?.reportingMalaria === true) {
                values.push(privateFormal?.laboratory[modelKeyName]);
            }

            if (parentPrivateFormal?.hospital?.reportingMalaria === true) {
                values.push(privateFormal?.hospital[modelKeyName]);
            }

            if (parentPrivateFormal?.military?.reportingMalaria === true) {
                values.push(privateFormal?.military[modelKeyName]);
            }

            if (parentPrivateFormal?.police?.reportingMalaria === true) {
                values.push(privateFormal?.police[modelKeyName]);
            }

            if (parentPrivateFormal?.prison?.reportingMalaria === true) {
                values.push(privateFormal?.prison[modelKeyName]);
            }
        }

        if (parentPrivateInformal) {
            if (parentPrivateInformal?.reportingMalaria === true) {
                values.push(response?.healthSector?.privateInformal[modelKeyName]);
            }
        }

        if (parentCommunityHealthSector) {
            if (parentCommunityHealthSector?.reportingMalaria === true) {
                values.push(response?.healthSector?.community[modelKeyName]);
            }
        }

        return calculatePercentageOfYesNo(values);
    };

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {

        if (response["parentData"]) {
            setIsFinalizeBtnClick(false);
            setTrueFlagOnFinalizeButtonClick();

            const isFormValid = validate(response);
            if (isFormValid) {
                onFinalize();
            }
        } else {
            setIsFinalizeBtnClick(true);
        }
    };


    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const proportionOfFirstGuidelineChecked = calculateProportion("isFirstGuidelineReceived");

        onValueChange(
            "metNotMetStatus",
            proportionOfFirstGuidelineChecked >= 80
                ? MetNotMetEnum.Met
                : proportionOfFirstGuidelineChecked < 50
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        //response?.healthSector?.publicHealthSector?.publicReportingDetails?.isFirstGuidelineReceived,
        response?.healthSector?.publicHealthSector?.healthFacility?.isFirstGuidelineReceived,
        response?.healthSector?.publicHealthSector?.hospital?.isFirstGuidelineReceived,
        response?.healthSector?.publicHealthSector?.laboratory?.isFirstGuidelineReceived,
        //response?.healthSector?.privateFormal?.privateFormal?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.healthFacility?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.hospital?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.laboratory?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.faithBasedClinic?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.ngoClinic?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.military?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.police?.isFirstGuidelineReceived,
        response?.healthSector?.privateFormal?.prison?.isFirstGuidelineReceived,
        response?.healthSector?.privateInformal?.isFirstGuidelineReceived,
        response?.healthSector?.community?.isFirstGuidelineReceived
    ]);

    //Create the table row when parent health sector reports malaria cases means in the parent data if health sectors that have property reportingMalaria is equal to true then generate the row.
    const renderHealthSectorRow = () => {
        const parentHealtSector: ParentHealthSector = response?.parentData?.healthSector;
        if (!parentHealtSector) {
            return <></>
        }

        const paddingLeft25 = 'padding-left-25';
        const fontBold = "fw-bold";
        const publicHealthSectorKey = "publicHealthSector";
        const publicReportingDetails = "publicReportingDetails";
        const privateFormalSector = "privateFormal";

        let rows: any = [<></>];
        const parentPublicHealthSector: ParentPublicHealthSector = parentHealtSector.publicHealthSector;
        const parentPrivateFormal: ParentPrivateFormalHealthSector = parentHealtSector.privateFormal;
        const parentPrivateInformal: ReportingDetails = parentHealtSector.privateInformal;
        const parentCommunityHealthSector: ReportingDetails = parentHealtSector.community;

        if (parentPublicHealthSector) {
            const publicSector = response?.healthSector?.publicHealthSector;

            //if (parentPublicHealthSector?.publicReportingDetails?.reportingMalaria === true) {

            if (parentPublicHealthSector?.healthFacility?.reportingMalaria === true ||
                parentPublicHealthSector?.laboratory?.reportingMalaria === true ||
                parentPublicHealthSector?.hospital?.reportingMalaria === true) {
                const publicHealthSectorRows = getHealthSectorTableRows(publicHealthSectorKey, publicReportingDetails, fontBold, t("indicators-responses:Common:Public"), publicSector?.publicReportingDetails, false);
                rows.push(publicHealthSectorRows);
            }
            //}

            if (parentPublicHealthSector?.healthFacility?.reportingMalaria === true) {
                const healthFacilityRows = getHealthSectorTableRows(publicHealthSectorKey, "healthFacility", paddingLeft25, t("indicators-responses:Common:HealthFacilities"), publicSector?.healthFacility, true);
                rows.push(healthFacilityRows);
            }

            if (parentPublicHealthSector?.laboratory?.reportingMalaria === true) {
                const laboratoryRows = getHealthSectorTableRows(publicHealthSectorKey, "laboratory", paddingLeft25, t("indicators-responses:Common:Laboratory"), publicSector?.laboratory, true);
                rows.push(laboratoryRows);
            }

            if (parentPublicHealthSector?.hospital?.reportingMalaria === true) {
                const hospitalRows = getHealthSectorTableRows(publicHealthSectorKey, "hospital", paddingLeft25, t("indicators-responses:Common:Hospital"), publicSector?.hospital, true);
                rows.push(hospitalRows);
            }
        }

        if (parentPrivateFormal) {
            const privateFormal = response?.healthSector?.privateFormal;


            //if (parentPrivateFormal?.privateFormal?.reportingMalaria === true) {
            if (parentPrivateFormal?.healthFacility?.reportingMalaria === true ||
                parentPrivateFormal?.faithBasedClinic?.reportingMalaria === true ||
                parentPrivateFormal?.ngoClinic?.reportingMalaria === true ||
                parentPrivateFormal?.laboratory?.reportingMalaria === true ||
                parentPrivateFormal?.hospital?.reportingMalaria === true ||
                parentPrivateFormal?.military?.reportingMalaria === true ||
                parentPrivateFormal?.police?.reportingMalaria === true ||
                parentPrivateFormal?.prison?.reportingMalaria === true
            ) {
                const privateFormalRows = getHealthSectorTableRows(privateFormalSector, privateFormalSector, fontBold, t("indicators-responses:Common:PrivateFormal"), privateFormal?.privateFormal, false);
                rows.push(privateFormalRows);
            }

            if (parentPrivateFormal?.healthFacility?.reportingMalaria === true) {
                const healthFacilityRows = getHealthSectorTableRows(privateFormalSector, "healthFacility", paddingLeft25, t("indicators-responses:Common:HealthFacilities"), privateFormal?.healthFacility, true);
                rows.push(healthFacilityRows);
            }

            if (parentPrivateFormal?.faithBasedClinic?.reportingMalaria === true) {
                const faithBasedClinicRows = getHealthSectorTableRows(privateFormalSector, "faithBasedClinic", paddingLeft25, t("indicators-responses:Common:FaithBasedClinics"), privateFormal?.faithBasedClinic, true);
                rows.push(faithBasedClinicRows);
            }

            if (parentPrivateFormal?.ngoClinic?.reportingMalaria === true) {
                const ngoClinicRows = getHealthSectorTableRows(privateFormalSector, "ngoClinic", paddingLeft25, t("indicators-responses:Common:NonGovernmentalOrganizationClinics"), privateFormal?.ngoClinic, true);
                rows.push(ngoClinicRows);
            }

            if (parentPrivateFormal?.laboratory?.reportingMalaria === true) {
                const laboratoryRows = getHealthSectorTableRows(privateFormalSector, "laboratory", paddingLeft25, t("indicators-responses:Common:Laboratory"), privateFormal?.laboratory, true);
                rows.push(laboratoryRows);
            }

            if (parentPrivateFormal?.hospital?.reportingMalaria === true) {
                const hospitalRows = getHealthSectorTableRows(privateFormalSector, "hospital", paddingLeft25, t("indicators-responses:Common:Hospital"), privateFormal?.hospital, true);
                rows.push(hospitalRows);
            }

            if (parentPrivateFormal?.military?.reportingMalaria === true) {
                const militaryRows = getHealthSectorTableRows(privateFormalSector, "military", paddingLeft25, t("indicators-responses:Common:Military"), privateFormal?.military, true);
                rows.push(militaryRows);
            }

            if (parentPrivateFormal?.police?.reportingMalaria === true) {
                const policeRows = getHealthSectorTableRows(privateFormalSector, "police", paddingLeft25, t("indicators-responses:Common:Police"), privateFormal?.police, true);
                rows.push(policeRows);
            }

            if (parentPrivateFormal?.prison?.reportingMalaria === true) {
                const prisonRows = getHealthSectorTableRows(privateFormalSector, "prison", paddingLeft25, t("indicators-responses:Common:Prison"), privateFormal?.prison, true);
                rows.push(prisonRows);
            }
        }

        if (parentPrivateInformal) {
            if (parentPrivateInformal?.reportingMalaria === true) {
                const parentPrivateInformalRows = getHealthSectorTableRows("privateInformal", '', fontBold, t("indicators-responses:Common:PrivateInformal"), response?.healthSector?.privateInformal, true);
                rows.push(parentPrivateInformalRows);
            }
        }

        if (parentCommunityHealthSector) {
            if (parentCommunityHealthSector?.reportingMalaria === true) {
                const communityHealthSectorRows = getHealthSectorTableRows("community", '', fontBold, t("indicators-responses:Common:Community"), response?.healthSector?.community, true);
                rows.push(communityHealthSectorRows);
            }
        }

        return rows;
    }

    //Update the health sector fields
    const onHealthSectorChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        healthSectorKeyName: string,
        healthSystemKeyName: string,
        guidelineKeyName: string) => {
        const allHealthSectors: HealthSector = response?.healthSector;
        const healthSector = response?.healthSector[healthSectorKeyName];

        let value: string | boolean = e.target.value;

        if (e.target.type === 'radio') {
            switch (value) {
                case "true":
                    value = true;
                    break;
                case "false":
                    value = false
                    break;
            }
        }

        let _healthSector: HealthSector;

        if (healthSystemKeyName) {
            const healthSystem = healthSector[healthSystemKeyName];

            _healthSector = {
                ...allHealthSectors,
                [healthSectorKeyName]: {
                    ...healthSector,
                    [healthSystemKeyName]: {
                        ...healthSystem,
                        [guidelineKeyName]: value
                    }
                }
            }
        } else {
            _healthSector = {
                ...allHealthSectors,
                [healthSectorKeyName]: {
                    ...healthSector,
                    [guidelineKeyName]: value
                }
            }
        }

        onValueChange("healthSector", _healthSector);
    }

    //Generate the table rows for the given health sector and its health system
    const getHealthSectorTableRows = (
        healthSectorKeyName: string,
        healthSystemKeyName: string,
        cellCssClassName: string,
        label: string,
        healthSystem: GuidelineDetails,
        isInputFieldShow: boolean = true) => {
        const getError = (modelKeyName: string) => {
            const errorKey = `healthSector.${healthSectorKeyName}${healthSystemKeyName ? '.' + healthSystemKeyName : ''}.${modelKeyName}`;
            return errors[errorKey] && errors[errorKey];
        }

        return <TableRow>
            <>
                <TableCell className={cellCssClassName}>
                    {label}
                </TableCell>

                {isInputFieldShow
                    ? (
                        <TableCell>
                            <RadioButtonGroup
                                key={`isFirstGuidelineReceived_${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`isFirstGuidelineReceived_${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`isFirstGuidelineReceived_${healthSectorKeyName}_${healthSystemKeyName}`}
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={healthSystem["isFirstGuidelineReceived"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => {
                                    onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "isFirstGuidelineReceived");
                                    //getMetNotMetStatus();
                                }}
                                error={getError('isFirstGuidelineReceived')}
                                helperText={getError('isFirstGuidelineReceived')}
                            />
                        </TableCell>
                    ) : <></>}
                {isInputFieldShow
                    ? (
                        <TableCell>
                            <TextBox
                                key={`firstGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`firstGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`firstGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                fullWidth
                                value={healthSystem["firstGuidelineDetails"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "firstGuidelineDetails")}
                                error={getError('firstGuidelineDetails')}
                                helperText={getError('firstGuidelineDetails')}
                            />
                        </TableCell>
                    ) : <></>}
                {isInputFieldShow
                    ? (
                        <TableCell>
                            <RadioButtonGroup
                                key={`isSecondGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`isSecondGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`isSecondGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={healthSystem["isSecondGuidelineReceived"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "isSecondGuidelineReceived")}
                                error={getError('isSecondGuidelineReceived')}
                                helperText={getError('isSecondGuidelineReceived')}
                            />
                        </TableCell>
                    ) : <></>}

                {isInputFieldShow
                    ? (
                        <TableCell>
                            <TextBox
                                key={`secondGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`secondGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`secondGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                fullWidth
                                value={healthSystem["secondGuidelineDetails"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "secondGuidelineDetails")}
                                error={getError('secondGuidelineDetails')}
                                helperText={getError('secondGuidelineDetails')}
                            />
                        </TableCell>
                    ) : <></>}
                {isInputFieldShow
                    ? (
                        <TableCell>
                            <RadioButtonGroup
                                key={`isThirdGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`isThirdGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`isThirdGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={healthSystem["isThirdGuidelineReceived"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "isThirdGuidelineReceived")}
                                error={getError('isThirdGuidelineReceived')}
                                helperText={getError('isThirdGuidelineReceived')}
                            />
                        </TableCell>
                    ) : <></>}

                {isInputFieldShow
                    ? (
                        <TableCell>
                            <TextBox
                                key={`thirdGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`thirdGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`thirdGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                fullWidth
                                value={healthSystem["thirdGuidelineDetails"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "thirdGuidelineDetails")}
                                error={getError('thirdGuidelineDetails')}
                                helperText={getError('thirdGuidelineDetails')}
                            />
                        </TableCell>
                    ) : <></>}

                {isInputFieldShow
                    ? (
                        <TableCell>
                            <RadioButtonGroup
                                key={`isFourthGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`isFourthGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`isFourthGuidelineReceived${healthSectorKeyName}_${healthSystemKeyName}`}
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={healthSystem["isFourthGuidelineReceived"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "isFourthGuidelineReceived")}
                                error={getError('isFourthGuidelineReceived')}
                                helperText={getError('isFourthGuidelineReceived')}
                            />
                        </TableCell>
                    ) : <></>}

                {isInputFieldShow
                    ? (
                        <TableCell>
                            <TextBox
                                key={`fourthGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                id={`fourthGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                name={`fourthGuidelineDetails${healthSectorKeyName}_${healthSystemKeyName}`}
                                fullWidth
                                value={healthSystem["fourthGuidelineDetails"]}
                                onChange={(
                                    e: React.ChangeEvent<HTMLInputElement>
                                ) => onHealthSectorChange(e, healthSectorKeyName, healthSystemKeyName, "fourthGuidelineDetails")}
                                error={getError('fourthGuidelineDetails')}
                                helperText={getError('fourthGuidelineDetails')}
                            />
                        </TableCell>
                    ) : <></>}
            </>
        </TableRow>
    }

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:BurdenAssessment"
                        )}
                    </p>
                    <p>
                        {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ResponseDesc"
                        )}
                    </p>
                    <p>
                        <i>
                            {t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:NoteToCompleteAssessment"
                            )}
                        </i>
                    </p>
                    {
                        //Show error message if not a single guideline filled or selected
                        errors["guideline1.guideLineName"] && (

                            <span className="Mui-error d-flex mb-2">
                                *
                                {t(
                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ResponseError"
                                )}
                            </span>
                        )}

                    {!response["parentData"] && isFinalizeBtnClick &&
                        (
                            <span>

                                <small className="Mui-error d-flex mt-2 mx-3">
                                    *  {t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ParentDataNotFilledError")}
                                </small>
                            </span>
                        )}

                    <Table>
                        <>
                            <TableHeader
                                headers={headersGuidlines.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {columnsGuidelinesTextbox.map(
                                        (column: any, index: number) => (
                                            <TableRow key={`row_${column.label}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{column.label}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={column.field}
                                                            name={column.field}
                                                            fullWidth
                                                            value={response["guideline1"]?.[column.field]}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, "guideline1")}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={column.field}
                                                            name={column.field}
                                                            fullWidth
                                                            value={response["guideline2"]?.[column.field]}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, "guideline2")}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={column.field}
                                                            name={column.field}
                                                            fullWidth
                                                            value={response["guideline3"]?.[column.field]}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, "guideline3")}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={column.field}
                                                            name={column.field}
                                                            fullWidth
                                                            value={response["guideline4"]?.[column.field]}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, "guideline4")}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        )
                                    )}

                                    {columnsGuidelinesRadio.map((column: any, index: number) => (
                                        <TableRow key={Math.random().toString()}>
                                            <>
                                                <TableCell>
                                                    <>{column.label}</>
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id={column.field}
                                                        name={column.field}
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={response["guideline1"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline1")}
                                                        error={
                                                            errors[`guideline1.${column.field}`] &&
                                                            errors[`guideline1.${column.field}`]
                                                        }
                                                        helperText={
                                                            errors[`guideline1.${column.field}`] &&
                                                            errors[`guideline1.${column.field}`]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id={column.field}
                                                        name={column.field}
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={response["guideline2"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline2")}
                                                        error={
                                                            errors[`guideline2.${column.field}`] &&
                                                            errors[`guideline2.${column.field}`]
                                                        }
                                                        helperText={
                                                            errors[`guideline2.${column.field}`] &&
                                                            errors[`guideline2.${column.field}`]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id={column.field}
                                                        name={column.field}
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={response["guideline3"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline3")}
                                                        error={
                                                            errors[`guideline3.${column.field}`] &&
                                                            errors[`guideline3.${column.field}`]
                                                        }
                                                        helperText={
                                                            errors[`guideline3.${column.field}`] &&
                                                            errors[`guideline3.${column.field}`]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id={column.field}
                                                        name={column.field}
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={response["guideline4"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline4")}
                                                        error={
                                                            errors[`guideline4.${column.field}`] &&
                                                            errors[`guideline4.${column.field}`]
                                                        }
                                                        helperText={
                                                            errors[`guideline4.${column.field}`] &&
                                                            errors[`guideline4.${column.field}`]
                                                        }

                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                                </>
                            </TableBody>
                        </>
                    </Table>
                    {!response?.parentData?.cannotBeAssessed &&
                        <>
                            <p className="pt-3">
                                {t(
                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ResponseDesc1"
                                )}
                            </p>

                            <Table className="app-table table">
                                <>
                                    <thead>
                                        <th colSpan={1}>
                                            <></>
                                        </th>
                                        <th colSpan={2}>
                                            {response["guideline1"]?.["guideLineName"] ||
                                                t(
                                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline1Name"
                                                )}
                                        </th>
                                        <th colSpan={2}>
                                            {response["guideline2"]?.["guideLineName"] ||
                                                t(
                                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline2Name"
                                                )}
                                        </th>
                                        <th colSpan={2}>
                                            {response["guideline3"]?.["guideLineName"] ||
                                                t(
                                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline3Name"
                                                )}
                                        </th>
                                        <th colSpan={2}>
                                            {response["guideline4"]?.["guideLineName"] ||
                                                t(
                                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline4Name"
                                                )}
                                        </th>
                                    </thead>

                                    <TableHeader
                                        headers={headers.map((header: any) => header.label)}
                                    />
                                    <TableBody>
                                        <>
                                            {renderHealthSectorRow()}
                                        </>
                                    </TableBody>

                                    <TableFooter>
                                        <>
                                            <TableCell>
                                                <>
                                                    <span>
                                                        {t(
                                                            "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:TableFooterProportions"
                                                        )}
                                                        <IconButton className="grid-icon-button">
                                                            <Tooltip
                                                                content={t(
                                                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:TableProportionsTooltip"
                                                                )}
                                                                isHtml
                                                            >
                                                                <InfoIcon fontSize="small" />
                                                            </Tooltip>
                                                        </IconButton>
                                                    </span>
                                                </>
                                            </TableCell>
                                            <TableCell colSpan={2}>
                                                <label>
                                                    {calculateProportion("isFirstGuidelineReceived")}%

                                                </label>
                                            </TableCell>
                                            <TableCell colSpan={2}>
                                                <label>
                                                    {calculateProportion("isSecondGuidelineReceived")}
                                                    %
                                                </label>
                                            </TableCell>
                                            <TableCell colSpan={2}>
                                                <label>
                                                    {calculateProportion("isThirdGuidelineReceived")}%
                                                </label>
                                            </TableCell>
                                            <TableCell colSpan={2}>
                                                <label>
                                                    {calculateProportion("isFourthGuidelineReceived")}
                                                    %
                                                </label>
                                            </TableCell>
                                        </>
                                    </TableFooter>
                                </>
                            </Table>
                        </>}
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_2_3_1_Response;
