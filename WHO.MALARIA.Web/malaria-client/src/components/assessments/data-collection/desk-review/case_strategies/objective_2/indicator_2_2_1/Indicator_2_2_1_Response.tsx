﻿import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import React, { useEffect, useRef, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import Checkbox from "../../../../../../controls/Checkbox";
import { IconButton } from "@mui/material";
import Tooltip from "../../../../../../controls/Tooltip";
import {
    Response_1,
    InformationSystem,
} from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import InfoIcon from "@mui/icons-material/Info";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 2.2.1 */
function Indicator_2_2_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_2_Indicator_2_2_1_Title"
    );

    const headers = [
        {
            field: "",
            label: "",
        },
        {
            field: "informationSystem1",
            label: `${t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:InformationSystem1"
            )}(${t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:CaseInformationSystem1Desc"
            )}) `,
        },
        {
            field: "informationSystem2",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:InformationSystem2"
            ),
        },
        {
            field: "informationSystem3",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:InformationSystem3"
            ),
        },
        {
            field: "informationSystem4",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:InformationSystem4"
            ),
        },
    ];

    const columns = [
        {
            field: "platformName",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:PlatformName"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:PlatformNameFirstPlaceholder"
            ),
            placeholderSecondColumn: "",
        },
        {
            field: "dataStorage",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:DataStorage"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:DataStorageFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:DataStorageSecondPlaceHolder"
            ),
        },
        {
            field: "database",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:Database"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:DatabaseFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:DatabaseSecondPlaceHolder"
            ),
        },
        {
            field: "version",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:Version"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:VersionFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:VersionSecondPlaceHolder"
            ),
        },
        {
            field: "instance",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:Instance"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:InstanceFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:InstanceSecondPlaceHolder"
            ),
        },
        {
            field: "managedBy",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:ManagedBy"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:ManagedByFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:ManagedBySecondPlaceHolder"
            ),
        },
        {
            field: "startDate",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:StartDate"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:StartDateFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:StartDateSecondPlaceHolder"
            ),
        },
        {
            field: "healthSectors",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:HealthSectors"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:HealthSectorsFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:HealthSectorsSecondPlaceHolder"
            ),
        },
        {
            field: "geographicalScope",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:GeographicalScope"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:GeographicalScopeFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:GeographicalScopeFirstPlaceholder"
            ),
        },
        {
            field: "typeOfSurveillance",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:TypeOfSurveillance"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:TypeOfSurveillanceFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:TypeOfSurveillanceSecondPlaceHolder"
            ),
        },
        {
            field: "levelOfReporting",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:LevelOfReporting"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:LevelOfReportingFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:LevelOfReportingSecondPlaceHolder"
            ),
        },
        {
            field: "otherDiseases",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:OtherDiseases"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:OtherDiseasesFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:OtherDiseasesSecondPlaceHolder"
            ),
        },
        {
            field: "currentStatus",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:CurrentStatus"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:CurrentStatusFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:CurrentStatusSecondPlaceHolder"
            ),
        },
        {
            field: "administrativeUnit",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:AdministrativeUnit"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:AdministrativeUnitFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:AdministrativeUnitSecondPlaceHolder"
            ),
        },
        {
            field: "masterFacility",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:MasterFacility"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:MasterFacilityFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:MasterFacilitySecondPlaceHolder"
            ),
        },
    ];

    const informationSystems = columns.map(
        (x) => new InformationSystem(null, null, null, null)
    );
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithIndex,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(
        new Response_1(false, null, null, null, informationSystems),
        validate
    );

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onValueChange("metNotMetStatus", null);
        onCannotBeAssessed(evt);
    }

    // triggers onChange of noInformationSystemExists checkbox 
    const onNoInformationSystemExistsChange = (evt: ChangeEvent<HTMLInputElement>) => {
        let status = null;
        if (evt.currentTarget.checked) {
            status = MetNotMetEnum.NotMet;
        }
        const _respone = { ...response, metNotMetStatus: status, noInformationSystemExists: evt.currentTarget.checked }
        onValueChange("metNotMetStatus", status);
        onValueChange("noInformationSystemExists", evt.currentTarget.checked);
    }

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    return (
        <>
            <div className="response-status-wrapper">
                {!response?.cannotBeAssessed &&
                    <div className="d-flex">
                        <fieldset disabled={response?.noInformationSystemExists}>
                            <RadioButtonGroup
                                id="metNotMetStatus"
                                name="metNotMetStatus"
                                row
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        MetNotMetEnum.Met,
                                        t("indicators-responses:Common:Met")
                                    ),
                                    new MultiSelectModel(
                                        MetNotMetEnum.PartiallyMet,
                                        t("indicators-responses:Common:PartiallyMet")
                                    ),
                                    new MultiSelectModel(
                                        MetNotMetEnum.NotMet,
                                        t("indicators-responses:Common:NotMet")
                                    ),
                                ]}
                                value={response?.metNotMetStatus}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                    onValueChange("metNotMetStatus", e.currentTarget.value)
                                }
                            />
                        </fieldset>

                        <IconButton className="grid-icon-button">
                            <Tooltip
                                content={t(
                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:MetNotMetTooltip"
                                )}
                                isHtml
                            >
                                <InfoIcon fontSize="default" />
                            </Tooltip>
                        </IconButton>
                    </div>
                }
                {
                    // Show error message met not met
                    (errors["metNotMetStatus"] && !response?.cannotBeAssessed && (
                        <span className="Mui-error d-flex mb-2">
                            *{errors["metNotMetStatus"]}
                        </span>
                    ))
                }
            </div>

            <div className="response-assess-wrapper">
                {!response?.noInformationSystemExists &&
                    <Checkbox
                        id="cannotBeAssessed"
                        name="cannotBeAssessed"
                        label={t("indicators-responses:Common:IndicatorNoAssess")}
                        onChange={onCannotBeAssessedChange}
                        checked={response?.cannotBeAssessed}
                    />
                }

                {!response?.cannotBeAssessed && (
                    <Checkbox
                        id="noInformationSystemExists"
                        name="noInformationSystemExists"
                        label={t("indicators-responses:Common:NoInformationSystemExists")}
                        value={response?.noInformationSystemExists}
                        onChange={onNoInformationSystemExistsChange}
                        checked={response?.noInformationSystemExists}
                    />
                )}
            </div>

            {!(response?.cannotBeAssessed || response?.noInformationSystemExists) ? (
                <div className="response-wrapper">
                    <p>
                        {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:ResponseDesc"
                        )}
                    </p>
                    <p className="fst-italic">
                        {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_1:ResponseTitle"
                        )}
                    </p>

                    <div>
                        <Table>
                            <>
                                <TableHeader
                                    headers={headers.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {columns.map((column: any, index: number) => (
                                            <TableRow id={`column_${column.field}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{column.label}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${column.field}_${index}_informationSystem1`}
                                                            name="informationSystem1"
                                                            placeholder={column.placeholderFirstColumn}
                                                            fullWidth
                                                            rows={1}
                                                            variant="outlined"
                                                            multiline
                                                            value={
                                                                response?.informationSystems[index]
                                                                    ?.informationSystem1 || ""
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onChangeWithIndex(
                                                                    e,
                                                                    "informationSystems",
                                                                    index
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `informationSystems[${index}].informationSystem1`
                                                                ] &&
                                                                errors[
                                                                `informationSystems[${index}].informationSystem1`
                                                                ]
                                                            }
                                                            helperText={
                                                                errors[
                                                                `informationSystems[${index}].informationSystem1`
                                                                ] &&
                                                                errors[
                                                                `informationSystems[${index}].informationSystem1`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${column.field}_${index}_informationSystem2`}
                                                            name="informationSystem2"
                                                            placeholder={column.placeholderSecondColumn}
                                                            fullWidth
                                                            rows={1}
                                                            variant="outlined"
                                                            multiline
                                                            value={
                                                                response?.informationSystems[index]
                                                                    ?.informationSystem2 || ""
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onChangeWithIndex(
                                                                    e,
                                                                    "informationSystems",
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${column.field}_${index}_informationSystem3`}
                                                            name="informationSystem3"
                                                            fullWidth
                                                            rows={1}
                                                            variant="outlined"
                                                            multiline
                                                            value={
                                                                response?.informationSystems[index]
                                                                    ?.informationSystem3 || ""
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onChangeWithIndex(
                                                                    e,
                                                                    "informationSystems",
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${column.field}_${index}_informationSystem4`}
                                                            name="informationSystem4"
                                                            fullWidth
                                                            rows={1}
                                                            variant="outlined"
                                                            multiline
                                                            value={
                                                                response?.informationSystems[index]
                                                                    ?.informationSystem4 || ""
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onChangeWithIndex(
                                                                    e,
                                                                    "informationSystems",
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                    </>
                                </TableBody>
                            </>
                        </Table>
                    </div>
                </div>
            ) :
                (
                    <>
                        {response?.cannotBeAssessed ?
                            (
                                <div className="response-wrapper d-flex">
                                    <TextBox
                                        id="cannotBeAssessedReason"
                                        name="cannotBeAssessedReason"
                                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                                        multiline
                                        rows={10}
                                        variant="outlined"
                                        fullWidth
                                        value={response?.cannotBeAssessedReason || ""}
                                        onChange={onChange}
                                        error={
                                            errors["cannotBeAssessedReason"] &&
                                            errors["cannotBeAssessedReason"]
                                        }
                                        helperText={
                                            errors["cannotBeAssessedReason"] &&
                                            errors["cannotBeAssessedReason"]
                                        }
                                    />
                                </div>

                            ) : (
                                <>

                                </>

                            )}
                    </>
                )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_2_2_1_Response;
