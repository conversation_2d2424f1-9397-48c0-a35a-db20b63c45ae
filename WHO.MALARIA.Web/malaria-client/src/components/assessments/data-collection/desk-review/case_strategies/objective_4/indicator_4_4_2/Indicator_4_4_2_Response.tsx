﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import InfoIcon from "@mui/icons-material/Info";
import Tooltip from "../../../../../../controls/Tooltip";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_4_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";
import ValidationRules from "./ValidationRules";

/** Renders the indicator 4.4.2 response for desk review */
const Indicator_4_4_2_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_4_Indicator_4_4_2_Title"
    );

    const validate = useFormValidation(ValidationRules);
    const errors = useSelector((state: any) => state.error);

    //Contains all the excluded properties in the array which are not needed
    const excludedPropertiesRowData: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "proportionOfRelevantStaff",
        "estimationDescriptions",
    ];

    //Contains all the excluded properties in the array which are not needed
    const excludedPropertiesRowDataTwo: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "jobAidContent",
        "estimationDescriptions",
    ];

    const {
        response,
        onChange,
        onChangeWithKey,
        onSave,
        onFinalize,
        getResponse,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    // triggers on click of finalize buttons, performs validations and then action is performed
    const onResponseFinalize = () => {
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    const headers = [
        {
            field: "jobAids",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:JobAids"
            ),
        },
        {
            field: "nationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:NationalLevel"
            ),
        },
        {
            field: "subnationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:SubnationalLevel"
            ),
        },
        {
            field: "serviceDeliveryLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:ServiceDeliveryLevel"
            ),
        },
    ];

    const rowData: any = {
        jobAidContent: {
            field: "jobAidContent",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:JobAidContent"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:DescribeJobAids"
            ),
        },
    };

    const rowDataTwo: any = {
        proportionOfRelevantStaff: {
            field: "proportionOfRelevantStaff",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:ProportionOfRelevantStaff"
            ),
        },
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:ResponseDesc"
                    )}
                </p>
                <p>
                    {t(
                        "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:ResponseDesc1"
                    )}
                </p>
                <Table>
                    <>
                        <TableHeader headers={headers.map((header: any) => header.label)} />
                        <TableBody>
                            <>
                                {Object.keys(response)
                                    .filter((key) => !excludedPropertiesRowData.includes(key))
                                    .map((row: string, index: any) => (
                                        <TableRow key={`row_${row}_${index}`}>
                                            <>
                                                <TableCell>
                                                    <label>{rowData[row]?.label}</label>
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${rowData[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="nationalLevel"
                                                        placeholder={rowData[row]?.placeholderFirstColumn}
                                                        fullWidth
                                                        multiline
                                                        rows={3}
                                                        value={response[row]?.nationalLevel}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${response[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="subNationalLevel"
                                                        placeholder={rowData[row]?.placeholderFirstColumn}
                                                        fullWidth
                                                        multiline
                                                        rows={3}
                                                        value={response[row]?.subNationalLevel}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>

                                                <TableCell>
                                                    <TextBox
                                                        id={`${response[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="serviceDeliveryLevel"
                                                        placeholder={rowData[row]?.placeholderFirstColumn}
                                                        fullWidth
                                                        multiline
                                                        rows={3}
                                                        value={response[row]?.serviceDeliveryLevel}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}

                                {Object.keys(response)
                                    .filter((key) => !excludedPropertiesRowDataTwo.includes(key))
                                    .map((row: string, index: any) => (
                                        <TableRow key={`row_${row}_${index}`}>
                                            <>
                                                <>
                                                    {rowDataTwo[row]?.label}
                                                </>

                                                <TableCell>
                                                    <TextBox
                                                        id={`${response[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="nationalLevel"
                                                        type="number"
                                                        fullWidth
                                                        inputProps={{
                                                            max: 100,
                                                            min: 0,
                                                        }}
                                                        maxLength={3}
                                                        placeholder="10%"
                                                        value={response[row]?.nationalLevel}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                        error={
                                                            errors[`${row}.nationalLevel`] &&
                                                            errors[`${row}.nationalLevel`]
                                                        }
                                                        helperText={
                                                            errors[`${row}.nationalLevel`] &&
                                                            errors[`${row}.nationalLevel`]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${response[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="subNationalLevel"
                                                        type="number"
                                                        fullWidth
                                                        inputProps={{
                                                            max: 100,
                                                            min: 0,
                                                        }}
                                                        placeholder="10%"
                                                        maxLength={3}
                                                        value={response[row]?.subNationalLevel}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                        error={
                                                            errors[`${row}.subNationalLevel`] &&
                                                            errors[`${row}.subNationalLevel`]
                                                        }
                                                        helperText={
                                                            errors[`${row}.subNationalLevel`] &&
                                                            errors[`${row}.subNationalLevel`]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${response[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="serviceDeliveryLevel"
                                                        type="number"
                                                        fullWidth
                                                        inputProps={{
                                                            max: 100,
                                                            min: 0,
                                                        }}
                                                        placeholder="10%"
                                                        maxLength={3}
                                                        value={response[row]?.serviceDeliveryLevel}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                        error={
                                                            errors[`${row}.serviceDeliveryLevel`] &&
                                                            errors[`${row}.serviceDeliveryLevel`]
                                                        }
                                                        helperText={
                                                            errors[`${row}.serviceDeliveryLevel`] &&
                                                            errors[`${row}.serviceDeliveryLevel`]
                                                        }
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                            </>
                        </TableBody>
                    </>
                </Table>

                <div className="row mt-2">
                    <div className="col-xs-12 col-sm-8 d-flex">
                        <TextBox
                            id="estimationDescriptions"
                            name="estimationDescriptions"
                            label={t(
                                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_2:DescribeTheEstimation"
                            )}
                            multiline
                            rows={10}
                            variant="outlined"
                            fullWidth
                            value={response?.estimationDescriptions}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e)}
                            InputLabelProps={{ shrink: true }}
                        />
                    </div>
                </div>
            </div>
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_4_4_2_Response;
