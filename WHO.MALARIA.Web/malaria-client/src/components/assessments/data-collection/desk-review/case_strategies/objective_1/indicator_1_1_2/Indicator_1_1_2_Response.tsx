﻿import { useEffect, useRef, useState, ChangeEvent } from "react";
import IndicatorLineGraph from "../IndicatorLineGraph";
import IndicatorBarGraph from "../IndicatorBarGraph";
import { TabModel } from "../../../../../../../models/TabModel";
import WHOTabs from "../../../../../../controls/WHOTabs";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import { Button } from "@mui/material";
import classNames from "classnames";
import useYears from "../../../../useYears";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Dropdown from "../../../../../../controls/Dropdown";
import { useTranslation } from "react-i18next";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import { useDispatch } from "react-redux";
import useIndicatorResponseCaptureForTabs from "../../../responses/useIndicatorResponseCaptureForTabs";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_2/Response_1";
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import parse from "html-react-parser";

/** Renders the response for indicator 1.1.2 */
const Indicator_1_1_2_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    const years = useYears();
    const portionOfSuspectsTestedOverRegionTab =
        "portionOfSuspectsTestedOverRegionTab";

    const portionOfSuspectsTestedOverTimeTab =
        "portionOfSuspectsTestedOverTimeTab";

    document.title = t(
        "indicators-responses:app:DR_Objective_1_Indicator_1_1_2_Title"
    );

    const [currentTab, setCurrentTab] = useState<number>(0);
    const dispatch = useDispatch();
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCaptureForTabs<Response_1>(
        Response_1.init(),
        validate
    );

    const errors = useSelector((state: any) => state.error);

    useEffect(() => {
        dispatch(updateStepIndex(0));
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    //Triggered whenever the year values are changed and update response
    const yearOfDataChangeHandler = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const _response = {
            ...response.portionOfSuspectsTestedOverRegionTab,
            yearOfData: e.target.value,
        };

        onValueChange("portionOfSuspectsTestedOverRegionTab", _response);
    };

    //Updates national level estimates
    const onNationalLevelEstimateChange = (
        nationalEstimates: Array<KeyValuePair<any, any>>
    ) => {
        let values;
        switch (currentTab) {
            case 0:
                //Convert key and values in number format as to align with model
                values = nationalEstimates.map((value) => ({
                    key: value.key ? +value.key : null,
                    value: value.value ? (+value.value) : value.value === 0 ? 0 : null
                }));

                onValueChange(portionOfSuspectsTestedOverTimeTab, {
                    values,
                    reasonForChangeObservedOvertime:
                        nationalEstimates.length > 1
                            ? response.portionOfSuspectsTestedOverTimeTab
                                .reasonForChangeObservedOvertime
                            : "",
                });
                break;
            case 1:
                //Convert values in number format as to align with model
                values = nationalEstimates.map((value) => ({
                    key: value.key,
                    value: value.value ? (+value.value) : value.value === 0 ? 0 : null
                }));

                onValueChange(portionOfSuspectsTestedOverRegionTab, {
                    values,
                    yearOfData: response.portionOfSuspectsTestedOverRegionTab.yearOfData,
                    reasonForChangeObservedOvertime:
                        nationalEstimates.length > 1
                            ? response.portionOfSuspectsTestedOverRegionTab
                                .reasonForChangeObservedOvertime
                            : "",
                });
                break;
        }
    };

    //Update reason for changes observed overtime
    const onDetailChange = (reasonForChangeObservedOvertime: string) => {
        switch (currentTab) {
            case 0:
                onValueChange(portionOfSuspectsTestedOverTimeTab, {
                    ...response.portionOfSuspectsTestedOverTimeTab,
                    reasonForChangeObservedOvertime,
                });
                break;
            case 1:
                onValueChange(portionOfSuspectsTestedOverRegionTab, {
                    ...response.portionOfSuspectsTestedOverRegionTab,
                    reasonForChangeObservedOvertime,
                });
                break;
        }
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        let mostRecentYearDataProportion: number = 0;
        let mostRecentYear: number = 0;
        response?.portionOfSuspectsTestedOverTimeTab.values.forEach(
            (item: KeyValuePair<number, number>) => {
                if (item.key > mostRecentYear) {
                    mostRecentYearDataProportion = item.value;
                    mostRecentYear = item.key;
                }
            }
        );

        onValueChange(
            "metNotMetStatus",
            mostRecentYearDataProportion >= 80
                ? MetNotMetEnum.Met
                : mostRecentYearDataProportion < 50
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [response?.portionOfSuspectsTestedOverTimeTab.values]);

    const tabs: Array<TabModel> = [
        new TabModel(
            0,
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspects"
            ),
            (
                <>
                    <IndicatorLineGraph
                        lineGraphXAxisText={t("indicators-responses:Common:Year")}
                        lineGraphYAxisText={t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:NationalLevelEstimate"
                        )}
                        description={
                            <>
                                <p>
                                    {parse(t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ResponseDescA"
                                    ))}
                                </p>
                                <p className="fw-italic">
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:MinimumOnePointRequired"
                                    )}
                                </p>
                                {/*Show error message if not all radio buttons are selected for Data quality control check in place*/}
                                {!!Object.keys(errors).length && (
                                    <span className="Mui-error d-flex mb-2">
                                        *
                                        {t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ResponseError"
                                        )}
                                    </span>
                                )}
                            </>
                        }
                        renderAddRemoveButton={true}
                        renderTextbox={true}
                        keyValuePairs={
                            response?.portionOfSuspectsTestedOverTimeTab?.values || []
                        }
                        onUpdatekeyValuePairs={onNationalLevelEstimateChange}
                        details={
                            response?.portionOfSuspectsTestedOverTimeTab
                                ?.reasonForChangeObservedOvertime
                        }
                        onDetailChange={onDetailChange}
                        showGraphOnLoad={
                            response?.portionOfSuspectsTestedOverTimeTab?.values?.length > 0
                        }
                        graphTitle={`${t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspects")} ${t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspectsPercent")}`}

                        keyValuePairsErrors={
                            errors["portionOfSuspectsTestedOverTimeTab.values"]
                        }
                        errorInDetails={
                            errors[
                            "portionOfSuspectsTestedOverTimeTab.reasonForChangeObservedOvertime"
                            ]
                        }
                        getMetNotMetStatusFn={getMetNotMetStatus}
                    />
                </>
            )
        ),
        new TabModel(
            1,
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspectsOverRegion"
            ),
            (
                <>
                    <p>{t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:YearOfDataHeading")}</p>
                    <div className="col-xs-12 col-md-4">
                        <div className="row mb-2">
                            <div className="col-xs-12 col-md-6">
                                <Dropdown
                                    id="yearOfData"
                                    name="yearOfData"
                                    variant="outlined"
                                    size="small"
                                    label={t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:YearOfData"
                                    )}
                                    value={
                                        response?.portionOfSuspectsTestedOverRegionTab?.yearOfData
                                    }
                                    options={years.map((year) => {
                                        return new MultiSelectModel(
                                            year,
                                            year.toString(),
                                            false,
                                            false
                                        );
                                    })}
                                    onChange={(
                                        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
                                    ) => yearOfDataChangeHandler(e)}
                                    error={errors["portionOfSuspectsTestedOverRegionTab.yearOfData"] && errors["portionOfSuspectsTestedOverRegionTab.yearOfData"]}
                                    helperText={
                                        errors["portionOfSuspectsTestedOverRegionTab.yearOfData"] && errors["portionOfSuspectsTestedOverRegionTab.yearOfData"]
                                    }
                                />
                            </div>
                        </div>
                    </div>
                    <IndicatorBarGraph
                        barGraphXAxisText={t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:Region"
                        )}
                        barGraphYAxisText={"%"}
                        description={
                            <>
                                <p>
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ResponseDescB"
                                    )}
                                </p>
                                <p className="fw-italic"></p>
                            </>
                        }
                        renderTextbox={true}
                        keyValuePairs={
                            response?.portionOfSuspectsTestedOverRegionTab.values
                        }
                        onUpdatekeyValuePairs={onNationalLevelEstimateChange}
                        details={
                            response?.portionOfSuspectsTestedOverRegionTab
                                .reasonForChangeObservedOvertime
                        }
                        onDetailChange={onDetailChange}
                        showGraphOnLoad={
                            response?.portionOfSuspectsTestedOverRegionTab?.values?.length > 0
                        }

                        graphTitle={response?.portionOfSuspectsTestedOverRegionTab?.yearOfData ? `${t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspectsOverRegion")} (${response?.portionOfSuspectsTestedOverRegionTab?.yearOfData || ""}) ${t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspectsPercent")}` : `${t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspectsOverRegion")} ${t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ProportionOfSuspectsPercent")}`}

                        keyValuePairsErrors={
                            errors["portionOfSuspectsTestedOverRegionTab.values"]
                        }
                        errorInDetails={
                            errors[
                            "portionOfSuspectsTestedOverRegionTab.reasonForChangeObservedOvertime"
                            ]
                        }
                    />
                </>
            )
        ),
    ];

    // triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTab(newValue);
        dispatch(updateStepIndex(newValue));
    };

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="app-tab-wrapper">
                        <WHOTabs
                            tabs={tabs}
                            value={currentTab}
                            onChange={onTabChange}
                            scrollable={false}
                        >
                            <div className="p-3">{tabs[currentTab].children}</div>
                        </WHOTabs>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        onChange={onChange}
                        value={response?.cannotBeAssessedReason || ""}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_1_1_2_Response;
