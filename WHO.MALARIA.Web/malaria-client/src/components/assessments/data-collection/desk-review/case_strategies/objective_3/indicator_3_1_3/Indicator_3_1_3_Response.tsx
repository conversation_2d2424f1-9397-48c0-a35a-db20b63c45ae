import { useEffect, useRef, ChangeEvent } from "react";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TextBox from "../../../../../../controls/TextBox";
import Checkbox from "../../../../../../controls/Checkbox";
import { Button } from "@mui/material";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_1_3/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";;

/** Renders response for indicator 3.1.3 */
function Indicator_3_1_3_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculatePercentage } = useCalculation();
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);
    let isProportionRateValid: boolean = true;

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid && isProportionRateValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const rows = [
        {
            field: "",
            label: "",
        },
        {
            field: "healthFacilityWithStocksOut",
            label: t("indicators-responses:Common:HealthFacilitiesWithStockOuts"),
        },
        {
            field: "healthFacilitiesReporting",
            label: t("indicators-responses:Common:HealthFacilitiesReporting"),
        },
        {
            field: "healthFacilitiesWithStockOuts",
            label: t(
                "indicators-responses:Common:ProportionHealthFacilitiesWithStockOuts"
            ),
        },
    ];

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const averagePercentage = calculatePercentage(
            response?.healthFacilitiesWithStockOut,
            response?.healthFacilitiesReporting
        );

        onValueChange(
            "metNotMetStatus",
            averagePercentage <= 20
                ? MetNotMetEnum.Met
                : averagePercentage > 50
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response?.healthFacilitiesWithStockOut,
        response?.healthFacilitiesReporting,
    ]);

    //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
    const calculatePercentageRange = (propertyName1: number, propertyName2: number) => {
        const proportionRateValue = calculatePercentage(propertyName1, propertyName2);
        const proportionRateExceptionContent =
            <span className="Mui-error d-flex mb-2">
                * {t("indicators-responses:Common:ResponseProportionError")}
            </span>

        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
            isProportionRateValid = true;
            return proportionRateValue +'%';
        }

        isProportionRateValid = false;
        return proportionRateExceptionContent;
    }
    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_1_3:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="row mt-3 mb-3">
                        <div className="col-md-12">
                            {t(
                                "indicators-responses:DRObjective_3_Responses:Indicator_3_1_3:ResponseDesc"
                            )}
                        </div>
                    </div>
                    <Table width="60%">
                        <>
                            <TableHeader headers={rows.map((row: any) => row.label)} />
                            <TableBody>
                                <>
                                    <TableRow>
                                        <>
                                            <TableCell>
                                                <>ACTs</>
                                            </TableCell>
                                            <TableCell>
                                                <TextBox
                                                    id="healthFacilitiesWithStockOut"
                                                    name="healthFacilitiesWithStockOut"
                                                    type="number"
                                                    fullWidth
                                                    value={response?.healthFacilitiesWithStockOut}
                                                    onChange={onChange}
                                                    error={
                                                        errors["healthFacilitiesWithStockOut"] &&
                                                        errors["healthFacilitiesWithStockOut"]
                                                    }
                                                    helperText={
                                                        errors["healthFacilitiesWithStockOut"] &&
                                                        errors["healthFacilitiesWithStockOut"]
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <TextBox
                                                    id="healthFacilitiesReporting"
                                                    name="healthFacilitiesReporting"
                                                    type="number"
                                                    fullWidth
                                                    value={response?.healthFacilitiesReporting}
                                                    onChange={onChange}
                                                    error={
                                                        errors["healthFacilitiesReporting"] &&
                                                        errors["healthFacilitiesReporting"]
                                                    }
                                                    helperText={
                                                        errors["healthFacilitiesReporting"] &&
                                                        errors["healthFacilitiesReporting"]
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <label>
                                                    {calculatePercentageRange(
                                                        response?.healthFacilitiesWithStockOut,
                                                        response?.healthFacilitiesReporting
                                                    )}
                                                </label>
                                            </TableCell>
                                        </>
                                    </TableRow>
                                </>
                            </TableBody>
                        </>
                    </Table>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_3_1_3_Response;
