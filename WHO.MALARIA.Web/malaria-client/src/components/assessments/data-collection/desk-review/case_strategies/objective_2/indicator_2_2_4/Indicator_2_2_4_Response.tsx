﻿import { Button } from "@mui/material";
import { useEffect, useRef, ChangeEvent } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_4/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 2.2.4 response for desk review */
const Indicator_2_2_4_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_2_Indicator_2_2_4_Title"
    );

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onCannotBeAssessed,
        onChange,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }
    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const masterHealthFacilityListExists = response.masterHealthFacilityListExists;

        onValueChange(
            "metNotMetStatus",
            masterHealthFacilityListExists === true ? MetNotMetEnum.Met : MetNotMetEnum.NotMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [response.masterHealthFacilityListExists]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_2_4:MetNotMetTooltip"
                )}
            />

         <div className="response-assess-wrapper">
            <Checkbox
                id="cannotBeAssessed"
                name="cannotBeAssessed"
                label={t("indicators-responses:Common:IndicatorNoAssess")}
                onChange={onCannotBeAssessedChange}
                checked={response?.cannotBeAssessed}
            />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="response-content">
                        <div className="row">
                            <div className="col-xs-12 col-md-12">
                                <p>
                                    <i>
                                        {t(
                                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_4:BothSectionRequired"
                                        )}
                                    </i>
                                </p>
                                <p className="my-1">
                                    {t(
                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_4:MFLExist"
                                    )}
                                </p>
                                <RadioButtonGroup
                                    id="masterHealthFacilityListExists"
                                    name="masterHealthFacilityListExists"
                                    row
                                    color="primary"
                                    options={[
                                        new MultiSelectModel(
                                            true,
                                            t("indicators-responses:Common:Yes")
                                        ),
                                        new MultiSelectModel(
                                            false,
                                            t("indicators-responses:Common:No")
                                        ),
                                    ]}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                        onChange(e);
                                        getMetNotMetStatus();
                                    }}
                                    value={response?.masterHealthFacilityListExists}
                                    error={
                                        errors["masterHealthFacilityListExists"] &&
                                        errors["masterHealthFacilityListExists"]
                                    }
                                    helperText={
                                        errors["masterHealthFacilityListExists"] &&
                                        errors["masterHealthFacilityListExists"]
                                    }
                                />
                                <div className="my-4">
                                    <TextBox
                                        id="masterHealthFacilityIdentifierDetails"
                                        name="masterHealthFacilityIdentifierDetails"
                                        label={t("indicators-responses:Common:ProvideDetails")}
                                        fullWidth
                                        multiline
                                        rows={5}
                                        maxLength={1000}
                                        onChange={onChange}
                                        value={
                                            response?.masterHealthFacilityIdentifierDetails || ""
                                        }
                                        error={
                                            errors["masterHealthFacilityIdentifierDetails"] &&
                                            errors["masterHealthFacilityIdentifierDetails"]
                                        }
                                        helperText={
                                            errors["masterHealthFacilityIdentifierDetails"] &&
                                            errors["masterHealthFacilityIdentifierDetails"]
                                        }
                                    />
                                </div>
                                <p className="my-1">
                                    {t(
                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_4:SameMasterFacility"
                                    )}
                                </p>
                                <RadioButtonGroup
                                    id="masterHealthFacilityExists"
                                    name="masterHealthFacilityExists"
                                    row
                                    color="primary"
                                    options={[
                                        new MultiSelectModel(
                                            true,
                                            t("indicators-responses:Common:Yes")
                                        ),
                                        new MultiSelectModel(
                                            false,
                                            t("indicators-responses:Common:No")
                                        ),
                                    ]}
                                    onChange={onChange}
                                    value={response?.masterHealthFacilityExists}
                                    error={
                                        errors["masterHealthFacilityExists"] &&
                                        errors["masterHealthFacilityExists"]
                                    }
                                    helperText={
                                        errors["masterHealthFacilityExists"] &&
                                        errors["masterHealthFacilityExists"]
                                    }
                                />
                                <div className="my-4">
                                    <TextBox
                                        id="masterHealthFacilityDetails"
                                        name="masterHealthFacilityDetails"
                                        label={t("indicators-responses:Common:ProvideDetails")}
                                        fullWidth
                                        multiline
                                        rows={5}
                                        maxLength={1000}
                                        onChange={onChange}
                                        value={response?.masterHealthFacilityDetails || ""}
                                        error={
                                            errors["masterHealthFacilityDetails"] &&
                                            errors["masterHealthFacilityDetails"]
                                        }
                                        helperText={
                                            errors["masterHealthFacilityDetails"] &&
                                            errors["masterHealthFacilityDetails"]
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        onChange={onChange}
                        value={response?.cannotBeAssessedReason || ""}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_2_2_4_Response;
