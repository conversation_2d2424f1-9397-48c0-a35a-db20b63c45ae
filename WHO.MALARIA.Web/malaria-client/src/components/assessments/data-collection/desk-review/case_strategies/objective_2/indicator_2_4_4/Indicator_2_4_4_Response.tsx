﻿import { Button } from "@mui/material";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import { useEffect, useRef } from "react";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_4_4/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import ValidationRules from "./ValidationRules";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";

/** Renders the indicator 2.4.4 response for desk review */
const Indicator_2_4_4_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);

  document.title = t(
    "indicators-responses:app:DR_Objective_2_Indicator_2_4_4_Title"
  );
  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
  const validate = useFormValidation(validationRulesRef.current);
  const errors = useSelector((state: any) => state.error);

  const { response, onChange, onSave, onFinalize, getResponse, setTrueFlagOnFinalizeButtonClick } =
    useIndicatorResponseCapture<Response_1>(Response_1.init());

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid) {
      onFinalize();
    }
  };

  useEffect(() => {
    getResponse();
  }, []);

  return (
    <>
      <div className="response-wrapper">
        <div className="response-content">
          <div className="row">
            <div className="col-xs-12 col-md-12">
              <label>
                {t(
                  "indicators-responses:DRObjective_2_Responses:Indicator_2_4_4:ResponseDesc"
                )}
              </label>
              <div className="my-5">
                <TextBox
                  id="plannedAllocation"
                  name="plannedAllocation"
                  onChange={onChange}
                  value={response?.plannedAllocation || ""}
                  label={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_4:PlannedAllocation"
                  )}
                  fullWidth
                  multiline
                  rows={5}
                  maxLength={1000}
                  error={
                    errors["plannedAllocation"] &&
                    errors["plannedAllocation"]
                  }
                  helperText={
                    errors["plannedAllocation"] &&
                    errors["plannedAllocation"]
                  }
                />
              </div>
              <div className="my-5">
                <TextBox
                  id="procedureForTroubleShooting"
                  name="procedureForTroubleShooting"
                  onChange={onChange}
                  value={response?.procedureForTroubleShooting || ""}
                  label={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_4:ProcedureForTroubleshooting"
                  )}
                  fullWidth
                  multiline
                  rows={5}
                  maxLength={1000}
                  error={
                    errors["procedureForTroubleShooting"] &&
                    errors["procedureForTroubleShooting"]
                  }
                  helperText={
                    errors["procedureForTroubleShooting"] &&
                    errors["procedureForTroubleShooting"]
                  }
                />
              </div>
              <div className="my-5">
                <TextBox
                  id="discrepanciesInThePlan"
                  name="discrepanciesInThePlan"
                  onChange={onChange}
                  value={response?.discrepanciesInThePlan || ""}
                  label={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_4_4:Discrepancies"
                  )}
                  fullWidth
                  multiline
                  rows={5}
                  maxLength={1000}
                  error={
                    errors["discrepanciesInThePlan"] &&
                    errors["discrepanciesInThePlan"]
                  }
                  helperText={
                    errors["discrepanciesInThePlan"] &&
                    errors["discrepanciesInThePlan"]
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
    </>
  );
};

export default Indicator_2_4_4_Response;
