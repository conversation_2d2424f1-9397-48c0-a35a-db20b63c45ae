﻿import { useState, ChangeEvent, useEffect } from "react";
import <PERSON><PERSON><PERSON> from "../../../../../controls/BarChart";
import { Button, IconButton, Table, TableBody } from "@mui/material";
import TextBox from "../../../../../controls/TextBox";
import { Delete, Add } from "@mui/icons-material";
import * as React from "react";
import { useTranslation } from "react-i18next";
import TableHeader from "../../responses/TableHeader";
import { KeyValuePair } from "../../../../../../models/DeskReview/KeyValueType";

//Interface for IndicatorBarGraph component
interface BarGraphProps {
    description?: React.ReactElement;
    barGraphXAxisText: string;
    barGraphYAxisText: string;
    graphTitle?: string;
    renderTextbox?: boolean;
    keyValuePairs: Array<KeyValuePair<any, any>>;
    details: string;
    showGraphOnLoad?: boolean;
    onUpdatekeyValuePairs: (keyValuePairs: Array<KeyValuePair<any, any>>) => void;
    onDetailChange?: (details: string) => void;
    /**Must contain errors in an array of key value pair  */
    keyValuePairsErrors?: any;
    /**Must contain error in a string format for the detail textbox*/
    errorInDetails?: string;
    getMetNotMetStatusFn?: () => void;
    className?: string;
}

/** Renders the response for indicator 1.1.1 Bar Graph */
const IndicatorBarGraph = (props: BarGraphProps) => {
    const {
        keyValuePairs,
        details,
        showGraphOnLoad,
        onUpdatekeyValuePairs,
        onDetailChange,
        graphTitle,
        getMetNotMetStatusFn,
        className
    } = props;

    const { t } = useTranslation(["indicators-responses"]);

    const barType = "column";
    const {
        barGraphXAxisText,
        barGraphYAxisText,
        renderTextbox,
        keyValuePairsErrors,
        errorInDetails,
    } = props;
    // BAR chart y-axis tiles
    const [barChartCategories, setBarChartCategories] = useState<
        Array<string | number>
    >([]);

    // BAR chart data array
    const [barData, setBarData] = useState<Array<Array<number>>>([]);

    // BAR ChartCategoryAxisTitle
    const [chartCategoryAxisTitle, setChartCategoryAxisTitle] = useState("");

    // BAR chart title
    const chartTitle = graphTitle
        ? graphTitle
        : t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:NationalLevelEstimate"
        );

    useEffect(() => {
        //Generate the graph only when "showGraphOnLoad" is set to TRUE
        showGraphOnLoad && generateBarGraphHandler();
    }, [showGraphOnLoad]);

    //OnChange Handler for the table inputs
    const onChangeHandler = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
        index: number
    ) => {
        const values = [...keyValuePairs];

        //Update KEY if key textbox's value changes else change the VALUE
        if (e.target.name === "key") {
            values[index].key = e.target.value;
        } else {
            values[index].value = e.target.value;
        }

        onUpdatekeyValuePairs(values);
    };

    //Triggers whenever 'Details' textarea's value changes
    const detailsChangeHandler = (
        e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        if (onDetailChange) onDetailChange(e.target.value);
    };

    // Triggered whenever 'Delete Row' button is clicked.
    const onRowDelete = () => {
        onUpdatekeyValuePairs(keyValuePairs.slice(0, keyValuePairs.length - 1));
    };

    // Triggered whenever 'Add Row' button is clicked.
    const onRowAdd = () => {
        const values = [...keyValuePairs, { key: null, value: null }];
        onUpdatekeyValuePairs(values);
    };

    //Generate Bar graph handler
    const generateBarGraphHandler = () => {
        setChartCategoryAxisTitle(barGraphXAxisText);
        const rows = graphPayload() || [];
        const regionData = rows.map((item) => item.region);
        const estimateData = rows.map((item) => item.estimate);
        setBarChartCategories(regionData);
        setBarData([estimateData]);
    };

    //Returns the data for the row of table
    const graphPayload = () => {
        const payload = [];
        const regionNodes = document.querySelectorAll('[name="key"]');
        const estimateNodes = document.querySelectorAll('[name="value"]');
        if (regionNodes && estimateNodes) {
            for (let index = 0; index < regionNodes.length; index++) {
                payload.push({
                    region: (regionNodes[index] as HTMLInputElement).value,
                    estimate: +(estimateNodes[index] as HTMLInputElement).value,
                });
            }

            return payload;
        }
    };

    return (
        <>
            {props.description}
            <div className="row graph-wrapper">
                <div className="col-xs-12 col-md-4">
                    <Table width="100%" className={className ? "app-table app-table-scroll" : "app-table app-table-sticky-scroll"}>
                        <>
                            <TableHeader headers={[barGraphXAxisText, barGraphYAxisText]} />
                            <TableBody>
                                <>
                                    {keyValuePairs.map((keyValuePair, index: number) => {
                                        return (
                                            <tr key={index}>
                                                <td>
                                                    <TextBox
                                                        className="col-form-control inputfocus"
                                                        type="text"
                                                        name="key"
                                                        placeholder={t("indicators-responses:Common:Region")}
                                                        value={keyValuePair.key}
                                                        onChange={(e) => {
                                                            onChangeHandler(e, index);
                                                            if (getMetNotMetStatusFn) getMetNotMetStatusFn();
                                                        }}
                                                        error={
                                                            keyValuePairsErrors &&
                                                            keyValuePairsErrors[index]?.key
                                                        }
                                                        helperText={
                                                            keyValuePairsErrors &&
                                                            keyValuePairsErrors[index]?.key
                                                        }
                                                    />
                                                </td>
                                                <td>
                                                    <TextBox
                                                        className="col-form-control inputfocus"
                                                        fullWidth
                                                        inputProps={{ max: 100, min: 0 }}
                                                        maxLength={3}
                                                        type="number"
                                                        name="value"
                                                        placeholder={t("indicators-responses:Common:Percent")}
                                                        value={keyValuePair.value}
                                                        error={
                                                            keyValuePairsErrors &&
                                                           keyValuePairsErrors[index]?.value
                                                        }
                                                        helperText={
                                                            keyValuePairsErrors &&
                                                            keyValuePairsErrors[index]?.value
                                                        }
                                                        onChange={(e) => {
                                                            onChangeHandler(e, index);
                                                            if (getMetNotMetStatusFn) getMetNotMetStatusFn();
                                                        }}
                                                    />
                                                </td>
                                            </tr>
                                        );
                                    })}
                                    <tr>
                                        <td>
                                            {keyValuePairs.length > 1 && (
                                                <IconButton onClick={onRowDelete}>
                                                    <Delete />
                                                </IconButton>
                                            )}
                                            <IconButton onClick={onRowAdd}>
                                                <Add />
                                            </IconButton>
                                        </td>
                                    </tr>
                                </>
                            </TableBody>
                        </>
                    </Table>
                    <Button
                        className="btn app-btn-secondary app-btn-full"
                        onClick={generateBarGraphHandler}
                    >
                        {t("indicators-responses:Common:GenerateGraph")}
                    </Button>
                </div>
                {/* BAR CHART */}
                <div className="col-xs-12 col-md-8">
                    <BarChart
                        chartTitle={chartTitle}
                        displayType={barType}
                        chartCategoryAxisTitle={chartCategoryAxisTitle}
                        barChartCategories={barChartCategories}
                        barData={barData}
                    />
                </div>
                <div className="col-xs-12 col-md-12 mt-3">
                    {renderTextbox && keyValuePairs.length > 1 && (
                        <TextBox
                            label={t("indicators-responses:Common:ReasonForChangesByRegion")}
                            fullWidth
                            multiline
                            rows={5}
                            maxLength={1000}
                            value={details}
                            error={errorInDetails ? true : false}
                            helperText={errorInDetails}
                            onChange={detailsChangeHandler}
                        />
                    )}
                </div>
            </div>
        </>
    );
};

export default IndicatorBarGraph;
