﻿import { Button } from "@mui/material";
import { useEffect } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_1_3/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 4.1.3 response for desk review */
const Indicator_4_1_3_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_4_Indicator_4_1_3_Title"
  );

  const { response, onChange, onSave, onFinalize, getResponse } =
    useIndicatorResponseCapture<Response_1>(new Response_1(null, null));

  useEffect(() => {
    getResponse();
  }, []);


  return (
    <>
      <div className="response-wrapper">
        <div className="row mb-3">
          <div className="col-xs-12 col-md-8">
            <div className="row mb-3">
              <div className="col-xs-12 col-md-12">
                <label>
                  {t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_1_3:DataQualityTargets"
                  )}
                </label>
                <RadioButtonGroup
                  id="isSetDataQualityTarget"
                  name="isSetDataQualityTarget"
                  row
                  color="primary"
                  options={[
                    new MultiSelectModel(
                      true,
                      t("indicators-responses:Common:Yes")
                    ),
                    new MultiSelectModel(
                      false,
                      t("indicators-responses:Common:No")
                    ),
                  ]}
                  value={response?.isSetDataQualityTarget}
                  onChange={onChange}
                />
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 col-md-12">
                <TextBox
                  id="details"
                  name="details"
                  label={t("indicators-responses:Common:Details")}
                  placeholder={t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_1_3:DetailsPlaceholder"
                  )}
                  multiline
                  rows={10}
                  variant="outlined"
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={response?.details}
                  onChange={onChange}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <SaveFinalizeButton onSave={onSave} onFinalize={onFinalize} />
    </>
  );
};

export default Indicator_4_1_3_Response;
