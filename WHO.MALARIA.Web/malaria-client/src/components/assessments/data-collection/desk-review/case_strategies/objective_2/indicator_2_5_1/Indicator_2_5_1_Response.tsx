﻿import { <PERSON><PERSON>, InputAdornment } from "@mui/material";
import React, { useEffect } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableFooter from "../../../responses/TableFooter";
import TableRow from "../../../responses/TableRow";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_5_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 2.5.1 response for desk review */
const Indicator_2_5_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);

    document.title = t(
        "indicators-responses:app:DR_Objective_1_Indicator_2_5_1_Title"
    );

    const { calculatePercentageGap } = useCalculation();

    const { response, onChangeWithKey, getResponse, onSave, onFinalize } =
        useIndicatorResponseCapture<Response_1>(Response_1.init());

    useEffect(() => {
        getResponse();
    }, []);

    const headers = [
        {
            field: "",
            label: "",
        },
        {
            field: "currency",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:Currency"
            ),
        },
        {
            field: "value",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:Value"
            ),
        },
    ];

    const columns: any = {
        totalEstimated: t(
            "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:EstimatedNationalBudget"
        ),
        fundsAvailable: t(
            "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:FundsAvailableForMalariaSurveillance"
        ),
        domesticFunds: t(
            "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:DomesticFunds"
        ),
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:ResponseDesc"
                    )}
                </p>

                <Table>
                    <>
                        <TableHeader headers={headers.map((header: any) => header.label)} />
                        <TableBody>
                            <>
                                {Object.keys(response).map(
                                    (modelKeyName: string, index: number) => (
                                        <>
                                            <TableRow key={`row_${modelKeyName}_${index}`}>
                                                <>
                                                    <TableCell>{columns[modelKeyName]}</TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="currency"
                                                            name="currency"
                                                            type="text"
                                                            fullWidth
                                                            placeholder="e.g. $"
                                                            value={response[modelKeyName]?.currency}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="value"
                                                            name="value"
                                                            type="number"
                                                            fullWidth
                                                            value={response[modelKeyName]?.value}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        </>
                                    )
                                )}
                            </>
                        </TableBody>
                        <TableFooter>
                            <>
                                <TableCell>
                                    <span>
                                        {t(
                                            "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:PercentageDifference"
                                        )}
                                    </span>
                                </TableCell>
                                <TableCell>
                                    <></>
                                </TableCell>
                                <TableCell>
                                    <label>
                                        {calculatePercentageGap(
                                            response["totalEstimated"]?.value,
                                            response["fundsAvailable"]?.value
                                        )}
                    %
                  </label>
                                </TableCell>
                                <TableCell>
                                    <></>
                                </TableCell>
                            </>
                        </TableFooter>
                    </>
                </Table>

                <div className="px-2">
                    <p className="fst-italic">{t("indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:PercentageDifferenceNote")}</p>
                </div>
            </div>

            <SaveFinalizeButton onSave={onSave} onFinalize={onFinalize}></SaveFinalizeButton>

        </>
    );
};

export default Indicator_2_5_1_Response;
