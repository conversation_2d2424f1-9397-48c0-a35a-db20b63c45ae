﻿import { But<PERSON> } from "@mui/material";
import { useEffect } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_1_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

type Indicator4_1_2Props = {};

/** Renders the indicator 4.1.2 response for desk review */
const Indicator_4_1_2_Response = (props: Indicator4_1_2Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_4_Indicator_4_1_2_Title"
  );

  const { response, onChange, onSave, onFinalize, getResponse } =
    useIndicatorResponseCapture<Response_1>(Response_1.init());

  useEffect(() => {
    getResponse();
  }, []);


  return (
    <>
      <div className="response-wrapper">
        <div className="row mb-3">
          <div className="col-xs-12 col-md-8">
            <div className="row mb-3">
              <div className="col-xs-12 col-md-12">
                <label>
                  {t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_1_2:IdentifyStrategicPlanningDocuments"
                  )}
                </label>
                <RadioButtonGroup
                  id="isStrategicPlanningDocumentsAvailable"
                  name="isStrategicPlanningDocumentsAvailable"
                  row
                  color="primary"
                  options={[
                    new MultiSelectModel(
                      true,
                      t("indicators-responses:Common:Yes")
                    ),
                    new MultiSelectModel(
                      false,
                      t("indicators-responses:Common:No")
                    ),
                  ]}
                  value={response?.isStrategicPlanningDocumentsAvailable}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onChange(e)
                  }
                />
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 col-md-12">
                <TextBox
                  id="strategicPlanningDocumentsAvailableDetails"
                  name="strategicPlanningDocumentsAvailableDetails"
                  label={t("indicators-responses:Common:Details")}
                  placeholder={t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_1_2:DetailsPlaceholder"
                  )}
                  multiline
                  rows={10}
                  variant="outlined"
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={response?.strategicPlanningDocumentsAvailableDetails}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onChange(e)
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <SaveFinalizeButton onSave={onSave} onFinalize={onFinalize} />
    </>
  );
};

export default Indicator_4_1_2_Response;
