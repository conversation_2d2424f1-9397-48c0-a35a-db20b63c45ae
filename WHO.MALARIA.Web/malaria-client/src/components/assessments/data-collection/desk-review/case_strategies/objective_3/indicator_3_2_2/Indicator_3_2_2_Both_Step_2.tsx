﻿import { TableRow } from "@mui/material";
import { useTranslation } from "react-i18next";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import { Step_B_Response } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_2/Response_1";
import { useSelector } from "react-redux";

type Indicator_3_2_2_Props = {
    step_B: Step_B_Response;
    updateStep_B: (step_B: Step_B_Response) => void;
};

/** Renders the response for indicator 3.2.2 Step 2 for both strategies (combination of burden reduction and elimination)*/
function Indicator_3_2_2_Both_Step_2(props: Indicator_3_2_2_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_B, updateStep_B } = props;
    const errors = useSelector((state: any) => state.error);

    // Triggered whenever the textbox control values are changed and update response
    const onValueChange = (
        fieldName: string,
        value: any,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_B,
            [modelKeyName]: {
                ...step_B[modelKeyName],
                [fieldName]: value,
            },
        };

        updateStep_B(_response);
    };

    // Triggered whenever the radio control values are changed and update response
    const onRadioButtonValueChange = (
        fieldName: string,
        value: any,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_B,
            [modelKeyName]: {
                ...step_B[modelKeyName],
                [fieldName]: value === "false" ? false : true,
            },
        };

        updateStep_B(_response);
    };

    const headers = [
        {
            field: "assessment",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Assessment"
            ),
        },
        {
            field: "checklist",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Checklist"
            ),
        },
        {
            field: "details",
            label: t("indicators-responses:Common:Details"),
        },
    ];

    // Excluded property that are not used in Array Map
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
    ];

    const columns: any = {
        correctlyDistinguisedPresumedCase: {
            field: "correctlyDistinguisedPresumedCase",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:CorrectlyDistinguisedPresumedCases"
            ),
        },
        rdtConfirmCase: {
            field: "rdtConfirmCase",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:RDTConfirmCases"
            ),
        },
        falciparumInfection: {
            field: "falciparumInfection",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:FaclciparumInfections"
            ),
        },
        indigenousCase: {
            field: "indigenousCase",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:IndigenousCases"
            ),
        },
        identifiedCasesThroughProactiveCaseDetection: {
            field: "identifiedCasesThroughProactiveCaseDetection",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:IndifiedCasesThroughProactiveCaseDetection"
            ),
        },
    };

    return (
        <>
            <div className="response-wrapper">
                {
                    //Show error message if user does not select 'Yes' or 'No' for assessment checklist (Part B) and does not provide details after selecting 'No' 
                    !!Object.keys(errors).length && (
                        <span className="Mui-error d-flex mb-2">
                            * {t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:ResponseErrorStepB")}
                        </span>)
                }
                <Table width="65%">
                    <>
                        <TableHead>
                            {headers.map((header: any) => (
                                <TableHeaderCell>
                                    <label>{header.label}</label>
                                </TableHeaderCell>
                            ))}
                        </TableHead>
                        <TableBody>
                            <>
                                {Object.keys(step_B)
                                    .filter((key) => !excludedProperties.includes(key))
                                    .map((modelKeyName: string, index: number) => (
                                        <TableRow key={`row_${columns.field}_${index}`}>
                                            <>
                                                <TableCell width="500px">
                                                    <span>{columns[modelKeyName]?.label}</span>
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="checklist"
                                                        name="checklist"
                                                        row
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={step_B[modelKeyName]?.checklist}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) =>
                                                            onRadioButtonValueChange(
                                                                "checklist",
                                                                e.currentTarget.value,
                                                                modelKeyName
                                                            )
                                                        }
                                                        error={errors[`${modelKeyName}.checklist`] && errors[`${modelKeyName}.checklist`]}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${columns.field}_${index}_${Math.random()}`}
                                                        name="details"
                                                        rows={2}
                                                        disabled={step_B[modelKeyName]?.checklist}
                                                        multiline
                                                        fullWidth
                                                        value={step_B[modelKeyName]?.details}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) =>
                                                            onValueChange(
                                                                "details",
                                                                e.currentTarget.value,
                                                                modelKeyName
                                                            )
                                                        }
                                                        error={errors[`step_B.${modelKeyName}.details`] && errors[`step_B.${modelKeyName}.details`]}
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                            </>
                        </TableBody>
                    </>
                </Table>
            </div>
        </>
    );
}

export default Indicator_3_2_2_Both_Step_2;
