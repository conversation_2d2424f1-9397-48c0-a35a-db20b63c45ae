﻿import { Button } from "@mui/material";
import React, { useEffect, useRef, ChangeEvent } from "react";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import useYears from "../../../../useYears";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Dropdown from "../../../../../../controls/Dropdown";
import IndicatorBarGraph from "../IndicatorBarGraph";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_6/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 1.1.6 response for desk review */
const Indicator_1_1_6_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_1_6_Title");

    const years = useYears();
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onCannotBeAssessed,
        onChange,
        getResponse,
        onValueChange,
        onSave,
        onFinalize,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }
    //Update reason for changes observed overtime
    const onDetailChange = (reasonForChangeObservedOvertime: string) => {
        onValueChange("graph", {
            ...response.graph,
            reasonForChangeObservedOvertime,
        });
    };

    //Updates national level estimates
    const onNationalLevelEstimateChange = (
        nationalEstimates: Array<KeyValuePair<any, any>>
    ) => {
        let values;

        //Convert key and values in number format as to align with model
        values = nationalEstimates.map((value) => ({
            key: value.key ? value.key : null,
            value: value.value ? (+value.value) : null,
        }));

        onValueChange("graph", {
            values,
            reasonForChangeObservedOvertime:
                nationalEstimates.length > 1
                    ? response.graph.reasonForChangeObservedOvertime
                    : "",
        });
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const nationalDataRate = response?.nationalData;

        onValueChange(
            "metNotMetStatus",
            nationalDataRate >= 80
                ? MetNotMetEnum.Met
                : nationalDataRate < 50
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [response?.nationalData]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_6:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_6:ResponseDesc"
                        )}
                    </p>
                    <i>
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_6:DisaggregatedByGeographicalArea"
                        )}
                    </i>
                    <i>{t("indicators-responses:Common:NationalLevelDataRequired")}</i>
                    <br /> <br />
                    {/*Show error message if not all radio buttons are selected for Data quality control check in place*/}
                    {(errors["nationalData"] || errors["graph.values"]) && (
                        <span className="Mui-error d-flex mb-2">
                            *
                            {t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_5:ResponseError"
                            )}
                        </span>
                    )}
                    <div className="response-content">
                        <div className="row mt-3 mb-3">
                            <div className="col-xs-12 col-md-6">
                                <div className="row">
                                    <div className="col-xs-12 col-md-4">
                                        <TextBox
                                            id="nationalData"
                                            name="nationalData"
                                            label={t("indicators-responses:Common:National")}
                                            type="number"
                                            className="col-form-control inputfocus"
                                            InputLabelProps={{ required: true, shrink: true }}
                                            inputProps={{
                                                max: 100,
                                                min: 0,
                                            }}
                                            maxLength={3}
                                            placeholder="(%)"
                                            fullWidth
                                            value={response?.nationalData}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                onChange(e);
                                                getMetNotMetStatus();
                                            }}
                                            error={errors["nationalData"] && errors["nationalData"]}
                                            helperText={
                                                errors["nationalData"] && errors["nationalData"]
                                            }
                                        />
                                    </div>

                                    <div className="col-xs-12 col-md-4">
                                        <TextBox
                                            id="publicData"
                                            name="publicData"
                                            label={t("indicators-responses:Common:Public")}
                                            type="number"
                                            className="col-form-control inputfocus"
                                            InputLabelProps={{ shrink: true }}
                                            inputProps={{
                                                max: 100,
                                                min: 0,
                                            }}
                                            maxLength={3}
                                            placeholder="(%)"
                                            fullWidth
                                            value={response?.publicData}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                onChange(e)
                                            }
                                            error={errors["publicData"] && errors["publicData"]}
                                            helperText={errors["publicData"] && errors["publicData"]}
                                        />
                                    </div>

                                    <div className="col-xs-12 col-md-4">
                                        <TextBox
                                            id="privateData"
                                            name="privateData"
                                            label={t("indicators-responses:Common:Private")}
                                            type="number"
                                            className="col-form-control inputfocus"
                                            InputLabelProps={{ shrink: true }}
                                            inputProps={{
                                                max: 100,
                                                min: 0,
                                            }}
                                            maxLength={3}
                                            placeholder="(%)"
                                            fullWidth
                                            value={response?.privateData}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                onChange(e)
                                            }
                                            error={errors["privateData"] && errors["privateData"]}
                                            helperText={
                                                errors["privateData"] && errors["privateData"]
                                            }
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <p>
                            {t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_6:IndicateRecentYearDataText"
                            )}
                        </p>
                        <div className="col-xs-12 col-md-4">
                            <div className="row mb-2">
                                <div className="col-xs-12 col-md-6">
                                    <Dropdown
                                        id="yearOfData"
                                        name="yearOfData"
                                        variant="outlined"
                                        size="small"
                                        label={t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:YearOfData"
                                        )}
                                        value={response?.yearOfData}
                                        options={years.map((year) => {
                                            return new MultiSelectModel(
                                                year,
                                                year.toString(),
                                                false,
                                                false
                                            );
                                        })}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            onValueChange(
                                                "yearOfData",
                                                +e.currentTarget.value
                                            )
                                        }
                                        error={errors["yearOfData"] && errors["yearOfData"]}
                                        helperText={
                                            errors["yearOfData"] && errors["yearOfData"]
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                        <IndicatorBarGraph
                            barGraphXAxisText={t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:Region"
                            )}
                            barGraphYAxisText="%"
                            renderTextbox={true}
                            keyValuePairs={response?.graph?.values}
                            onUpdatekeyValuePairs={onNationalLevelEstimateChange}
                            details={response?.graph?.reasonForChangeObservedOvertime}
                            onDetailChange={onDetailChange}
                            showGraphOnLoad={response?.graph?.values?.length > 0}
                            graphTitle={`${t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_6:ProportionOfCases24"
                            )} (${response?.yearOfData || ""})`}
                            keyValuePairsErrors={errors["graph.values"]}
                        />
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e)}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_1_1_6_Response;
