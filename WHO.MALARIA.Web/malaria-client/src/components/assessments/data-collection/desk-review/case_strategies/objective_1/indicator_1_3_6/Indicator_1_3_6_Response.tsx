﻿import { <PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableBody from "../../../responses/TableBody";
import IndicatorLineGraph from "../IndicatorLineGraph";
import useMonths from "../../../../../../../services/commonService";
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_6/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import Dropdown from "../../../../../../controls/Dropdown";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import useYears from "../../../../useYears";

/** Renders the response for indicator 1.3.6 */
const Indicator_1_3_6_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_3_6_Title");
    const months = useMonths().map((item) => {
        return { key: item.key, value: null };
    });
    const years = useYears();
    const { calculatePercentage } = useCalculation();
    const [isFirstTimeLoad, setIsFirstTimeLoad] = useState<boolean>(true);
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChangeWithKey,
        onSave,
        onFinalize,
        onValueChange,
        getResponse,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(
        Response_1.init(months),
        validate
    );

    const errors = useSelector((state: any) => state.error);

    // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
    let isProportionRateValid: boolean = true;

    useEffect(() => {
        getResponse();
    }, []);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid && isProportionRateValid) {
            onFinalize();
        }
    };

    // Triggers when input control's value changes
    const onCaseClassificationChange = (
        caseClassificationRates: Array<KeyValuePair<string, number | null>>
    ) => {
        const classificationValue: Array<KeyValuePair<string, number | null>> =
            caseClassificationRates.map(
                (dataValue: KeyValuePair<string, number | null>) => ({
                    key: dataValue.key,
                    value: dataValue.value ? Math.round(+dataValue.value) : dataValue.value === 0 ? 0 : null,
                })
            );
        onValueChange("caseClassificationRates", classificationValue);
        setIsFirstTimeLoad(false);
    };

    // Check Value in Classfication for reload graph on load
    const checkValueInCaseClassification = (): boolean => {
        let checkValue: boolean = false;
        if (isFirstTimeLoad) {
            response.caseClassificationRates.some(function (
                classiFicationvalue: KeyValuePair<string, number | null>
            ) {
                if (classiFicationvalue.value) {
                    return (checkValue = true);
                }
            });
        }

        return checkValue;
    };

    //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
    const calculatePercentageRange = (propertyName1: number, propertyName2: number) => {
        const proportionRateValue = calculatePercentage(propertyName1, propertyName2);

        const proportionRateExceptionContent =
            <span className="Mui-error d-flex mb-2">
                * {t("indicators-responses:Common:ResponseProportionError")}
            </span>

        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
            isProportionRateValid = true;
            return proportionRateValue + '%';
        }

        isProportionRateValid = false;
        return proportionRateExceptionContent;
    }

    return (
        <>
            <div className="response-wrapper">
                <p className="fw-lighter">
                    {t(
                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:ResponseDesc"
                    )}
                </p>
                <p>
                    {t(
                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:SectionShouldBeCompleted"
                    )}
                </p>
                <div className="mt-3">
                    <table  className="app-table minWidth">
                        <thead>
                            <th>
                                <div className="fw-bold"></div>
                            </th>
                            <th>
                                <div className="fw-bold">
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:NoOfFociCaseNumerator"
                                    )}
                                </div>
                            </th>
                            <th>
                                <div className="fw-bold">
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:NoOfFociCaseDenominator"
                                    )}
                                </div>
                            </th>
                            <th>
                                <div className="fw-bold">
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:Rate"
                                    )}
                                </div>
                            </th>
                            <th>
                                <div>
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:FociInvestigation"
                                    )}
                                </div>
                            </th>
                        </thead>
                        <TableBody>
                            <>
                                <TableRow className={!!Object.keys(errors).length ? "app-error" : ""}>
                                    <>
                                        <TableCell>
                                            {t(
                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:FociClassificationRate"
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <TextBox
                                                id="numerator"
                                                name="numerator"
                                                type="number"
                                                fullWidth
                                                className="col-form-control inputfocus"
                                                InputLabelProps={{ shrink: true }}
                                                inputProps={{
                                                    max: 100,
                                                    min: 0,
                                                    maxLength: 3,
                                                }}
                                                value={response["caseClassificationRate"]?.numerator}
                                                onChange={(
                                                    e: React.ChangeEvent<HTMLInputElement>
                                                ) => {
                                                    onChangeWithKey(e, "caseClassificationRate");
                                                }}
                                                error={
                                                    errors["caseClassificationRate.numerator"] &&
                                                    errors["caseClassificationRate.numerator"]
                                                }
                                                helperText={
                                                    errors["caseClassificationRate.numerator"] &&
                                                    errors["caseClassificationRate.numerator"]
                                                }
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <TextBox
                                                id="denominator"
                                                name="denominator"
                                                type="number"
                                                fullWidth
                                                className="col-form-control inputfocus"
                                                InputLabelProps={{ shrink: true }}
                                                inputProps={{
                                                    max: 100,
                                                    min: 0,
                                                    maxLength: 3,
                                                }}
                                                value={
                                                    response["caseClassificationRate"]?.denominator
                                                }
                                                onChange={(
                                                    e: React.ChangeEvent<HTMLInputElement>
                                                ) => {
                                                    onChangeWithKey(e, "caseClassificationRate");
                                                }}
                                                error={
                                                    errors["caseClassificationRate.denominator"] &&
                                                    errors["caseClassificationRate.denominator"]
                                                }
                                                helperText={
                                                    errors["caseClassificationRate.denominator"] &&
                                                    errors["caseClassificationRate.denominator"]
                                                }
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {calculatePercentageRange(
                                                    response["caseClassificationRate"]?.numerator,
                                                    response["caseClassificationRate"]?.denominator
                                                )}
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <TextBox
                                                id="evidenceOfFollowupOrUse"
                                                name="evidenceOfFollowupOrUse"
                                                placeholder={t(
                                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:TextboxLabel"
                                                )}
                                                multiline
                                                fullWidth
                                                rows={2}
                                                variant="outlined"
                                                value={
                                                    response["caseClassificationRate"]
                                                        ?.evidenceOfFollowupOrUse
                                                }
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                    onChangeWithKey(e, "caseClassificationRate")
                                                }
                                                error={
                                                    errors[
                                                    "caseClassificationRate.evidenceOfFollowupOrUse"
                                                    ] &&
                                                    errors[
                                                    "caseClassificationRate.evidenceOfFollowupOrUse"
                                                    ]
                                                }
                                                helperText={
                                                    errors[
                                                    "caseClassificationRate.evidenceOfFollowupOrUse"
                                                    ] &&
                                                    errors[
                                                    "caseClassificationRate.evidenceOfFollowupOrUse"
                                                    ]
                                                }
                                            />
                                        </TableCell>
                                    </>
                                </TableRow>
                            </>
                        </TableBody>
                    </table>
                    <div>
                        <div className="col-xs-12 col-md-4">
                            <div className="row mb-2">
                                <div className="col-xs-12 col-md-6">
                                    <Dropdown
                                        id="yearOfData"
                                        name="yearOfData"
                                        variant="outlined"
                                        size="small"
                                        label={t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:YearOfData"
                                        )}
                                        value={response?.yearOfData}
                                        options={years.map((year) => {
                                            return new MultiSelectModel(
                                                year,
                                                year.toString(),
                                                false,
                                                false
                                            );
                                        })}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            onValueChange(
                                                "yearOfData",
                                                +e.currentTarget.value
                                            )
                                        }
                                        error={errors["yearOfData"] && errors["yearOfData"]}
                                        helperText={
                                            errors["yearOfData"] && errors["yearOfData"]
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                        <IndicatorLineGraph
                            lineGraphXAxisText={t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:Month"
                            )}
                            lineGraphYAxisText={t(
                                "indicators-responses:Common:Rate"
                            )}
                            description={
                                <>
                                    <p></p>
                                    <p className="fw-italic">
                                        {t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:IndicateTheFoci"
                                        )}
                                    </p>
                                </>
                            }
                            renderTextbox={false}
                            keyValuePairs={response.caseClassificationRates}
                            onUpdatekeyValuePairs={onCaseClassificationChange}
                            showGraphOnLoad={checkValueInCaseClassification()}
                            details={""}
                            renderKeysInLabel={true}
                            renderAddRemoveButton={false}
                            graphTitle={`${t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_6:DataForFociClassification"
                            )} (${response?.yearOfData || ""})`}
                            keyValuePairsErrors={
                                errors["caseClassificationRates"] ||
                                errors["caseClassificationRates.value"]
                            }
                            className="app-table-scroll"
                        />
                    </div>
                </div>
            </div>
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_1_3_6_Response;