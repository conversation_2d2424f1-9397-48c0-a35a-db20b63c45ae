import { <PERSON><PERSON><PERSON><PERSON>on } from "@mui/material";
import { Add, Delete } from "@mui/icons-material";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import TableRow from "../../../responses/TableRow";
import {
    Response_1,
    ReportingTool,
} from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_3_1/Response_1";
import {
    Response_1 as Parent,
    NumberOfRecordingForm
} from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { v4 as uuidv4 } from 'uuid';
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import { DeskReviewAssessmentResponseStatus } from "../../../../../../../models/Enums";
import FileUploader from "../../../../../../controls/FileUploader";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import { assessmentService } from "../../../../../../../services/assessmentService";
import { useLocation } from "react-router-dom";
import { DeskReviewRequestModel } from "../../../../../../../models/DeskReview/DeskReviewRequestModel";
import InformationDialog from "../../../../../../common/InformationDialog";
import useFormChangeTracker from "../../../../../../common/useFormChangeTracker";

/** Renders the response indicator 3.3.1 */
function Indicator_3_3_1_Response() {
    const location: any = useLocation();
    const assessmentId = location?.state?.assessmentId;
    const indicatorId = location?.state?.indicatorId;
    const strategyId = location?.state?.strategyId;
    const sequence = location?.state?.sequence;
    const assessmentIndicatorId: string = location?.state?.assessmentIndicatorId;
    const assessmentStrategyId: string = location?.state?.assessmentStrategyId;

    const { t } = useTranslation(["indicators-responses"]);
    const validate = useFormValidation(ValidationRules);
    const [isShowInformationDialog, setIsShowInformationDialog] = useState<boolean>(false);

    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_3_1_Title");

    const {
        response,
        onValueChange,
        onChange,
        onChangeOfArrayWithIndex,
        getResponse,
        setTrueFlagOnFinalizeButtonClick,
        saveResponseAndFiles,
        getResponseDocuments,
        addDeletedDocumentIds
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);
    const { setIsDirtyToFalse } = useFormChangeTracker();

    //Map recording tools with the reporting tools
    const mapRecordingToolsWithReportingTools = (recordingTools: Array<NumberOfRecordingForm>) => {
        const reportingTools = recordingTools.map((data: NumberOfRecordingForm) => new ReportingTool(data.documentId,
            data.nameOfReportingToolSourceDocument,
            data.toolType,
            data.yearToolWasIntroduced,
            null,
            null,
            null,
            data.frequencyDataReported,
            data.personResponsibleForReporting,
            null,
            data.recipientListVariablesReported,
            null,
            null));

        return reportingTools;
    }

    useEffect(() => {
        getResponse();
        getResponseDocuments();
    }, []);

    const headerRows = [
        { field: "", label: "" },
        {
            field: "reportingToolName",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:NameOfReportingToolSourceDocument"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:NameOfReportingToolSourceDocumentPlaceholder"
            ),
        },
        {
            field: "toolType",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:ToolType"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:ToolTypePlaceholder"
            ),
        },
        {
            field: "yearTool",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:YearToolWasIntroduced"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:YearToolPlaceholder"
            ),
        },
        {
            field: "reportingHealthSystemLevel",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:HealthSystemLevelReporting"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:HealthSystemLevelPlaceholder"
            ),
        },
        {
            field: "methodOfReporting",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:MethodOfReportingTransmission"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:MethodOfReportingTransmissionPlaceholder"
            ),
        },
        {
            field: "aggregation",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:Aggregation"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:AggregationPlaceholder"
            ),
        },
        {
            field: "frequencyData",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:FrequencyDataReported"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:CaseByCasePlaceholder"
            ),
        },
        {
            field: "personResponsible",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:PersonResponsibleForReporting"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:RecipientOfReports1Placeholder"
            ),
        },
        {
            field: "reportsRecipient",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:RecipientOfReports1"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:RecipientOfReports2Placeholder"
            ),
        },
        {
            field: "variablesReportedList",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:RecipientListVariablesReported"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:RecipientListVariablesReportedPlaceholder"
            ),
        },
        {
            field: "linkOrScreenshot",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:LinkOrScreenshot"
            ),
            placeholder: "",
        },
        { field: "", label: "" },
    ];

    const addNewRow = () => {
        const reportingTools = [...response?.reportingTools];

        reportingTools.push(
            new ReportingTool(
                uuidv4()
            )
        );
        onValueChange("reportingTools", reportingTools);
    };

    // Remove last record from the recording tools
    const removeRow = (colIndex: number) => {
        if (colIndex === 0)
            // first element
            return;

        const documentId = response.reportingTools[response.reportingTools.length - 1]?.documentId;
        if (documentId) {
            addDeletedDocumentIds(documentId);
        }

        const _response = response.reportingTools.slice(
            0,
            response?.reportingTools.length - 1
        );

        onValueChange("reportingTools", _response);
    };

    // Renders the AddDeleteIcon component
    const AddDeleteIcon = ({ rowIndex = 0, colIndex = 0 }) => {
        return (
            <>
                {colIndex === response?.reportingTools.length - 1 && (
                    <IconButton
                        hidden={rowIndex < headerRows.length - 1}
                        onClick={addNewRow}
                    >
                        <Add className="icon-button-primary" />
                    </IconButton>
                )}

                {colIndex > 0 && colIndex === response?.reportingTools.length - 1 && (
                    <IconButton
                        hidden={rowIndex < headerRows.length - 1}
                        onClick={() => removeRow(colIndex)}
                    >
                        <Delete className="icon-button-primary" />
                    </IconButton>
                )}
            </>
        );
    };

    //Returns file uploader, visibility and delete icon for screenshot
    const AddScreenshotControls = ({ colIndex = 0 }) => {
        const url: string = setScreenshot(colIndex);

        if (url) {
            return <>
                <IconButton title={t("indicators-responses:Common.ClickToView")}>
                    <VisibilityIcon onClick={() => window.open(url)} className="icon-button-primary" />
                </IconButton>
                <IconButton title={t("indicators-responses:Common.Delete")}>
                    <DeleteIcon onClick={() => onFileDelete(colIndex)} className="icon-button-primary" />
                </IconButton>
            </>;
        }

        return <FileUploader
            id={`linkOrScreenshot${colIndex}`}
            multiple
            accept=".png, .jpg, .jpeg"
            onChange={(
                evt: React.ChangeEvent<HTMLInputElement>
            ) => onFileUpload(colIndex, evt)}
        />;

    }

    //Set the validation error in UI for screenshot
    const SetScreenshotError = ({ colIndex = 0 }) => {
        let message: string = "";

        switch (true) {
            case !!errors[`reportingTools[${colIndex}].documentId`]:
                message = errors[`reportingTools[${colIndex}].documentId`];
                break;
            case !!errors[`reportingTools[${colIndex}].file`]:
                message = errors[`reportingTools[${colIndex}].file`]
                break;
        }

        return <span className="Mui-error d-flex mb-2">
            {message}
        </span>
    }

    //Process data on save and calls processDataAndSave method based on the isFinalized flag
    const onSave = () => {
        //If user selects to refer reporting tools that are same as recording tools in an indicator 3.2.1
        if (response.areReportingToolsSameAsRecordingTools) {
            SaveWhenReportingToolsAreSameAsRecordingTools(false);
        } else {
            processDataAndSave(false)
        }
        setIsDirtyToFalse();
    }

    //Process data on finalize and calls processDataAndSave method based on the isFinalized flag
    const onResponseFinalize = () => {
        //If user selects to refer reporting tools that are same as recording tools in an indicator 3.2.1
        if (response.areReportingToolsSameAsRecordingTools) {
            SaveWhenReportingToolsAreSameAsRecordingTools(true);
            setIsShowInformationDialog(true);
        } else {
            processDataAndSave(true)
        }
        setIsDirtyToFalse();
    }

    //Process data set the date control value then save or finalize
    const processDataAndSave = (isFinalized: boolean) => {
        const files: Array<KeyValuePair<string, File>> = response.reportingTools.map((x: ReportingTool) => ({ key: x.documentId, value: x.file }));

        if (isFinalized) {
            const isFormValid = validate(response);
            setTrueFlagOnFinalizeButtonClick();
            if (isFormValid) {
                saveResponseAndFiles(files, DeskReviewAssessmentResponseStatus.Completed)
            }
        } else {
            saveResponseAndFiles(files, DeskReviewAssessmentResponseStatus.InProgress);
        }
    };

    //Save when reporting tools are same as recording tools of indicator 3.2.1
    const SaveWhenReportingToolsAreSameAsRecordingTools = async (isFinalize: boolean) => {
        const parentData: Parent = await assessmentService.getParentIndicatorResponse(
            indicatorId,
            assessmentId,
            assessmentStrategyId
        );

        let reportingTools: Array<ReportingTool>;
        let response: Response_1;

        if (!parentData || !parentData?.recordingTools || parentData.cannotBeAssessed) {
            response = Response_1.init();
            response.areReportingToolsSameAsRecordingTools = true;
            response.isParentCannotBeAssessed = true;
        }
        else {
            reportingTools = mapRecordingToolsWithReportingTools(parentData.recordingTools);
            response = new Response_1(reportingTools, true, null, null);
        }

        const status = isFinalize
            ? DeskReviewAssessmentResponseStatus.Completed
            : DeskReviewAssessmentResponseStatus.InProgress;

        assessmentService.saveIndicatorResponse(
            new DeskReviewRequestModel(
                assessmentId,
                indicatorId,
                strategyId,
                assessmentIndicatorId,
                assessmentStrategyId,
                status,
                JSON.stringify(response),
                sequence
            )
        );
    };

    //Upload screenhot and save in the state
    const onFileUpload = (index: number, evt: React.ChangeEvent<HTMLInputElement>) => {
        if (evt.target.files) {
            const reportingTools = [...response.reportingTools];
            reportingTools[index].documentId = uuidv4();
            reportingTools[index].file = evt.target.files[0];
            onValueChange("reportingTools", reportingTools);
        }
    }

    //Triggers when user click on delete button of File uploader
    const onFileDelete = (index: number): void => {
        const reportingTools = [...response.reportingTools];
        const reportingTool = reportingTools[index];
        const documentId = reportingTool.documentId;

        reportingTool.documentId = "";
        reportingTool.file = null;

        onValueChange("reportingTools", reportingTools);

        if (response?.documents) {
            const documents = response.documents.filter((x: any) => x.id !== documentId);
            onValueChange("documents", documents);
        }

        addDeletedDocumentIds(documentId);
    };

    //Generate URL from uploaded file or file contents which is base64 string
    const setScreenshot = (index: number) => {
        const reportingTool = response?.reportingTools[index];
        const file = reportingTool?.file;
        if (file) {
            return URL.createObjectURL(file);
        }

        //Get file content (base64 string)
        const content = response?.documents?.find((x: any) => x.id === reportingTool?.documentId)?.fileContent;
        if (content) {
            const byteCharacters = atob(content);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });
            return URL.createObjectURL(blob);
        }

        return "";
    }

    return (
        <>
            <div className="response-wrapper">
                <label>
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:ResponseDesc"
                    )}
                </label>
                <RadioButtonGroup
                    id="areReportingToolsSameAsRecordingTools"
                    name="areReportingToolsSameAsRecordingTools"
                    row
                    color="primary"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    value={response?.areReportingToolsSameAsRecordingTools}
                    onChange={onChange}
                />
                {!response.areReportingToolsSameAsRecordingTools ? (
                    <div className="respone-content">
                        <div className="fst-italic">
                            {t(
                                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:NoteInfo"
                            )}
                        </div>
                        <div className="mt-3">
                            {t(
                                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:ToolsUsedToReportData"
                            )}
                        </div>
                        <div className="mt-2 fst-italic">
                            {t("indicators-responses:Common:NoteToCompleteAssessment")}
                        </div>
                        <div className="table-responsive">
                            <Table className="table app-table">
                                <>
                                    <TableHead>
                                        <>
                                            {headerRows.map((header: any, index: number) => (
                                                <TableHeaderCell key={`${header.field}_${index}`}>
                                                    <span className="fw-bold">{header.label}</span>
                                                </TableHeaderCell>
                                            ))}
                                        </>
                                    </TableHead>
                                    <TableBody>
                                        <>
                                            {Object.keys(
                                                response?.reportingTools as [ReportingTool]
                                            ).map((row: any, colIndex: number) => {
                                                return (
                                                    <>
                                                        <TableRow
                                                            key={`row_${colIndex}`}
                                                            className={
                                                                !!Object.keys(errors).length ? "app-error" : ""
                                                            }
                                                        >
                                                            <>
                                                                {headerRows.map(
                                                                    (header: any, rowIndex: number) => (
                                                                        <>
                                                                            {
                                                                                <TableCell
                                                                                    width="300px"
                                                                                    key={header.field + "_" + colIndex}
                                                                                >
                                                                                    <>
                                                                                        <label hidden={rowIndex !== 0}>
                                                                                            {`${t(
                                                                                                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:ReportingTool"
                                                                                            )} ${colIndex + 1}`}
                                                                                        </label>
                                                                                        {header.field ===
                                                                                            "linkOrScreenshot" && (
                                                                                                <>
                                                                                                    <AddScreenshotControls colIndex={colIndex} />
                                                                                                    <SetScreenshotError colIndex={colIndex} />
                                                                                                </>
                                                                                            )}                                                                                       {header.field !==
                                                                                                "linkOrScreenshot" &&
                                                                                                header.field !==
                                                                                                "lastUpdatedDate" && (
                                                                                                    <TextBox
                                                                                                        id={`${header?.field}_${rowIndex}_${colIndex}`}
                                                                                                        name={header.field}
                                                                                                        multiline
                                                                                                        rows={2}
                                                                                                        variant="outlined"
                                                                                                        fullWidth
                                                                                                        hidden={
                                                                                                            rowIndex === 0 ||
                                                                                                            rowIndex ===
                                                                                                            headerRows.length - 1
                                                                                                        }
                                                                                                        maxLength={2000}
                                                                                                        placeholder={
                                                                                                            colIndex === 0
                                                                                                                ? header.placeholder
                                                                                                                : ""
                                                                                                        }
                                                                                                        value={
                                                                                                            response?.reportingTools[
                                                                                                            colIndex
                                                                                                            ]?.[header.field]
                                                                                                        }
                                                                                                        onChange={(
                                                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                                                        ) =>
                                                                                                            onChangeOfArrayWithIndex(
                                                                                                                e,
                                                                                                                "reportingTools",
                                                                                                                colIndex
                                                                                                            )
                                                                                                        }
                                                                                                        error={
                                                                                                            errors[
                                                                                                            `reportingTools[${colIndex}].${header.field}`
                                                                                                            ] &&
                                                                                                            errors[
                                                                                                            `reportingTools[${colIndex}].${header.field}`
                                                                                                            ]
                                                                                                        }
                                                                                                        helperText={
                                                                                                            errors[
                                                                                                            `reportingTools[${colIndex}].${header.field}`
                                                                                                            ] &&
                                                                                                            errors[
                                                                                                            `reportingTools[${colIndex}].${header.field}`
                                                                                                            ]
                                                                                                        }
                                                                                                    />
                                                                                                )}
                                                                                        <AddDeleteIcon
                                                                                            rowIndex={rowIndex}
                                                                                            colIndex={colIndex}
                                                                                        />
                                                                                    </>
                                                                                </TableCell>
                                                                            }
                                                                        </>
                                                                    )
                                                                )}
                                                            </>
                                                        </TableRow>
                                                    </>
                                                );
                                            })}

                                            <TableRow>
                                                <>
                                                    <TableCell colSpan={2}>
                                                        <span>
                                                            {t(
                                                                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:TotalNumberOfReportingTools"
                                                            )}
                                                        </span>
                                                    </TableCell>
                                                    <TableCell colSpan={8}>
                                                        <label>{(response?.reportingTools).length}</label>
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        </>
                                    </TableBody>
                                </>
                            </Table>
                        </div>
                    </div>
                ) : (
                    <p>
                        {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:NotNeedToFill"
                        )}
                    </p>
                )}
            </div>
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
            <InformationDialog
                title={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:AssessChildInformationDialogTitle"
                )}
                content={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:AssessChildInformationDialogContent"
                )}
                open={isShowInformationDialog}
                onClick={() => {
                    setIsShowInformationDialog(false);
                }}
            />
        </>
    );
}

export default Indicator_3_3_1_Response;