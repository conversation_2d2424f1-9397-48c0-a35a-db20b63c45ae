﻿import { But<PERSON> } from "@mui/material";
import React, { useEffect } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableFooter from "../../../responses/TableFooter";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_3_4/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 3.3.4 response for desk review */
const Indicator_3_3_4_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_1_Indicator_3_3_4_Title"
    );
    const { calculatePercentageOfYesNo } = useCalculation();
    const headers = [
        {
            field: "",
            label: "",
        },
        {
            field: "highModerate",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:HighModerate"
            ),
        },
        {
            field: "low",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:Low"
            ),
        },
        {
            field: "veryLow",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:VeryLow"
            ),
        },
        {
            field: "criteriaMet",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:CriteriaMet"
            ),
        },
    ];

    const columns = [
        {
            field: "reportingFrequency",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ReportingFrequency"
            ),
        },
        {
            field: "aggregationOfReportedData",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:AggregationOfReportedData"
            ),
        },
        {
            field: "reportingOfZeroCases",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ReportingOfZeroCases"
            ),
        },
    ];

    const { response, onChange, onSave, onFinalize, getResponse, setTrueFlagOnFinalizeButtonClick } =
        useIndicatorResponseCapture<Response_1>(Response_1.init());

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        onFinalize();
    };

    useEffect(() => {
        getResponse();
    }, []);

    //Method creates an array of properties that have checked "Yes"
    const calculateCheckedForProperty = () => {

        return calculatePercentageOfYesNo([
            response?.reportingFrequencyCriteriaMet,
            response?.aggregationOfReportedDataCriteriaMet,
            response?.reportingOfZeroCasesCriteriaMet,
        ]);
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ResponseDesc"
                    )}
                </p>

                <Table className="app-table app-table-border">
                    <>
                        <TableHeader headers={headers.map((header: any) => header.label)} />
                        <TableBody>
                            <>
                                <TableRow>
                                    <>
                                        <TableCell>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ReportingFrequency"
                                                )}
                                            </>
                                        </TableCell>
                                        <TableCell>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:Monthly"
                                                )}
                                            </>
                                        </TableCell>
                                        <TableCell>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:Weekly"
                                                )}
                                            </>
                                        </TableCell>
                                        <TableCell>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ImmediateCaseNotification"
                                                )}
                                            </>
                                        </TableCell>
                                        <TableCell>
                                            <>
                                                <RadioButtonGroup
                                                    name="reportingFrequencyCriteriaMet"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={response?.reportingFrequencyCriteriaMet}
                                                    onChange={onChange}
                                                />
                                            </>
                                        </TableCell>
                                    </>
                                </TableRow>

                                <TableRow>
                                    <>
                                        <TableCell>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:AggregationOfReportedData"
                                                )}
                                            </>
                                        </TableCell>
                                        <TableCell colSpan={2}>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:AggregateCasesBySexAndAgeCategory"
                                                )}
                                            </>
                                        </TableCell>

                                        <TableCell>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:CaseClassification"
                                                )}
                                            </>
                                        </TableCell>
                                        <TableCell>
                                            <>
                                                <RadioButtonGroup
                                                    name="aggregationOfReportedDataCriteriaMet"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={response?.aggregationOfReportedDataCriteriaMet}
                                                    onChange={onChange}
                                                />
                                            </>
                                        </TableCell>
                                    </>
                                </TableRow>

                                <TableRow>
                                    <>
                                        <TableCell>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ReportingOfZeroCases"
                                                )}
                                            </>
                                        </TableCell>
                                        <TableCell colSpan={3}>
                                            <>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ReportingWhenNoMalariaCases"
                                                )}
                                            </>
                                        </TableCell>

                                        <TableCell>
                                            <>
                                                <RadioButtonGroup
                                                    name="reportingOfZeroCasesCriteriaMet"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={response?.reportingOfZeroCasesCriteriaMet}
                                                    onChange={onChange}
                                                />
                                            </>
                                        </TableCell>
                                    </>
                                </TableRow>
                            </>
                        </TableBody>
                        <TableFooter>
                            <>
                                <TableCell>
                                    <span>
                                        {t(
                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:ProportionOfCrietriaMet"
                                        )}
                                    </span>
                                </TableCell>
                                <TableCell colSpan={4}>
                                    <label>{calculateCheckedForProperty()}%</label>
                                </TableCell>
                            </>
                        </TableFooter>
                    </>
                </Table>
            </div>

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_3_3_4_Response;
