import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, useRef, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableBody from "../../../responses/TableBody";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { useSelector } from "react-redux";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import parse from "html-react-parser";
import useCalculation from "../../../responses/useCalculation";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum, StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router-dom";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 1.3.2 */
function Indicator_1_3_2_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    const location: any = useLocation();
    const strategyId = location?.state?.strategyId;
    const { calculatePercentageOfYesNo } = useCalculation();
    document.title = t(
        "indicators-responses:app:DR_Objective_1_Indicator_1_3_2_Title"
    );

    /** Excluded property that are not used in Array Map */
    const excludedProperties: Array<string> = strategyId === StrategiesEnum.BurdenReduction.toLowerCase() ?
        [
            "cannotBeAssessed",
            "cannotBeAssessedReason",
            "hasMalariaSurReportBeenProducedInLast12Months",
            "links",
            "metNotMetStatus",
            "responseActivities"
        ] :
        [
            "cannotBeAssessed",
            "cannotBeAssessedReason",
            "hasMalariaSurReportBeenProducedInLast12Months",
            "links",
            "metNotMetStatus"
        ];

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithKey,
        getResponse,
        onSave,
        onFinalize,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize buttons, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }
    const rowsData: any = {
        improvementsInFeedbackAndSupervision: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ImprovementsInSupervision"
        ),
        improvementsInDataQuality: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ImprovementsInDataQuality"
        ),
        initiateSurveillanceTrainingAndDataAnalysis: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:InitiateSurveillanceTraining"
        ),
        responseActivities: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ResponseActivities"
        ),
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const evidencePercentage = strategyId === StrategiesEnum.BurdenReduction.toLowerCase()
            ? calculatePercentageOfYesNo([
                response?.improvementsInFeedbackAndSupervision?.evidence,
                response?.improvementsInDataQuality?.evidence,
                response?.initiateSurveillanceTrainingAndDataAnalysis?.evidence,
            ]) : calculatePercentageOfYesNo([
                response?.improvementsInFeedbackAndSupervision?.evidence,
                response?.improvementsInDataQuality?.evidence,
                response?.responseActivities?.evidence,
                response?.initiateSurveillanceTrainingAndDataAnalysis?.evidence,
            ]);

        onValueChange(
            "metNotMetStatus",
            evidencePercentage === 100
                ? MetNotMetEnum.Met
                : evidencePercentage === 0
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response?.improvementsInFeedbackAndSupervision?.evidence,
        response?.improvementsInDataQuality?.evidence,
        response?.responseActivities?.evidence,
        response?.initiateSurveillanceTrainingAndDataAnalysis?.evidence,
    ]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p className="fw-lighter">
                        {parse(
                            t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ResponseDesc"
                            )
                        )}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:CompleteAssessmentTicked")}
                    </p>
                    <p className="fw-lighter">
                        {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:DocumentsDetailsDesc")}
                    </p>
                    {
                        //Show error message if not all radio buttons are selected for Data quality control check in place
                        errors["improvementsInFeedbackAndSupervision.evidence"] && (
                            <span className="Mui-error d-flex mb-2">
                                *
                                {t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ResponseError"
                                )}
                            </span>
                        )}
                    <div className="mt-3">
                        <table className="app-table">
                            <thead>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:DataUse")}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:Evidence")}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:DocumentsData")}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:Links")}
                                    </div>
                                </th>
                            </thead>
                            <TableBody>
                                <>
                                    {Object.keys(response)
                                        .filter((key: string) => !excludedProperties.includes(key))
                                        .map((row: string, index: number) => (
                                            <TableRow key={`row_${row}_${index}`}>
                                                <>
                                                    <TableCell>{rowsData[row]}</TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="evidence"
                                                            name="evidence"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={response[row]?.evidence}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onChangeWithKey(e, row);
                                                                getMetNotMetStatus();
                                                            }}
                                                            error={
                                                                errors[`${row}.evidence`] &&
                                                                errors[`${row}.evidence`]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="details"
                                                            name="details"
                                                            placeholder={t(
                                                                "indicators-responses:Common:DetailsForBothResponses"
                                                            )}
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={response[row]?.details || ""}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, row)}
                                                            error={
                                                                errors[`${row}.details`] &&
                                                                errors[`${row}.details`]
                                                            }
                                                            helperText={
                                                                errors[`${row}.details`] &&
                                                                errors[`${row}.details`]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="links"
                                                            name="links"
                                                            placeholder={t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:LinksPlaceholder")}
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={response[row]?.links || ""}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, row)}
                                                            error={
                                                                errors[`${row}.links`] && errors[`${row}.links`]
                                                            }
                                                            helperText={
                                                                errors[`${row}.links`] && errors[`${row}.links`]
                                                            }
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                </>
                            </TableBody>
                        </table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                        helperText={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_1_3_2_Response;
