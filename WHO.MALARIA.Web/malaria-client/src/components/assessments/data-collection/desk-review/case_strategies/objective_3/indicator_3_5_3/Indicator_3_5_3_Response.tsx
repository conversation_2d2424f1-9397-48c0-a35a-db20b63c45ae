﻿import { <PERSON><PERSON>, Icon<PERSON>utton } from "@mui/material";
import React, { useEffect } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableFooter from "../../../responses/TableFooter";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_5_3/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import { StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 3.5.3 response for desk review */
function Indicator_3_5_3_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculatePercentageOfYesNo } = useCalculation();
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_3_5_3_Title");

    const headers = [
        {
            field: "dataQualityControlCheck",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:DataQualityControlCheck"
            ),
        },
        {
            field: "dataQualityControlCheckInPlace",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:DataQualityControlCheckInPlace"
            ),
        },
    ];

    /** Excluded property that are not used in Array Map */
    let excludedProperties: Array<string>
    StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
        ? excludedProperties = [
        ] :
        excludedProperties = [
            "avoidDuplicateEntryOfRecord"
        ];

    let columns: any;

    StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
        ? (columns = {
            builtInChecksAtDataEntry: {
                field: "builtInChecksAtDataEntry",
                label: t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:BuiltInChecksAtDataEntry"
                ),
            },
            producesDataVerificationReport: {
                field: "producesDataVerificationReports",
                label: t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:ProducesDataVerificationReports"
                ),
            },
            avoidDuplicateEntryOfRecord: {
                field: "avoidDuplicateEntryOfRecords",
                label: t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:AvoidDuplicateEntryOfRecords"
                ),
            },
        })
        : (columns = {
            builtInChecksAtDataEntry: {
                field: "builtInChecksAtDataEntry",
                label: t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:BuiltInChecksAtDataEntry"
                ),
            },
            producesDataVerificationReport: {
                field: "producesDataVerificationReports",
                label: t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:ProducesDataVerificationReports"
                ),
            }
        });

    const { response, onChangeWithKey, onSave, onFinalize, getResponse, setTrueFlagOnFinalizeButtonClick } =
        useIndicatorResponseCapture<Response_1>(Response_1.init());

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        onFinalize();
    };

    useEffect(() => {
        getResponse();
    }, []);

    //Method creates an array of properties that have checked "Yes"
    const calculateCheckedForProperty = () => {
        let propertyArray: boolean[] = Array();
        StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
            ? (propertyArray = Object.keys(response).map((key) => {
                return response[key]?.inPlace;
            }))
            : (propertyArray = Object.keys(response)
                .filter((key: string) => !"avoidDuplicateEntryOfRecord".includes(key))
                .map((modelKeyName: string) => {
                    return response[modelKeyName]?.inPlace;
                }));

        return calculatePercentageOfYesNo(propertyArray);
    };

    return (
        <>
            <div className="response-wrapper">
                <Table>
                    <>
                        <TableHeader headers={headers.map((header: any) => header.label)} />
                        <TableBody>
                            <>
                                {Object.keys(response).filter((key) => !excludedProperties.includes(key)).map(
                                    (modelKeyName: string, index: number) => (
                                        <TableRow key={`row_${columns.field}_${index}`}>
                                            <>
                                                <TableCell>
                                                    <span>{columns[modelKeyName]?.label}</span>
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="inPlace"
                                                        name="inPlace"
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={response[modelKeyName]?.inPlace}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, modelKeyName)}

                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    )
                                )}
                            </>
                        </TableBody>
                        <TableFooter>
                            <>
                                <TableCell>
                                    <>
                                        {t(
                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:ProportionOfExpectedQualityControls"
                                        )}

                                        <IconButton className="grid-icon-button">
                                            <Tooltip
                                                content={t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_5_3:TableFooterProportionsTooltip"
                                                )}
                                                isHtml
                                            >
                                                <InfoIcon fontSize="small" />
                                            </Tooltip>
                                        </IconButton>
                                    </>
                                </TableCell>
                                <span></span>
                                <TableCell>
                                    <label>{calculateCheckedForProperty()}%</label>
                                </TableCell>
                            </>
                        </TableFooter>
                    </>
                </Table>
            </div>

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_3_5_3_Response;
