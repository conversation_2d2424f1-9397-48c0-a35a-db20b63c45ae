﻿import TextBox from "../../../../../../controls/TextBox";
import React, { useEffect, useRef } from "react";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import { Button, IconButton } from "@mui/material";
import { Response_1, Screenshot } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_4_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import TableFooter from "../../../responses/TableFooter";
import useCalculation from "../../../responses/useCalculation";
import { v4 as uuidv4 } from 'uuid';
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import { DeskReviewAssessmentResponseStatus } from "../../../../../../../models/Enums";
import FileUploader from "../../../../../../controls/FileUploader";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { useSelector } from "react-redux";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 3.4.1 */
function Indicator_3_4_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculateNumberOfNotNullInArray } = useCalculation();
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_4_1_Title");

    // Excluded property that are not used in Array Map 
    const excludedProperties: Array<string> = [
        "screenshot",
        "documents"
    ];

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChangeWithKey,
        onValueChange,
        getResponse,
        saveResponseAndFiles,
        getResponseDocuments,
        addDeletedDocumentIds
    } =
        useIndicatorResponseCapture<Response_1>(Response_1.init());

    const errors = useSelector((state: any) => state.error);

    useEffect(() => {
        getResponse();
        getResponseDocuments();
    }, []);

    //Returns file uploader, visibility and delete icon for screenshot
    const AddScreenshotControls = ({ outputNumber = 1 }) => {
        const url: string = setScreenshot(outputNumber);

        if (url) {
            return <>
                <IconButton title={t("indicators-responses:Common.ClickToView")}>
                    <VisibilityIcon onClick={() => window.open(url)} className="icon-button-primary" />
                </IconButton>
                <IconButton title={t("indicators-responses:Common.Delete")}>
                    <DeleteIcon onClick={() => onFileDelete(outputNumber)} className="icon-button-primary" />
                </IconButton>
            </>;
        }

        return <FileUploader
            id={`linkOrScreenshot${outputNumber}`}
            multiple
            accept=".png, .jpg, .jpeg"
            onChange={(
                evt: React.ChangeEvent<HTMLInputElement>
            ) => onFileUpload(outputNumber, evt)}
        />;
    }

    //Set the validation error in UI for screenshot
    const SetScreenshotError = ({ outputNumber = 1 }) => {
        let message: string = "";

        switch (true) {
            case !!errors[`screenshot.documentId${outputNumber}`]:
                message = errors[`screenshot.documentId${outputNumber}`];
                break;
            case !!errors[`screenshot.file${outputNumber}`]:
                message = errors[`screenshot.file${outputNumber}`]
                break;
        }

        return <span className="Mui-error d-flex mb-2">
            {message}
        </span>
    }

    //Process data on save and calls processDataAndSave method based on the isFinalized flag
    const onSave = () => {
        processDataAndSave(false)
    }

    //Process data on finalize and calls processDataAndSave method based on the isFinalized flag
    const onResponseFinalize = () => {
        processDataAndSave(true)
    }

    //Upload screenhot and save in the state
    const onFileUpload = (outputNumber: number, evt: React.ChangeEvent<HTMLInputElement>) => {
        if (evt.target.files) {
            const screenshot: Screenshot = {
                ...response.screenshot,
                [`documentId${outputNumber}`]: uuidv4(),
                [`file${outputNumber}`]: evt.target.files[0]
            }
            onValueChange('screenshot', screenshot)
        }
    }

    //Triggers when user click on delete button of File uploader
    const onFileDelete = (outputNumber: number): void => {
        const documentId: string = response.screenshot[`documentId${outputNumber}`];

        const screenshot: Screenshot = {
            ...response.screenshot,
            [`documentId${outputNumber}`]: "",
            [`file${outputNumber}`]: null,
        }

        onValueChange("screenshot", screenshot);

        if (response?.documents) {
            const documents = response.documents.filter((x: any) => x.id !== documentId);
            onValueChange("documents", documents);
        }

        addDeletedDocumentIds(documentId);
    };

    //Generate URL from uploaded file or file contents which is base64 string
    const setScreenshot = (outputNumber: number) => {
        const documentId = response?.screenshot[`documentId${outputNumber}`];
        const file = response.screenshot[`file${outputNumber}`];

        if (file) {
            return URL.createObjectURL(file);
        }

        //Get file content (base64 string)
        const content = response?.documents?.find((x: any) => x.id === documentId)?.fileContent;
        if (content) {
            const byteCharacters = atob(content);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });
            return URL.createObjectURL(blob);
        }

        return "";
    }

    const headers = [
        {
            field: "",
            label: "",
        },
        {
            field: "output1",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Output1"
            ),
        },
        {
            field: "output2",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Output2"
            ),
        },
        {
            field: "output3",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Output3"
            ),
        },
        {
            field: "output4",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Output4"
            ),
        },
        {
            field: "output5",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Output5"
            ),
        },
        {
            field: "output6",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Output6"
            ),
        },
    ];

    //Process data set the date control value then save or finalize
    const processDataAndSave = (isFinalized: boolean) => {
        const files: Array<KeyValuePair<string, File>> = [];
        for (let index = 1; index <= 6; index++) {
            files.push({ key: response?.screenshot[`documentId${index}`], value: response?.screenshot[`file${index}`] })
        }

        if (isFinalized) {
            const isFormValid = validate(response);
            if (isFormValid) {
                saveResponseAndFiles(files, DeskReviewAssessmentResponseStatus.Completed)
            }
        } else {
            saveResponseAndFiles(files, DeskReviewAssessmentResponseStatus.InProgress);
        }
    };

    const rowData: any = {
        nameTypeExpectedOutput: {
            field: "nameTypeExpectedOutput",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:NameTypeExpectedOutput"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:NameTypeExpectedOutputPlaceholder"
            ),
            placeholderSecondColumn: "",
        },
        dataSourceUsed: {
            field: "dataSourceUsed",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:DataSourceUsed"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:DataSourceUsedPlaceholder"
            ),
        },
        indicatorsAndVisualizations: {
            field: "indicatorsAndVisualizations",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:IndicatorsAndVisualizations"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:IndicatorsAndVisualizationsPlaceholder"
            ),
        },
        toolsUsed: {
            field: "toolsUsed",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:ToolsUsed"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:ToolsUsedPlaceholder"
            ),
        },
        method: {
            field: "method",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Method"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:MethodPlaceholder"
            ),
        },
        frequencyOfAnalysis: {
            field: "frequencyOfAnalysis",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:FrequencyOfAnalysis"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:FrequencyOfAnalysisPlaceholder"
            ),
        },
        levelOfAnalysisDone: {
            field: "levelOfAnalysisDone",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:LevelOfAnalysisDone"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:LevelOfAnalysisDonePlaceholder"
            ),
        },
        personResponsible: {
            field: "personResponsible",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:PersonResponsible"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:PersonResponsiblePlaceholder"
            ),
        },
        recipients: {
            field: "recipients",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Recipients"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:RecipientsPlaceholder"
            ),
        },
        methodOfDissemination: {
            field: "methodOfDissemination",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:MethodOfDissemination"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:OutputSharedPlaceholder"
            ),
        },
    };

    //Returns the count of the row which doesnt have any null value
    const calculateDataFilledPercentage = () => {

        return calculateNumberOfNotNullInArray(
            Object.values(response?.nameTypeExpectedOutput)
        );
    };

    //Returns the screenshot control for output1 to output6 for routine analysis
    const showAddScreenshotControls = () => {
        let content = [];
        for (let index = 1; index <= 6; index++) {
            content.push(
                <TableCell key={`screenshot_${index}`}>
                    <>
                        <AddScreenshotControls outputNumber={index} />
                        <SetScreenshotError outputNumber={index} />
                    </>
                </TableCell>
            );
        }
        return content;
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:ResponseDesc"
                    )}
                </p>
                <p className="fst-italic">
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:NoteToCompleteAssessment"
                    )}
                </p>

                <div>
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {Object.keys(response).filter(
                                        (key: string) => !excludedProperties.includes(key)
                                    ).map((row: string, index: number) => (
                                        <TableRow key={`row_${rowData.field}_${index}`}>
                                            <>
                                                <TableCell>
                                                    <>{rowData[row]?.label}</>
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${rowData[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="output1"
                                                        placeholder={rowData[row]?.placeholderFirstColumn}
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        value={response[row]?.output1}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                        error={
                                                            errors[`${row}.output1`] &&
                                                            errors[`${row}.output1`]
                                                        }
                                                        helperText={
                                                            errors[`${row}.output1`] &&
                                                            errors[`${row}.output1`]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${rowData[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="output2"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        value={response[row]?.output2}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${rowData[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="output3"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        value={response[row]?.output3}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${rowData[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="output4"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        value={response[row]?.output4}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${rowData[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="output5"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        value={response[row]?.output5}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`${rowData[row]?.field
                                                            }_${index}_${Math.random()}`}
                                                        name="output6"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        value={response[row]?.output6}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, row)}
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                                    <TableRow>
                                        <>
                                            <TableCell>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:Screenshot"
                                                )}
                                            </TableCell>
                                            {showAddScreenshotControls()}
                                        </>
                                    </TableRow>
                                </>
                            </TableBody>
                            <TableFooter>
                                <>
                                    <TableCell colSpan={2}>
                                        <>
                                            <span>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_4_1:TotalExpectedOutputs"
                                                )}
                                            </span>
                                        </>
                                    </TableCell>
                                    <TableCell colSpan={8}>
                                        <label>{calculateDataFilledPercentage()}</label>
                                    </TableCell>
                                </>
                            </TableFooter>
                        </>
                    </Table>
                </div>
            </div>
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_4_1_Response;