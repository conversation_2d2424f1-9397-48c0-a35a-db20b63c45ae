﻿import { useEffect } from "react";
import { <PERSON><PERSON> } from "@mui/material";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import classNames from "classnames";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import { useTranslation } from "react-i18next";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TableFooter from "../../../responses/TableFooter";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_3_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import { StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 2.3.2  */
function Indicator_2_3_2_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculateProportionOfExpectedContent } = useCalculation();
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    let excludedProperties: Array<string>;
    document.title = t(
        "indicators-responses:app:DR_Objective_2_Indicator_2_3_2_Title"
    );

    // Excluded property that are not used in Array Map
    StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
        ? (excludedProperties = [
            "cannotBeAssessed",
            "cannotBeAssessedReason",
            "guideline1",
            "guideline2",
            "guideline3",
            "guideline4",
        ])
        : (excludedProperties = [
            "cannotBeAssessed",
            "cannotBeAssessedReason",
            "guideline1",
            "guideline2",
            "guideline3",
            "guideline4",
            "nonCompliance",
            "highRiskPopulation",
            "caseInvestigation",
            "caseAcquiredLocally",
            "additionalInvestigation",
            "malariaOutbreak",
            "malariaCasesAnnuallyReported",
        ]);

    const {
        response,
        onChangeWithKey,
        getResponse,
        onSave,
        onFinalize,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init());

    useEffect(() => {
        getResponse();
    }, []);

    const headersGuidlines = [
        { field: "", label: "" },

        {
            field: "guideline1",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Guideline1"
            ),
        },
        { field: "", label: "" },
        {
            field: "guideline2",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Guideline2"
            ),
        },
        { field: "", label: "" },
        {
            field: "guideline3",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Guideline3"
            ),
        },
        { field: "", label: "" },
        {
            field: "guideline4",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Guideline4"
            ),
        },
        { field: "", label: "" },
    ];

    const headers = [
        {
            field: "minimumExpectedContent",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:MinimumExpectedContent"
            ),
        },
        {
            field: "checklist",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Checklist"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Details"
            ),
        },
        {
            field: "checklist",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Checklist"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Details"
            ),
        },
        {
            field: "checklist",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Checklist"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Details"
            ),
        },
        {
            field: "checklist",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Checklist"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Details"
            ),
        },
    ];

    const columnsGuidelines = [
        {
            field: "name",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:GuidlineName"
            ),
        },
        {
            field: "publicationDate",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:PublicationDate"
            ),
        },
    ];

    let columns: any;

    StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
        ? (columns = {
            malariaCase: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:DocumentWHORecomendation"
            ),
            proceduralGuideLine: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:ProceduralGuidlines"
            ),
            dataSecurityStatement: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:DatSecurityStatement"
            ),
            nonCompliance: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:Consequences"
            ),
            highRiskPopulation: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:AllAtRiskPopulation"
            ),
            caseInvestigation: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:CaseInvestigation"
            ),
            caseAcquiredLocally: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:CaseAquiredLocally"
            ),
            additionalInvestigation: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:AdditionalInvestigation"
            ),
            malariaOutbreak: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:SOPToInvestigation"
            ),
            malariaCasesAnnuallyReported: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:CountryShouldReport"
            ),
        })
        : (columns = {
            malariaCase: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:DocumentWHORecomendation"
            ),
            proceduralGuideLine: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:ProceduralGuidlines"
            ),
            dataSecurityStatement: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:DatSecurityStatement"
            ),
        });

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        onFinalize();
    };

    //Method calculates the proportion for the filtered properties
    const filterPropertyAndCalculateProportion = (
        propertyName: string,
        strategyName: string
    ) => {
        const propertyValues: Array<boolean> = Object.keys(response)
            .filter((key: string) => !excludedProperties.includes(key))
            .map((key: string) => {
                return response[key][propertyName];
            });

        return calculateProportionOfExpectedContent(propertyValues, strategyName);
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:ResponseTitle"
                    )}{" "}
                </p>
                <p className="fst-italic">
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:ResponseDesc"
                    )}
                </p>

                <div>
                    <Table>
                        <>
                            <TableHeader
                                headers={headersGuidlines.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {columnsGuidelines.map((column: any, index: number) => (
                                        <TableRow key={`row_${column.label}_${index}`}>
                                            <>
                                                <TableCell>
                                                    <>{column.label}</>
                                                </TableCell>
                                                <TableCell colSpan={2}>
                                                    <TextBox
                                                        id={column.field}
                                                        name={column.field}
                                                        fullWidth
                                                        value={response["guideline1"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline1")}
                                                    />
                                                </TableCell>
                                                <TableCell colSpan={2}>
                                                    <TextBox
                                                        id={column.field}
                                                        name={column.field}
                                                        fullWidth
                                                        value={response["guideline2"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline2")}
                                                    />
                                                </TableCell>
                                                <TableCell colSpan={2}>
                                                    <TextBox
                                                        id={column.field}
                                                        name={column.field}
                                                        fullWidth
                                                        value={response["guideline3"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline3")}
                                                    />
                                                </TableCell>
                                                <TableCell colSpan={2}>
                                                    <TextBox
                                                        id={column.field}
                                                        name={column.field}
                                                        fullWidth
                                                        value={response["guideline4"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline4")}
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                                </>
                            </TableBody>

                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {Object.keys(response)
                                        .filter(
                                            (key: string) => !excludedProperties.includes(key)
                                        )
                                        .map((modelKeyName: string, index: any) => (
                                            <TableRow key={`row_${modelKeyName}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{columns[modelKeyName]}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="isFirstChecklist"
                                                            name="isFirstChecklist"
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={response[modelKeyName]?.isFirstChecklist}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="firstCheckListDetails"
                                                            name="firstCheckListDetails"
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={
                                                                response[modelKeyName]?.firstCheckListDetails
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="isSecondChecklist"
                                                            name="isSecondChecklist"
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={
                                                                response[modelKeyName]?.isSecondChecklist
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="secondCheckListDetails"
                                                            name="secondCheckListDetails"
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={
                                                                response[modelKeyName]?.secondCheckListDetails
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="isThirdChecklist"
                                                            name="isThirdChecklist"
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={response[modelKeyName]?.isThirdChecklist}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="thirdCheckListDetails"
                                                            name="thirdCheckListDetails"
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={
                                                                response[modelKeyName]?.thirdCheckListDetails
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="isFourthChecklist"
                                                            name="isFourthChecklist"
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={
                                                                response[modelKeyName]?.isFourthChecklist
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="fourthCheckListDetails"
                                                            name="fourthCheckListDetails"
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={
                                                                response[modelKeyName]?.fourthCheckListDetails
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                </>
                            </TableBody>
                            {StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId ? (
                                <TableFooter>
                                    <>
                                        <TableCell>
                                            <span>
                                                {t(
                                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:TableFooterOne"
                                                )}
                                            </span>
                                        </TableCell>

                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isFirstChecklist",
                                                    "Elimination"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isSecondChecklist",
                                                    "Elimination"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isThirdChecklist",
                                                    "Elimination"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isFourthChecklist",
                                                    "Elimination"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                    </>
                                </TableFooter>
                            ) : (
                                <TableFooter>
                                    <>
                                        <TableCell>
                                            <span>
                                                {t(
                                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_3_2:TableFooterTwo"
                                                )}
                                            </span>
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isFirstChecklist",
                                                    "Burden"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isSecondChecklist",
                                                    "Burden"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isThirdChecklist",
                                                    "Burden"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                        <TableCell>
                                            <label>
                                                {filterPropertyAndCalculateProportion(
                                                    "isFourthChecklist",
                                                    "Burden"
                                                )}
                                                %
                                            </label>
                                        </TableCell>
                                        <TableCell>
                                            <span> </span>
                                        </TableCell>
                                    </>
                                </TableFooter>
                            )}
                        </>
                    </Table>
                </div>
            </div>

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_2_3_2_Response;