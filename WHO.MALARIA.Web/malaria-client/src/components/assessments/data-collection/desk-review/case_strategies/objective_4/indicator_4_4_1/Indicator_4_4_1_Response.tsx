﻿import { IconButton } from "@mui/material";
import { useEffect, useRef, ChangeEvent } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableFooter from "../../../responses/TableFooter";
import Checkbox from "../../../../../../controls/Checkbox";
import InfoIcon from "@mui/icons-material/Info";
import Tooltip from "../../../../../../controls/Tooltip";
import { Response_1, TrainingDateDetails } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_4_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import { CommonValidationRules, EliminationValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetEnum, StrategiesEnum } from "../../../../../../../models/Enums";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import { useLocation } from "react-router-dom";
import DatePicker from '../../../../../../controls/DatePicker';
// import { Date | null } from "@material-ui/pickers/typings/date";

/** Renders the indicator 4.4.1 response for desk review */
const Indicator_4_4_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculatePercentageOfYesNo, calculatePercentage } = useCalculation();
    document.title = t("indicators-responses:app:DR_Objective_4_Indicator_4_4_1_Title");
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;

    let ValidationRules: IValidationRuleProvider;
    ValidationRules = {
        ...CommonValidationRules,
        ...EliminationValidationRules
    }

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithKey,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
        convertToUTC
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const headers = [
        {
            field: "training",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:Training"
            ),
        },
        {
            field: "nationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:NationalLevel"
            ),
        },
        {
            field: "subnationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:SubnationalLevel"
            ),
        },
        {
            field: "serviceDeliveryLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ServiceDeliveryLevel"
            ),
        },
    ];

    const rowDataTextbox: any = {
        attendants: {
            field: "attendants",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:Attendants"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ProgramStaffPlaceholder"
            ),
        },
        frequencyOfTraining: {
            field: "frequencyOfTraining",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:FrequencyOfTraining"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:AnnuallyPlaceholder"
            ),
        },
    };

    const rowDataDatePicker: any = {
        lastDateOfTraining: {
            field: "lastDateOfTraining",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:DateOfLastTraining"
            ),
        },
    }

    const rowDataNumericTextbox: any = {
        plannedTraining: {
            field: "plannedTraining",
            label: t("indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:PlannedTrainings"),
        },
        previousYearTraining: {
            field: "previousYearTraining",
            label: t("indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:PreviousYearTrainings"),
        }
    };

    //Contains all the excluded textbox properties in the array
    const excludedTextboxProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "metNotMetStatus",
        "hasTraining",
        "dataCollection",
        "dataReporting",
        "conductingDataQualityReview",
        "conductingDataAnalysis",
        "preparingDisseminationReports",
        "supervision",
        "caseNotification",
        "caseInvestigation",
        "caseClassification",
        "fociInvestigation",
        "fociClassification",
        "qualityAssuranceOfLabData",
        "trainingForMicroscopy",
        "trainingInPublicPrivateSectors",
        "lastDateOfTraining",
        "plannedTraining",
        "previousYearTraining",
        "nationalTrainingProportion",
        "subNationalTrainingProportion",
        "serviceDeliveryTrainingProportion"
    ];

    //Contains all the excluded properties in the array which are not needed
    const excludedRadioButtonProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "metNotMetStatus",
        "hasTraining",
        "attendants",
        "frequencyOfTraining",
        "lastDateOfTraining",
        "plannedTraining",
        "previousYearTraining",
        "nationalTrainingProportion",
        "subNationalTrainingProportion",
        "serviceDeliveryTrainingProportion"
    ];

    //Contains all the included numeric textbox properties in the array
    const includedNumericTextboxProperties: Array<string> = [
        "plannedTraining",
        "previousYearTraining",
    ];

    //Contains all the included date picker properties in the array
    const includedDatePickerProperties: Array<string> = [
        "lastDateOfTraining"
    ];

    const rowDataRadio: any = {
        dataCollection: {
            field: "dataCollection",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:DataCollection"
            ),
        },
        dataReporting: {
            field: "dataReporting",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:DataReporting"
            ),
        },
        conductingDataQualityReview: {
            field: "conductingDataQualityReview",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ConductingDataQualityReview"
            ),
        },
        conductingDataAnalysis: {
            field: "conductingDataAnalysis",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ConductingDataAnalysis"
            ),
        },
        preparingDisseminationReports: {
            field: "preparingDisseminationReports",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:PreparingDisseminationReports"
            ),
        },
        supervision: {
            field: "supervision",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:Supervision"
            ),
        },
        caseNotification: {
            field: "caseNotification",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:CaseNotification"
            ),
        },
        caseInvestigation: {
            field: "caseInvestigation",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:CaseInvestigation"
            ),
        },
        caseClassification: {
            field: "caseClassification",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:CaseClassification"
            ),
        },
        fociInvestigation: {
            field: "fociInvestigation",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:FociInvestigation"
            ),
        },
        fociClassification: {
            field: "fociClassification",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:FociClassification"
            ),
        },
        qualityAssuranceOfLabData: {
            field: "qualityAssuranceOfLabData",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:QualityAssuranceOfLabData"
            ),
        },
        trainingForMicroscopy: {
            field: "trainingForMicroscopy",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:TrainingForMicroscopy"
            ),
        },
        trainingInPublicPrivateSectors: {
            field: "trainingInPublicPrivateSectors",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:TrainingInPublicPrivateSectors"
            ),
        },
    }

    //On click of date picker value updated in field
    const onDatePickerValueChange = (object: string, field: string, value: any) => {
        const lastDateOfProperty: TrainingDateDetails = response[object];
        const responseDateUpdate = {
            ...lastDateOfProperty,
            [field]: convertToUTC(new Date(value))
        };

        onValueChange(object, responseDateUpdate);
    }

    //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
    const calculatePercentageRange = (propertyName1: number, propertyName2: number) => {
        const proportionRateValue = calculatePercentage(propertyName1, propertyName2);

        const proportionRateExceptionContent =
            <span className="Mui-error d-flex mb-2">
                * {t("indicators-responses:Common:ResponseProportionError")}
            </span>

        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
            return proportionRateValue + '%';
        }

        return proportionRateExceptionContent;
    }

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const nationalTrainingPercentage = calculatePercentage(
            response["previousYearTraining"]?.national,
            response["plannedTraining"]?.national
        );

        const subNationalTrainingPercentage = calculatePercentage(
            response["previousYearTraining"]?.subNational,
            response["plannedTraining"]?.subNational
        );

        const serviceDeliveryTrainingPercentage = calculatePercentage(
            response["previousYearTraining"]?.serviceDeliveryLevel,
            response["plannedTraining"]?.serviceDeliveryLevel
        );

        onValueChange(
            "metNotMetStatus",
            (nationalTrainingPercentage === 100 && subNationalTrainingPercentage === 100 && serviceDeliveryTrainingPercentage === 100)
                ? MetNotMetEnum.Met
                : (nationalTrainingPercentage === 0 || subNationalTrainingPercentage === 0 || serviceDeliveryTrainingPercentage === 0)
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response.plannedTraining,
        response.previousYearTraining,
        response.plannedTraining.national,
        response.previousYearTraining.national,
        response.plannedTraining.subNational,
        response.previousYearTraining.subNational,
        response.plannedTraining.serviceDeliveryLevel,
        response.previousYearTraining.serviceDeliveryLevel
    ]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:MetNotMetTooltip"
                )}

            />
            <div className="response-assess-wrapper">
                <Checkbox
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="row mb-3">
                        <div className="col-xs-12 col-md-12">
                            <label>
                                {t(
                                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:TrainingCarriedOut"
                                )}
                            </label>
                            <RadioButtonGroup
                                id="hasTraining"
                                name="hasTraining"
                                row
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={response?.hasTraining}
                                onChange={onChange}
                            />
                        </div>
                    </div>

                    {response?.hasTraining && (
                        <>
                            <p>
                                {t(
                                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ResponseDesc"
                                )}&nbsp;
                                {t("indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ResponseDescDetails")}
                            </p>

                            {
                                //Show error message if user does not select 'Yes' or 'No' for all national, sub-national and service delivery
                                !!Object.keys(errors).length && (
                                    <>
                                        <span className="Mui-error d-flex mb-2">
                                            * {t("indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ResponseError")}
                                        </span>
                                        <span className="Mui-error d-flex mb-2">
                                            * {t("indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ResponseErrorForPreviousYear")}
                                        </span>
                                    </>
                                )
                            }
                            {
                                (errors["nationalTrainingProportion"] || errors["subNationalTrainingProportion"] || errors["serviceDeliveryTrainingProportion"]) && (
                                    <span className="Mui-error d-flex mb-2">
                                        * {t("indicators-responses:Common:ResponseProportionError")}
                                    </span>
                                )
                            }
                            <Table>
                                <>
                                    <TableHeader
                                        headers={headers.map((header: any) => header.label)}
                                    />
                                    <TableBody>
                                        <>
                                            {Object.keys(response)
                                                .filter(
                                                    (key: string) =>
                                                        !excludedRadioButtonProperties.includes(key)
                                                )
                                                .map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{rowDataRadio[modelKeyName]?.label}</>
                                                            </TableCell>
                                                            <TableCell>
                                                                <RadioButtonGroup
                                                                    id="national"
                                                                    name="national"
                                                                    color="primary"
                                                                    options={[
                                                                        new MultiSelectModel(
                                                                            true,
                                                                            t("indicators-responses:Common:Yes")
                                                                        ),
                                                                        new MultiSelectModel(
                                                                            false,
                                                                            t("indicators-responses:Common:No")
                                                                        ),
                                                                    ]}
                                                                    value={response[modelKeyName]?.national}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => {
                                                                        onChangeWithKey(e, modelKeyName);
                                                                    }}
                                                                    error={
                                                                        errors[`${modelKeyName}.national`] &&
                                                                        errors[`${modelKeyName}.national`]
                                                                    }
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <RadioButtonGroup
                                                                    id="subNational"
                                                                    name="subNational"
                                                                    color="primary"
                                                                    options={[
                                                                        new MultiSelectModel(
                                                                            true,
                                                                            t("indicators-responses:Common:Yes")
                                                                        ),
                                                                        new MultiSelectModel(
                                                                            false,
                                                                            t("indicators-responses:Common:No")
                                                                        ),
                                                                    ]}
                                                                    value={response[modelKeyName]?.subNational}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                    error={
                                                                        errors[`${modelKeyName}.subNational`] &&
                                                                        errors[`${modelKeyName}.subNational`]
                                                                    }
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <RadioButtonGroup
                                                                    id="serviceDeliveryLevel"
                                                                    name="serviceDeliveryLevel"
                                                                    color="primary"
                                                                    options={[
                                                                        new MultiSelectModel(
                                                                            true,
                                                                            t("indicators-responses:Common:Yes")
                                                                        ),
                                                                        new MultiSelectModel(
                                                                            false,
                                                                            t("indicators-responses:Common:No")
                                                                        ),
                                                                    ]}
                                                                    value={
                                                                        response[modelKeyName]?.serviceDeliveryLevel
                                                                    }
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                    error={
                                                                        errors[
                                                                        `${modelKeyName}.serviceDeliveryLevel`
                                                                        ] &&
                                                                        errors[
                                                                        `${modelKeyName}.serviceDeliveryLevel`
                                                                        ]
                                                                    }
                                                                />
                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}

                                            {Object.keys(response)
                                                .filter(
                                                    (key: string) =>
                                                        !excludedTextboxProperties.includes(key)
                                                )
                                                .map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{rowDataTextbox[modelKeyName]?.label}</>
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    fullWidth
                                                                    rows={1}
                                                                    variant="outlined"
                                                                    multiline
                                                                    id={`${rowDataTextbox[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="national"
                                                                    placeholder={
                                                                        rowDataTextbox[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }

                                                                    value={response[modelKeyName]?.national}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                />
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    fullWidth
                                                                    rows={1}
                                                                    variant="outlined"
                                                                    multiline
                                                                    id={`${rowDataTextbox[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="subNational"
                                                                    placeholder={
                                                                        rowDataTextbox[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }

                                                                    value={response[modelKeyName]?.subNational}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                />
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    fullWidth
                                                                    rows={1}
                                                                    variant="outlined"
                                                                    multiline
                                                                    id={`${rowDataTextbox[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="serviceDeliveryLevel"
                                                                    placeholder={
                                                                        rowDataTextbox[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }

                                                                    value={
                                                                        response[modelKeyName]?.serviceDeliveryLevel
                                                                    }
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                />
                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}

                                            {Object.keys(response)
                                                .filter(
                                                    (key: string) =>
                                                        includedDatePickerProperties.includes(key)
                                                )
                                                .map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{rowDataDatePicker[modelKeyName]?.label}</>
                                                            </TableCell>

                                                            <TableCell>

                                                                <DatePicker
                                                                    id={`${rowDataDatePicker[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="national"
                                                                    placeholder={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    value={response[modelKeyName]?.national}
                                                                    onChange={(
                                                                        date: Date | null | null,
                                                                        value?: string | null
                                                                    ) => {
                                                                        onDatePickerValueChange(
                                                                            modelKeyName,
                                                                            "national",
                                                                            value as string
                                                                        );
                                                                    }}

                                                                    error={
                                                                        errors[`${modelKeyName}.national`] &&
                                                                        errors[`${modelKeyName}.national`]
                                                                    }

                                                                    helperText={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    onKeyDown={(e) => {
                                                                        e.preventDefault();
                                                                    }}
                                                                />

                                                            </TableCell>

                                                            <TableCell>

                                                                <DatePicker
                                                                    id={`${rowDataDatePicker[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="subNational"
                                                                    placeholder={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    value={response[modelKeyName]?.subNational}
                                                                    onChange={(
                                                                        date: Date | null | null,
                                                                        value?: string | null
                                                                    ) => {
                                                                        onDatePickerValueChange(
                                                                            modelKeyName,
                                                                            "subNational",
                                                                            value as string
                                                                        );
                                                                    }}

                                                                    error={
                                                                        errors[`${modelKeyName}.subNational`] &&
                                                                        errors[`${modelKeyName}.subNational`]
                                                                    }

                                                                    helperText={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    onKeyDown={(e) => {
                                                                        e.preventDefault();
                                                                    }}
                                                                />

                                                            </TableCell>

                                                            <TableCell>

                                                                <DatePicker
                                                                    id={`${rowDataDatePicker[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="serviceDeliveryLevel"
                                                                    placeholder={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    value={response[modelKeyName]?.serviceDeliveryLevel}
                                                                    onChange={(
                                                                        date: Date | null | null,
                                                                        value?: string | null
                                                                    ) => {
                                                                        onDatePickerValueChange(
                                                                            modelKeyName,
                                                                            "serviceDeliveryLevel",
                                                                            value as string
                                                                        );
                                                                    }}

                                                                    error={
                                                                        errors[`${modelKeyName}.serviceDeliveryLevel`] &&
                                                                        errors[`${modelKeyName}.serviceDeliveryLevel`]
                                                                    }

                                                                    helperText={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    onKeyDown={(e) => {
                                                                        e.preventDefault();
                                                                    }}
                                                                />

                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}
                                            {Object.keys(response)
                                                .filter(
                                                    (key: string) =>
                                                        includedNumericTextboxProperties.includes(key)
                                                )
                                                .map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{rowDataNumericTextbox[modelKeyName]?.label}</>
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    id={`${rowDataNumericTextbox[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="national"
                                                                    placeholder={
                                                                        rowDataNumericTextbox[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    type="number"
                                                                    className="col-form-control inputfocus"
                                                                    inputProps={{
                                                                        min: 0,
                                                                    }}
                                                                    maxLength={3}
                                                                    fullWidth
                                                                    value={response[modelKeyName]?.national}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => {
                                                                        onChangeWithKey(e, modelKeyName);
                                                                        getMetNotMetStatus();
                                                                    }}
                                                                    error={
                                                                        errors[`${modelKeyName}.national`] &&
                                                                        errors[`${modelKeyName}.national`]
                                                                    }
                                                                />
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    id={`${rowDataNumericTextbox[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="subNational"
                                                                    placeholder={
                                                                        rowDataNumericTextbox[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    type="number"
                                                                    className="col-form-control inputfocus"
                                                                    inputProps={{
                                                                        min: 0,
                                                                    }}
                                                                    maxLength={3}
                                                                    fullWidth
                                                                    value={response[modelKeyName]?.subNational}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => {
                                                                        onChangeWithKey(e, modelKeyName);
                                                                        getMetNotMetStatus();
                                                                    }}
                                                                    error={
                                                                        errors[`${modelKeyName}.subNational`] &&
                                                                        errors[`${modelKeyName}.subNational`]
                                                                    }
                                                                />
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    id={`${rowDataNumericTextbox[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="serviceDeliveryLevel"
                                                                    placeholder={
                                                                        rowDataNumericTextbox[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    type="number"
                                                                    className="col-form-control inputfocus"
                                                                    inputProps={{
                                                                        min: 0,
                                                                    }}
                                                                    maxLength={3}
                                                                    fullWidth
                                                                    value={
                                                                        response[modelKeyName]?.serviceDeliveryLevel
                                                                    }
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => {
                                                                        onChangeWithKey(e, modelKeyName);
                                                                        getMetNotMetStatus();
                                                                    }}
                                                                    error={
                                                                        errors[`${modelKeyName}.serviceDeliveryLevel`] &&
                                                                        errors[`${modelKeyName}.serviceDeliveryLevel`]
                                                                    }
                                                                />
                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}
                                        </>
                                    </TableBody>

                                    <TableFooter>
                                        <>
                                            <TableCell>
                                                <span>
                                                    {t(
                                                        "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ProportionOfPlannedTrainings"
                                                    )}

                                                    <IconButton className="grid-icon-button">
                                                        <Tooltip
                                                            content={t(
                                                                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ProportionOfPlannedTrainingsTooltip"
                                                            )}
                                                            isHtml
                                                        >
                                                            <InfoIcon fontSize="small" />
                                                        </Tooltip>
                                                    </IconButton>
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <label>
                                                    {calculatePercentageRange(
                                                        response["previousYearTraining"]?.national,
                                                        response["plannedTraining"]?.national
                                                    )}
                                                </label>
                                            </TableCell>
                                            <TableCell>
                                                <label>
                                                    {calculatePercentageRange(
                                                        response["previousYearTraining"]?.subNational,
                                                        response["plannedTraining"]?.subNational
                                                    )}
                                                </label>
                                            </TableCell>
                                            <TableCell>
                                                <label>
                                                    {calculatePercentageRange(
                                                        response["previousYearTraining"]?.serviceDeliveryLevel,
                                                        response["plannedTraining"]?.serviceDeliveryLevel
                                                    )}
                                                </label>
                                            </TableCell>
                                        </>
                                    </TableFooter>
                                </>
                            </Table>
                        </>
                    )}
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_4_4_1_Response;