﻿import { Button } from "@mui/material";
import { useEffect, useRef, ChangeEvent } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_6/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 2.2.6 response for desk review */
const Indicator_2_2_6_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);

    document.title = t(
        "indicators-responses:app:DR_Objective_2_Indicator_2_2_6_Title"
    );

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onCannotBeAssessed,
        onChange,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const drugEfficacyMonitoringIntegrated =
            response.drugEfficacyMonitoringIntegrated;

        onValueChange(
            "metNotMetStatus",
            drugEfficacyMonitoringIntegrated === true
                ? MetNotMetEnum.Met
                : MetNotMetEnum.NotMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [response.drugEfficacyMonitoringIntegrated]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_2_6:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">           
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="response-content">
                        <div className="row">
                            <div className="col-xs-12 col-md-12">
                                <p>
                                    {t(
                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_6:ResponseDesc"
                                    )}
                                </p>
                                <RadioButtonGroup
                                    id="drugEfficacyMonitoringIntegrated"
                                    name="drugEfficacyMonitoringIntegrated"
                                    row
                                    color="primary"
                                    options={[
                                        new MultiSelectModel(
                                            true,
                                            t("indicators-responses:Common:Yes")
                                        ),
                                        new MultiSelectModel(
                                            false,
                                            t("indicators-responses:Common:No")
                                        ),
                                    ]}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                        onChange(e);
                                        getMetNotMetStatus();
                                    }}
                                    value={response?.drugEfficacyMonitoringIntegrated}
                                    error={
                                        errors["drugEfficacyMonitoringIntegrated"] &&
                                        errors["drugEfficacyMonitoringIntegrated"]
                                    }
                                    helperText={
                                        errors["drugEfficacyMonitoringIntegrated"] &&
                                        errors["drugEfficacyMonitoringIntegrated"]
                                    }
                                />
                                <TextBox
                                    id="drugEfficacyMonitoringDetails"
                                    name="drugEfficacyMonitoringDetails"
                                    label={t("indicators-responses:Common:ProvideDetails")}
                                    fullWidth
                                    multiline
                                    rows={5}
                                    maxLength={1000}
                                    onChange={onChange}
                                    value={response?.drugEfficacyMonitoringDetails || ""}
                                    error={
                                        errors["drugEfficacyMonitoringDetails"] &&
                                        errors["drugEfficacyMonitoringDetails"]
                                    }
                                    helperText={
                                        errors["drugEfficacyMonitoringDetails"] &&
                                        errors["drugEfficacyMonitoringDetails"]
                                    }
                                />
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        onChange={onChange}
                        value={response?.cannotBeAssessedReason || ""}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_2_2_6_Response;
