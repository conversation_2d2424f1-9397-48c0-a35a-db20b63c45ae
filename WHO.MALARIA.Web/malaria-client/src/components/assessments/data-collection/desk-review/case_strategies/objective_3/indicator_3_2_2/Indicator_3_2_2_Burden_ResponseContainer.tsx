﻿import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, useRef, useState, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Response_3 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_2/Response_3";
import { StepperModel } from "../../../../../../../models/StepperModel";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import WHOStepper from "../../../../../../controls/WHOStepper";
import useIndicatorResponseCaptureForTabs from "../../../responses/useIndicatorResponseCaptureForTabs";
import Indicator_3_2_2_Burden_Step_1 from "./Indicator_3_2_2_Step_1";
import Indicator_3_2_2_Burden_Step_2 from "./Indicator_3_2_2_Burden_Step_2";
import useFormValidation from "../../../../../../common/useFormValidation";
import { CommonValidationRules, BurdenReductionValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the Indicator response for 3.2.2 */
function Indicator_3_2_2_Burden_ResponseContainer() {
    const { t } = useTranslation(["indicators-responses"]);

    let ValidationRules: IValidationRuleProvider;
    ValidationRules = {
        ...CommonValidationRules,
        ...BurdenReductionValidationRules,
    };

    const steps: Array<StepperModel> = [
        new StepperModel(0, "A", <></>),
        new StepperModel(1, "B", <></>),
    ];

    const [currentStep, setCurrentStep] = useState<number>(0);
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(updateStepIndex(0));
        getResponse();
    }, []);

    // Trrigers whenever user changes the step
    const onStepChange = (curerntStep: number) => {
        setCurrentStep(curerntStep);
        dispatch(updateStepIndex(curerntStep));
    };

    /** Renders the component based on step */
    const renderStepComponent = () => {
        switch (currentStep) {
            case 0:
                return (
                    <Indicator_3_2_2_Burden_Step_1
                        step_A={response?.step_A}
                        updateStep_A={updateStep_A}
                        onValueChange={onValueChange}
                    />
                );
            case 1:
                return (
                    <Indicator_3_2_2_Burden_Step_2
                        step_B={response.step_B}
                        updateStep_B={updateStep_B}
                    />
                );
        }
    };

    const {
        response,
        onChange,
        updateStep_A,
        updateStep_B,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCaptureForTabs<Response_3>(Response_3.init());

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            <div className="response-wrapper">
                {!response.cannotBeAssessed ? (
                    <>
                        {!!Object.keys(errors).length && (
                            <span className="Mui-error d-flex mt-2">
                                *
                                {t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:ResponseErrorAToB"
                                )}
                            </span>
                        )}
                        <WHOStepper
                            alternativeLabel={false}
                            nonLinear={true}
                            steps={steps}
                            activeStep={currentStep}
                            enableStepClick
                            onStepChange={onStepChange}
                        >
                            <>{renderStepComponent()}</>
                        </WHOStepper>
                    </>
                ) : (
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                )}
            </div>

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_3_2_2_Burden_ResponseContainer;