﻿import { Button } from "@mui/material";
import { useEffect } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import {
    Response_1,
    SurveillanceStaffPerceivedAbility,
} from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_4_3/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 4.4.3 response for desk review */
const Indicator_4_4_3_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_4_Indicator_4_4_3_Title"
    );
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    let columns: any;

    const headers = [
        {
            field: "tasks",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:Tasks"
            ),
        },
        {
            field: "difficultyInCompletion",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:DifficultyInCompletion"
            ),
        },
        {
            field: "descriptionOfWhy",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:DescriptionOfWhy"
            ),
        },
    ];

    StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
        ? (columns = [
            {
                field: "recordMalariaData",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:RecordMalariaData"
                ),
            },
            {
                field: "reportMalariaDataToDistrict",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:ReportMalariaDataToDistrict"
                ),
            },
            {
                field: "dataQualityChecks",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:DataQualityChecks"
                ),
            },
            {
                field: "calculateMalariaIndicators",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:CalculateMalariaIndicators"
                ),
            },
            {
                field: "dataVisualsOfMalariaIndicators",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:DataVisualsOfMalariaIndicators"
                ),
            },
            {
                field: "interpretMalariaData",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:InterpretMalariaData"
                ),
            },
            {
                field: "caseNotification",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:CaseNotification"
                ),
            },
            {
                field: "caseInvestigation",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:CaseInvestigation"
                ),
            },
            {
                field: "caseClassification",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:CaseClassification"
                ),
            },
            {
                field: "focusInvestigation",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:FocusInvestigation"
                ),
            },
            {
                field: "focusClassification",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:FocusClassification"
                ),
            },
            {
                field: "focusResponse",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:FocusResponse"
                ),
            },
            {
                field: "makingDecisions",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:MakingDecisions"
                ),
            },
        ])
        : (columns = [
            {
                field: "recordMalariaData",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:RecordMalariaData"
                ),
            },
            {
                field: "reportMalariaDataToDistrict",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:ReportMalariaDataToDistrict"
                ),
            },
            {
                field: "dataQualityChecks",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:DataQualityChecks"
                ),
            },
            {
                field: "calculateMalariaIndicators",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:CalculateMalariaIndicators"
                ),
            },
            {
                field: "dataVisualsOfMalariaIndicators",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:DataVisualsOfMalariaIndicators"
                ),
            },
            {
                field: "interpretMalariaData",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:InterpretMalariaData"
                ),
            },
            {
                field: "makingDecisions",
                label: t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:MakingDecisions"
                ),
            },
        ]);

    const surveillanceStaffPerceivedAbilities = columns.map(
        (column: any) => new SurveillanceStaffPerceivedAbility(column.field, null, null)
    );

    const validate = useFormValidation(ValidationRules);

    const { response, onChangeWithIndex, onSave, onFinalize, getResponse, onChange } =
        useIndicatorResponseCapture<Response_1>(
            new Response_1(surveillanceStaffPerceivedAbilities, null), validate
        );

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize buttons, performs validations and then action is performed
    const onResponseFinalize = () => {
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    return (
        <>
            <div className="response-wrapper">
                <div className="row my-3 d-flex">
                    <div className="col-xs-12 col-sm-6">
                        <TextBox
                            id="jobRole"
                            name="jobRole"
                            label={t("indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:PersonInterviewedJobRole")}
                            variant="outlined"
                            fullWidth
                            value={response?.jobRole}
                            onChange={onChange}
                            InputLabelProps={{ shrink: true }}
                            inputProps={{                               
                                maxLength: 100,
                            }}
                            error={
                                errors["jobRole"] &&
                                errors["jobRole"]
                            }
                        />
                    </div>
                </div>
                <p>
                    {t(
                        "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:ResponseDesc"
                    )}
                </p>
                {
                    // User does not select 'Yes' or 'No' for all difficulty in completion.
                    !!Object.keys(errors).length && (
                        <span className="Mui-error d-flex mb-2">
                            *{" "}
                            {t(
                                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_3:ResponseError"
                            )}
                        </span>
                    )
                }
                <Table>
                    <>
                        <TableHeader headers={headers.map((header: any) => header.label)} />
                        <TableBody>
                            <>
                                {columns.map((column: any, index: number) => (
                                    <TableRow key={`column_${column.field}_${index}`}>
                                        <>
                                            <TableCell>
                                                <label>{column.label}</label>
                                            </TableCell>

                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="completionDifficulty"
                                                    name="completionDifficulty"
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={
                                                        response?.surveillanceStaffPerceivedAbilities[index]
                                                            ?.completionDifficulty
                                                    }
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onChangeWithIndex(
                                                            e,
                                                            "surveillanceStaffPerceivedAbilities",
                                                            index
                                                        )
                                                    }
                                                    error={
                                                        errors[
                                                        `response?.surveillanceStaffPerceivedAbilities[${index}].completionDifficulty`
                                                        ] &&
                                                        errors[`response?.surveillanceStaffPerceivedAbilities[${index}].completionDifficulty`]
                                                    }
                                                />
                                            </TableCell>

                                            <TableCell>
                                                <TextBox
                                                    id={`${column.field}_${index}_${Math.random()}`}
                                                    name="description"
                                                    placeholder={column.placeholderFirstColumn}
                                                    fullWidth
                                                    multiline
                                                    rows={2}
                                                    value={
                                                        response?.surveillanceStaffPerceivedAbilities[index]
                                                            ?.description
                                                    }
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onChangeWithIndex(
                                                            e,
                                                            "surveillanceStaffPerceivedAbilities",
                                                            index
                                                        )
                                                    }
                                                    error={errors[`surveillanceStaffPerceivedAbilities[${index}].description`] && errors[`surveillanceStaffPerceivedAbilities[${index}].description`]}
                                                    helperText={
                                                        errors[`surveillanceStaffPerceivedAbilities[${index}].description`] && errors[`surveillanceStaffPerceivedAbilities[${index}].description`]}
                                                />
                                            </TableCell>
                                        </>
                                    </TableRow>
                                ))}
                            </>
                        </TableBody>
                    </>
                </Table>
            </div>

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_4_4_3_Response;
