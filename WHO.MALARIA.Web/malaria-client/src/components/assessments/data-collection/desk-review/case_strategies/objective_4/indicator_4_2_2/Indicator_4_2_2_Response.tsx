﻿import { But<PERSON> } from "@mui/material";
import { useEffect } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_2_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 4.2.2 response for desk review */
const Indicator_4_2_2_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_4_Indicator_4_2_2_Title"
  );

  const { response, onChange, onSave, onFinalize, getResponse } =
    useIndicatorResponseCapture<Response_1>(Response_1.init());

  useEffect(() => {
    getResponse();
  }, []);

  return (
    <>
      <div className="response-wrapper">
        <div className="row mb-3">
          <div className="col-xs-12 col-md-8">
            <div className="row mb-5">
              <div className="col-xs-12 col-md-12">
                <TextBox
                  id="motivationReason"
                  name="motivationReason"
                  label={t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_2_2:StaffPerformHighQuality"
                  )}
                  placeholder={t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_2_2:StaffPerformHighQualityPlaceholder"
                  )}
                  multiline
                  rows={10}
                  variant="outlined"
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={response?.motivationReason}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onChange(e)
                  }
                />
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 col-md-12">
                <TextBox
                  id="demotivationReason"
                  name="demotivationReason"
                  label={t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_2_2:CausesOfDemotivation"
                  )}
                  placeholder={t(
                    "indicators-responses:DRObjective_4_Responses:Indicator_4_2_2:CausesOfDemotivationPlaceholder"
                  )}
                  multiline
                  rows={10}
                  variant="outlined"
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={response?.demotivationReason}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onChange(e)
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <SaveFinalizeButton onSave={onSave} onFinalize={onFinalize} />
    </>
  );
};

export default Indicator_4_2_2_Response;
