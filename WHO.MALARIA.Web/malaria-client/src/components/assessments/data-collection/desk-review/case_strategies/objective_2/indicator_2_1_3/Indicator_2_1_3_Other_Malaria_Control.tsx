﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import React, { useEffect } from "react";
import TableFooter from "../../../responses/TableFooter";
import { IconButton } from "@mui/material";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import useCalculation from "../../../responses/useCalculation";
import { useSelector } from "react-redux";

type Indicator_2_1_3_Props = {
    response: any;
    updateStep_B: (step_B: any) => void;
    onValueChange: (field: string, value: any) => void;
};

/** Renders the response for indicator 2.1.3 step 2 */
function Indicator_2_1_3_Other_Malaria_Control(props: Indicator_2_1_3_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_B, proportionRateForOtherStrategy } = props.response;
    const { updateStep_B, onValueChange } = props;
    const { calculateProportionRate } = useCalculation();
    const { specifyOther } = step_B;
    const errors = useSelector((state: any) => state.error);

    useEffect(() => {
        const proportionRateForOtherStrategy = calculateProportionRateForProperty("strategyInPlace", "surveillanceImplemented");

        onValueChange("proportionRateForOtherStrategy", proportionRateForOtherStrategy);
    }, [step_B]);

    // Excluded property that are not used in Array Map */
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "specifyOther",
    ];

    // Excluded Calculate Proportion Properties that are not used in Array Map */
    const excludedCalculateProportionProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "specifyOther",
        "vitalRegistrationSystem",
        "laboratoryData"
    ];

    // Triggered whenever the textbox control values are changed and update response
    const onFieldValueChange = (
        fieldName: string,
        value: any,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_B,
            [modelKeyName]: {
                ...step_B[modelKeyName],
                [fieldName]: value,
            },
        };

        updateStep_B(_response);
    };

    // Triggered whenever the radio control values are changed and update response
    const onRadioButtonValueChange = (
        fieldName: string,
        value: string,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_B,
            [modelKeyName]: {
                ...step_B[modelKeyName],
                [fieldName]: value === "false" ? false : true,
            },
        };

        updateStep_B(_response);
    };

    const headers = [
        {
            field: "malariaControlStrategy",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:MalariaControlStrategy"
            ),
        },
        {
            field: "strategyInPlace",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:StrategyInPlace"
            ),
        },
        {
            field: "surveillanceImplemented",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:SurveillanceImplemented"
            ),
        },
        {
            field: "dataIntegratedSurveillance",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:DataIntegratedSurveillance"
            ),
        },
        {
            field: "methodOfIntegration",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:MethodOfIntegration"
            ),
        },
        {
            field: "dataLinkage",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:DataLinkage"
            ),
        },
        {
            field: "detailsOnDataLinkage",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:DetailsOnDataLinkage"
            ),
        },
    ];

    const columns: any = {
        chemoPreventionInPregnantWomen: {
            field: "chemopreventionInPregnantWomen",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ChemopreventionInPregnantWomen"
            ),
        },
        chemoPreventionInInfancy: {
            field: "chemopreventionInInfancy",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ChemopreventionInInfancy"
            ),
        },
        chemoPreventionSMC: {
            field: "chemoPreventionSMC",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ChemopreventionSMC"
            ),
        },
        chemoPreventionMDA: {
            field: "chemopreventionMDA",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ChemopreventionMDA"
            ),
        },
        vectorControlRoutineChannel: {
            field: "vectorControlRoutineChannel",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:VectorControlRoutineChannel"
            ),
        },
        vectorControlMassCampaign: {
            field: "vectorControlMassCampaign",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:VectorControlsMassCampaigns"
            ),
        },
        vectorControlIRS: {
            field: "vectorControlIRS",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:VectorControlIRS"
            ),
        },
        vectorControlLSM: {
            field: "vectorControlLSM",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:VectorControlLSM"
            ),
        },
        drugEfficacy: {
            field: "drugEfficacy",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:DrugEfficacy"
            ),
        },
        genomicSurveillance: {
            field: "genomicSurveillance",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:GenomicSurveillance"
            ),
        },
        entomologicalSurveillance: {
            field: "entomologicalSurveillance",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:EntomologicalSurveillance"
            ),
        },
        commodityTracking: {
            field: "commodityTracking",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:CommodityTracking"
            ),
        },
        vitalRegistrationSystem: {
            field: "vitalRegistrationSystem",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:VitalRegistrationSystem"
            ),
        },
        laboratoryData: {
            field: "laboratoryData",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:LaboratoryData"
            ),
        },
    };

    //Method creates the arrays for two different property to calculate the proportion rate
    const calculateProportionRateForProperty = (
        modelKeyNameOne: string,
        modelKeyNameTwo: string
    ) => {
        let columnOneSum: number = 0;
        let columnTwoSum: number = 0;

        Object.keys(step_B)
            .filter((key: string) => !excludedCalculateProportionProperties.includes(key))
            .map((item: string) => {
                columnOneSum += step_B[item]?.[modelKeyNameOne];
                columnTwoSum += step_B[item]?.[modelKeyNameTwo];
            });

        return calculateProportionRate(columnOneSum, columnTwoSum);
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:OtherMalariaControlDesc"
                    )}
                </p>
                <div>
                    {
                        //Show error message if user does not select 'Yes' or 'No' for all Malaria control strategy in place, Surveillance implemented
                        !!Object.keys(errors).filter((key: string) => !excludedProperties.includes(key)).length && (
                            <span className="Mui-error d-flex mb-2">
                                *
                                {t(
                                    "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ResponseError"
                                )}
                            </span>
                        )
                    }
                    {
                        errors["proportionRateForOtherStrategy"] && (
                            <span className="Mui-error d-flex mb-2">{errors["proportionRateForOtherStrategy"]}</span>
                        )
                    }
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {Object.keys(step_B)
                                        .filter((key: string) => !excludedProperties.includes(key))
                                        .map((modelKeyName: string, index: number) => (
                                            <TableRow key={`modelKeyName${columns.field}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <span>{columns[modelKeyName]?.label}</span>
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="strategyInPlace"
                                                            name="strategyInPlace"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={
                                                                step_B[modelKeyName]?.strategyInPlace
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onRadioButtonValueChange(
                                                                    "strategyInPlace",
                                                                    e.currentTarget.value,
                                                                    modelKeyName
                                                                );
                                                            }}
                                                            error={
                                                                errors[
                                                                `step_B.${modelKeyName}.strategyInPlace`
                                                                ] &&
                                                                errors[
                                                                `step_B.${modelKeyName}.strategyInPlace`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="surveillanceImplemented"
                                                            name="surveillanceImplemented"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={
                                                                step_B[modelKeyName]?.surveillanceImplemented
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onRadioButtonValueChange(
                                                                    "surveillanceImplemented",
                                                                    e.currentTarget.value,
                                                                    modelKeyName
                                                                );
                                                            }}
                                                            error={
                                                                errors[
                                                                `step_B.${modelKeyName}.surveillanceImplemented`
                                                                ] &&
                                                                errors[
                                                                `step_B.${modelKeyName}.surveillanceImplemented`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem"
                                                            name="dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={
                                                                step_B[modelKeyName]
                                                                    ?.dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onRadioButtonValueChange(
                                                                    "dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem",
                                                                    e.currentTarget.value,
                                                                    modelKeyName
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="methodOfIntegration"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={step_B[modelKeyName]?.methodOfIntegration}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "methodOfIntegration",
                                                                    e.currentTarget.value,
                                                                    modelKeyName
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="dataLinkage"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={step_B[modelKeyName]?.dataLinkage}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "dataLinkage",
                                                                    e.currentTarget.value,
                                                                    modelKeyName
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="detailsOnDataLinkageToTheIndexCase"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={
                                                                step_B[modelKeyName]
                                                                    ?.detailsOnDataLinkageToTheIndexCase
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "detailsOnDataLinkageToTheIndexCase",
                                                                    e.currentTarget.value,
                                                                    modelKeyName
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}

                                    <TableRow>
                                        <>
                                            <TableCell>
                                                <TextBox
                                                    id="otherStrategy"
                                                    name="otherStrategy"
                                                    variant="outlined"
                                                    fullWidth
                                                    InputLabelProps={{ shrink: true }}
                                                    value={specifyOther?.otherStrategy}
                                                    placeholder={t(
                                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:Other"
                                                    )}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onFieldValueChange(
                                                            "otherStrategy",
                                                            e.currentTarget.value,
                                                            "specifyOther"
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    name="strategyInPlace"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={specifyOther?.strategyInPlace}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onRadioButtonValueChange(
                                                            "strategyInPlace",
                                                            e.currentTarget.value,
                                                            "specifyOther"
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    name="surveillanceImplemented"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={specifyOther?.surveillanceImplemented}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onRadioButtonValueChange(
                                                            "surveillanceImplemented",
                                                            e.currentTarget.value,
                                                            "specifyOther"
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    name="dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={
                                                        specifyOther?.dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem
                                                    }
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onRadioButtonValueChange(
                                                            "dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem",
                                                            e.currentTarget.value,
                                                            "specifyOther"
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <TextBox
                                                    id="methodOfIntegration"
                                                    name="methodOfIntegration"
                                                    variant="outlined"
                                                    fullWidth
                                                    InputLabelProps={{ shrink: true }}
                                                    value={specifyOther?.methodOfIntegration}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onFieldValueChange(
                                                            "methodOfIntegration",
                                                            e.currentTarget.value,
                                                            "specifyOther"
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <TextBox
                                                    id="dataLinkage"
                                                    name="dataLinkage"
                                                    variant="outlined"
                                                    fullWidth
                                                    InputLabelProps={{ shrink: true }}
                                                    value={specifyOther?.dataLinkage}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onFieldValueChange(
                                                            "dataLinkage",
                                                            e.currentTarget.value,
                                                            "specifyOther"
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <TextBox
                                                    id="detailsOnDataLinkageToTheIndexCase"
                                                    name="detailsOnDataLinkageToTheIndexCase"
                                                    variant="outlined"
                                                    fullWidth
                                                    InputLabelProps={{ shrink: true }}
                                                    value={
                                                        specifyOther?.detailsOnDataLinkageToTheIndexCase
                                                    }
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onFieldValueChange(
                                                            "detailsOnDataLinkageToTheIndexCase",
                                                            e.currentTarget.value,
                                                            "specifyOther"
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                        </>
                                    </TableRow>
                                </>
                            </TableBody>
                            <TableFooter>
                                <>
                                    <TableCell>
                                        <>
                                            {t(
                                                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:OtherMalariaTableFooterTitle"
                                            )}

                                            <IconButton className="grid-icon-button">
                                                <Tooltip
                                                    content={t(
                                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:OtherMalariaTooltip"
                                                    )}
                                                    isHtml
                                                >
                                                    <InfoIcon fontSize="small" />
                                                </Tooltip>
                                            </IconButton>
                                        </>
                                    </TableCell>
                                    <TableCell colSpan={6}>
                                        <label>
                                            {errors["proportionRateForOtherStrategy"] ? <span className="Mui-error d-flex">{errors["proportionRateForOtherStrategy"]}</span>
                                                : `${proportionRateForOtherStrategy}%`}
                                        </label>
                                    </TableCell>
                                </>
                            </TableFooter>
                        </>
                    </Table>
                </div>
            </div>
        </>
    );
}

export default Indicator_2_1_3_Other_Malaria_Control;