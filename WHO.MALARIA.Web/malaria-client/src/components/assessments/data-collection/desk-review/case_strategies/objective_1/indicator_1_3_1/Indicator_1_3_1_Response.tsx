import { <PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, ChangeEvent, useRef } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableBody from "../../../responses/TableBody";
import {
    Response_1,
    DataUseResponse,
} from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import useCalculation from "../../../responses/useCalculation";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import {
    MetNotMetEnum,
    StrategiesEnum,
} from "../../../../../../../models/Enums";
import { useLocation } from "react-router";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import parse from "html-react-parser";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 1.3.1 */
function Indicator_1_3_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculatePercentageOfYesNo } = useCalculation();
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;

    document.title = t(
        "indicators-responses:app:DR_Objective_1_Indicator_1_3_1_Title"
    );
    let rows: any;

    StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
        ? (rows = [
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:NationalStrategicPlanning"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:SubnationalStrategicPlanning"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:StratificationPrioritizationInterventions"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:MalariaPolicy"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:AdvocateForPolicy"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:MonitorProgramPerformance"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:AllocationOfResources"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:DistributionOfCommodities"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:EliminationCertification"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:CaseDetection"
            ),
        ])

        : (rows = [
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:NationalStrategicPlanning"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:SubnationalStrategicPlanning"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:StratificationPrioritizationInterventions"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:MalariaPolicy"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:AdvocateForPolicy"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:MonitorProgramPerformance"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:AllocationOfResources"
            ),
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:DistributionOfCommodities"
            ),
        ]);

    const healthManagementStructure = rows.map(() => new DataUseResponse());
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithIndex,
        getResponse,
        onSave,
        onFinalize,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(
        Response_1.init(healthManagementStructure),
        validate
    );

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize buttons, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const planningPercentage = calculatePercentageOfYesNo([
            response?.healthManagementStructure[0]?.evidence,
            response?.healthManagementStructure[1]?.evidence,
            response?.healthManagementStructure[2]?.evidence,
        ]);
        const policyPercentage = calculatePercentageOfYesNo([
            response?.healthManagementStructure[3]?.evidence,
            response?.healthManagementStructure[4]?.evidence,
        ]);
        const operationalPercentage = calculatePercentageOfYesNo([
            response?.healthManagementStructure[5]?.evidence,
            response?.healthManagementStructure[6]?.evidence,
            response?.healthManagementStructure[7]?.evidence,
        ]);
        const otherCasePercentage = calculatePercentageOfYesNo([
            response?.healthManagementStructure[8]?.evidence,
            response?.healthManagementStructure[9]?.evidence,
        ]);
        StrategiesEnum.BurdenReduction.toLowerCase() === strategyId
            ? onValueChange(
                "metNotMetStatus",
                planningPercentage > 0 &&
                    policyPercentage > 0 &&
                    operationalPercentage > 0
                    ? MetNotMetEnum.Met
                    : planningPercentage === 0 &&
                        policyPercentage === 0 &&
                        operationalPercentage === 0
                        ? MetNotMetEnum.NotMet
                        : MetNotMetEnum.PartiallyMet
            )
            : onValueChange(
                "metNotMetStatus",
                planningPercentage > 0 &&
                    policyPercentage > 0 &&
                    operationalPercentage > 0 &&
                    otherCasePercentage > 0
                    ? MetNotMetEnum.Met
                    : planningPercentage === 0 &&
                        policyPercentage === 0 &&
                        operationalPercentage === 0 &&
                        otherCasePercentage === 0
                        ? MetNotMetEnum.NotMet
                        : MetNotMetEnum.PartiallyMet
            );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response?.healthManagementStructure[0]?.evidence,
        response?.healthManagementStructure[1]?.evidence,
        response?.healthManagementStructure[2]?.evidence,
        response?.healthManagementStructure[3]?.evidence,
        response?.healthManagementStructure[4]?.evidence,
        response?.healthManagementStructure[5]?.evidence,
        response?.healthManagementStructure[6]?.evidence,
        response?.healthManagementStructure[7]?.evidence,
        response?.healthManagementStructure[8]?.evidence,
        response?.healthManagementStructure[9]?.evidence,
    ]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p className="fw-lighter">
                        {parse(t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:ResponseDesc"
                        ))}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:CompleteAssessmentDesc")}
                    </p>
                    <p className="fw-lighter">
                        {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:DocumentsDetailsDesc")}
                    </p>

                    {/*Show error message if not all radio buttons are selected for Data quality control check in place*/}
                    {errors["healthManagementStructure[0].evidence"] && (
                        <span className="Mui-error d-flex mb-2">
                            *
                            {t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_1:ResponseError"
                            )}
                        </span>
                    )}

                    <div className="mt-3">
                        <table width="100%" className="app-table">
                            <thead>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:DataUse")}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:Evidence")}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:DocumentsDetails")}
                                    </div>
                                </th>
                                <th>
                                    <div className="fw-bold">
                                        {t("indicators-responses:Common:Links")}
                                    </div>
                                </th>
                            </thead>
                            <TableBody>
                                <>
                                    {rows.map((row: any, index: number) => (
                                        <TableRow key={`row_${index}`}>
                                            <>
                                                <TableCell>{row}</TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="evidence"
                                                        name="evidence"
                                                        row
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={
                                                            response?.healthManagementStructure[index]
                                                                ?.evidence
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onChangeWithIndex(
                                                                e,
                                                                "healthManagementStructure",
                                                                index
                                                            );
                                                            getMetNotMetStatus();
                                                        }}
                                                        error={
                                                            errors[
                                                            `healthManagementStructure[${index}].evidence`
                                                            ] &&
                                                            errors[
                                                            `healthManagementStructure[${index}].evidence`
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`details_${index}_${Math.random()}`}
                                                        name="details"
                                                        value={
                                                            response?.healthManagementStructure[index]
                                                                ?.details
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) =>
                                                            onChangeWithIndex(
                                                                e,
                                                                "healthManagementStructure",
                                                                index
                                                            )
                                                        }
                                                        fullWidth
                                                        error={
                                                            errors[
                                                            `healthManagementStructure[${index}].details`
                                                            ] &&
                                                            errors[
                                                            `healthManagementStructure[${index}].details`
                                                            ]
                                                        }
                                                        helperText={
                                                            errors[
                                                            `healthManagementStructure[${index}].details`
                                                            ] &&
                                                            errors[
                                                            `healthManagementStructure[${index}].details`
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`links_${index}_${Math.random()}`}
                                                        name="links"
                                                        value={
                                                            response?.healthManagementStructure[index]?.links
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) =>
                                                            onChangeWithIndex(
                                                                e,
                                                                "healthManagementStructure",
                                                                index
                                                            )
                                                        }
                                                        fullWidth
                                                        error={
                                                            errors[
                                                            `healthManagementStructure[${index}].links`
                                                            ] &&
                                                            errors[
                                                            `healthManagementStructure[${index}].links`
                                                            ]
                                                        }
                                                        helperText={
                                                            errors[
                                                            `healthManagementStructure[${index}].links`
                                                            ] &&
                                                            errors[
                                                            `healthManagementStructure[${index}].links`
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                                </>
                            </TableBody>
                        </table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                        helperText={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_1_3_1_Response;
