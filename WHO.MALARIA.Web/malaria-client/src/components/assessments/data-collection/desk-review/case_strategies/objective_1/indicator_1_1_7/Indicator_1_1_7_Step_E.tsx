﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import { Add, Delete } from "@mui/icons-material";
import TableBody from "../../../responses/TableBody";
import TableHead from "../../../responses/TableHeader";
import TableCell from "../../../responses/TableCell";
import Dropdown from "../../../../../../controls/Dropdown";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import {
    Chart,
    ChartSeries,
    ChartSeriesItem,
    ChartCategoryAxis,
    ChartCategoryAxisItem,
    ChartTitle,
    ChartLegend,
} from "@progress/kendo-react-charts";
import useYears from "../../../../useYears";
import {
    MalariaDeathsOverTimeData,
    Step_E_Response,
    LineDatamodel,
} from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_7/Response_1";
import TableHeader from "../../../responses/TableHeader";
import Table from "../../../responses/Table";
import { useSelector } from "react-redux";
import parse from "html-react-parser";

type Indicator_1_1_7_Props = {
    step_E: Step_E_Response;
    updateStep_E: (step_E: any) => void;
    onChangeOfArrayWithIndex: any;
    onChange: React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
};

/** Renders the indicator 1.1.7 Step E response for desk review */
const Indicator_1_1_7_Step_E = (props: Indicator_1_1_7_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("app.DR_Objective_1_Indicator_1_1_7_Title");
    const { step_E, updateStep_E } = props;
    const years = useYears();
    const [isFirstTime, setIsFirstTime] = useState<boolean>(true);
    const errors = useSelector((state: any) => state.error);

    // Render Graph First Time if Data
    useEffect(() => {
        // This is for render 1st time for Graph
        if (isFirstTime && step_E && step_E.nationalLevelDeathOverTimeData.length) {
            generateLineGraphHandler(step_E);
            setIsFirstTime(false);
        }
    }, [step_E]);

    // Triggered whenever 'Delete Row' button is clicked.
    const onRowDelete = () => {
        updateStep_E({
            ...step_E,
            nationalLevelDeathOverTimeData:
                step_E.nationalLevelDeathOverTimeData.slice(
                    0,
                    step_E.nationalLevelDeathOverTimeData.length - 1
                ),
        });
    };

    // Triggered whenever 'Add Row' button is clicked.
    const onRowAdd = () => {
        const newNationalLevelData: Array<MalariaDeathsOverTimeData> = [
            ...step_E.nationalLevelDeathOverTimeData,
            new MalariaDeathsOverTimeData(),
        ];
        updateStep_E({
            ...step_E,
            nationalLevelDeathOverTimeData: newNationalLevelData,
        });
    };

    // Triggers when input control's value changes
    const onValueChange = (fieldName: string, value: any, index: number) => {
        let _nationalLevelData: Array<MalariaDeathsOverTimeData> = [
            ...step_E.nationalLevelDeathOverTimeData,
        ];
        _nationalLevelData[index][fieldName] = value ? Math.round(value) : null;

        const _response = {
            ...step_E,
            nationalLevelDeathOverTimeData: _nationalLevelData,
        };
        updateStep_E(_response);
    };

    // Triggers when input control's value changes
    const onDataSourceValueChange = (fieldname: string, value: any) => {
        const _response = {
            ...step_E,
            [fieldname]: value,
        };
        updateStep_E(_response);
    };

    // Table headers
    const tableHeaders = [
        t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:Year"),
        t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DataSource1"
        ),
        t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DataSource2"
        ),
        t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DataSource3"
        ),
    ];

    // Show placeholder text for data sources textbox
    const rowDataTextboxPlaceholder: any = [{
        field: "dataSource1Deaths",
        placeholderFirstColumn: 2500,
        placeholderSecondColumn: 3600,
        placeholderThirdColumn: 4000,
    },
    {
        field: "dataSource2Deaths",
        placeholderFirstColumn: 3000,
        placeholderSecondColumn: 3750,
        placeholderThirdColumn: 4600,
    },
    {
        field: "dataSource3Deaths",
        placeholderFirstColumn: 3500,
        placeholderSecondColumn: 3650,
        placeholderThirdColumn: 5000,
    }];

    // line chart title
    const linechartTitle = t(
        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:GraphTitle"
    );

    // line chart x-axis tiles
    const [lineChartxAxisTitles, setlineChartxAxisTitles] = useState<
        Array<string | number>
    >([]);

    // line chart data source
    const lineChartDataSource = () => {
        return [
            {
                name: !step_E.dataSource1 ? t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DataSourceOne") : step_E.dataSource1,
                data: [],
            },
            {
                name: !step_E.dataSource2 ? t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DataSourceTwo"
                ) : step_E.dataSource2,
                data: [],
            },
            {
                name: !step_E.dataSource3 ? t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DataSourceThree"
                ) : step_E.dataSource3,
                data: [],
            },
        ]
    }

    // Line chart data array
    const [lineChartData, setLineChartData] = useState<Array<LineDatamodel>>(lineChartDataSource);

    // Line chart xAxisTitle
    const xAxisTitle = t("indicators-responses:Common:Year");

    //Generate graph handler
    const generateLineGraphHandler = (step_E: Step_E_Response) => {
        const lineChartData = lineChartDataSource();

        const yearData = step_E.nationalLevelDeathOverTimeData.map(
            (item: MalariaDeathsOverTimeData) => item.year || ""
        );
        const dataSourceOne = step_E.nationalLevelDeathOverTimeData.map(
            (item: MalariaDeathsOverTimeData) => item.dataSource1Deaths
        );
        const dataSourceTwo = step_E.nationalLevelDeathOverTimeData.map(
            (item: MalariaDeathsOverTimeData) => item.dataSource2Deaths
        );
        const dataSourceThree = step_E.nationalLevelDeathOverTimeData.map(
            (item: MalariaDeathsOverTimeData) => item.dataSource3Deaths
        );
        setlineChartxAxisTitles(yearData);
        setLineChartData([
            { name: lineChartData[0].name, data: dataSourceOne },
            { name: lineChartData[1].name, data: dataSourceTwo },
            { name: lineChartData[2].name, data: dataSourceThree },
        ]);
    };

    return (
        <>
            <div className="response-wrapper">
                <div className="response-content">
                    <div className="row mb-3">
                        <div className="col-xs-12 col-md-6 col-lg-12">
                            <div className="row">
                                <div className="col-xs-12 col-md-12">
                                    <p>
                                        {parse(t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseDescETitle"
                                        ))}
                                    </p>

                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseDescENote"
                                    )}
                                </div>
                            </div>

                            <div className="row graph-wrapper section-graph-wrapper">
                                <div className="col-xs-12 col-md-12 col-lg-12">
                                    <div className="mt-3">
                                        <Table className="app-table table">
                                            <>
                                                <TableHeader headers={tableHeaders} />
                                                <thead>
                                                    <th></th>
                                                    <th>
                                                        <TextBox
                                                            className="col-form-control inputfocus"
                                                            type="string"
                                                            id="dataSource1"
                                                            name="dataSource1"
                                                            fullWidth
                                                            multiline
                                                            rows={2}
                                                            placeholder={t(
                                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:MalariaInpatientDeaths"
                                                            )}
                                                            value={step_E?.dataSource1}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onDataSourceValueChange(
                                                                    "dataSource1",
                                                                    e.currentTarget.value
                                                                )
                                                            }
                                                            error={
                                                                errors["step_E.dataSource1"] &&
                                                                errors["step_E.dataSource1"]
                                                            }
                                                            helperText={
                                                                errors["step_E.dataSource1"] &&
                                                                errors["step_E.dataSource1"]
                                                            }
                                                        />
                                                    </th>
                                                    <th>
                                                        <TextBox
                                                            className="col-form-control inputfocus"
                                                            type="string"
                                                            id="dataSource2"
                                                            name="dataSource2"
                                                            multiline
                                                            fullWidth
                                                            rows={2}
                                                            placeholder={t(
                                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:MalariaHospitalDeaths"
                                                            )}
                                                            value={step_E?.dataSource2}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onDataSourceValueChange(
                                                                    "dataSource2",
                                                                    e.currentTarget.value
                                                                )
                                                            }
                                                            error={
                                                                errors["step_E.dataSource2"] &&
                                                                errors["step_E.dataSource2"]
                                                            }
                                                            helperText={
                                                                errors["step_E.dataSource2"] &&
                                                                errors["step_E.dataSource2"]
                                                            }
                                                        />
                                                    </th>
                                                    <th>
                                                        <TextBox
                                                            className="col-form-control inputfocus"
                                                            type="string"
                                                            id="dataSource3"
                                                            name="dataSource3"
                                                            fullWidth
                                                            multiline
                                                            rows={2}
                                                            placeholder={t(
                                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:TotalMalariaDeaths"
                                                            )}
                                                            value={step_E?.dataSource3}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onDataSourceValueChange(
                                                                    "dataSource3",
                                                                    e.currentTarget.value
                                                                )
                                                            }
                                                            error={
                                                                errors["step_E.dataSource3"] &&
                                                                errors["step_E.dataSource3"]
                                                            }
                                                            helperText={
                                                                errors["step_E.dataSource3"] &&
                                                                errors["step_E.dataSource3"]
                                                            }
                                                        />
                                                    </th>
                                                </thead>

                                                <TableBody>
                                                    <>
                                                        {Object.keys(
                                                            step_E.nationalLevelDeathOverTimeData as [
                                                                MalariaDeathsOverTimeData
                                                            ]
                                                        ).map((modelKeyName: string, index: number) => {
                                                            return (
                                                                <tr id={`row_${index}`}>
                                                                    <TableCell>
                                                                        <Dropdown
                                                                            id="year"
                                                                            name="year"
                                                                            variant="outlined"
                                                                            size="small"
                                                                            label={t(
                                                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:YearOfData"
                                                                            )}
                                                                            options={years.map((year) => {
                                                                                return new MultiSelectModel(
                                                                                    year,
                                                                                    year.toString(),
                                                                                    false,
                                                                                    false
                                                                                );
                                                                            })}
                                                                            value={
                                                                                step_E.nationalLevelDeathOverTimeData[
                                                                                    index
                                                                                ]?.year
                                                                            }
                                                                            onChange={(
                                                                                e: React.ChangeEvent<HTMLInputElement>
                                                                            ) =>
                                                                                onValueChange(
                                                                                    "year",
                                                                                    +e.currentTarget.value,
                                                                                    index
                                                                                )
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <TextBox
                                                                            className="col-form-control inputfocus"
                                                                            type="number"
                                                                            id="dataSource1Deaths"
                                                                            name="dataSource1Deaths"
                                                                            placeholder={
                                                                                rowDataTextboxPlaceholder[modelKeyName]?.placeholderFirstColumn
                                                                            }
                                                                            fullWidth
                                                                            value={
                                                                                step_E.nationalLevelDeathOverTimeData[
                                                                                    index
                                                                                ]?.dataSource1Deaths
                                                                            }
                                                                            onChange={(
                                                                                e: React.ChangeEvent<HTMLInputElement>
                                                                            ) =>
                                                                                onValueChange(
                                                                                    "dataSource1Deaths",
                                                                                    e.currentTarget.value,
                                                                                    index
                                                                                )
                                                                            }
                                                                            error={
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource1Deaths`] &&
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource1Deaths`]
                                                                            }
                                                                            helperText={
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource1Deaths`] &&
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource1Deaths`]
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <TextBox
                                                                            className="col-form-control inputfocus"
                                                                            type="number"
                                                                            id="dataSource2Deaths"
                                                                            name="dataSource2Deaths"
                                                                            placeholder={
                                                                                rowDataTextboxPlaceholder[modelKeyName]?.placeholderSecondColumn
                                                                            }
                                                                            fullWidth
                                                                            value={
                                                                                step_E.nationalLevelDeathOverTimeData[
                                                                                    index
                                                                                ]?.dataSource2Deaths
                                                                            }
                                                                            onChange={(
                                                                                e: React.ChangeEvent<HTMLInputElement>
                                                                            ) =>
                                                                                onValueChange(
                                                                                    "dataSource2Deaths",
                                                                                    e.currentTarget.value,
                                                                                    index
                                                                                )
                                                                            }
                                                                            error={
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource2Deaths`] &&
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource2Deaths`]
                                                                            }
                                                                            helperText={
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource2Deaths`] &&
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource2Deaths`]
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <TextBox
                                                                            className="col-form-control inputfocus"
                                                                            type="number"
                                                                            id="dataSource3Deaths"
                                                                            name="dataSource3Deaths"
                                                                            placeholder={
                                                                                rowDataTextboxPlaceholder[modelKeyName]?.placeholderThirdColumn
                                                                            }
                                                                            fullWidth
                                                                            value={
                                                                                step_E.nationalLevelDeathOverTimeData[
                                                                                    index
                                                                                ]?.dataSource3Deaths
                                                                            }
                                                                            onChange={(
                                                                                e: React.ChangeEvent<HTMLInputElement>
                                                                            ) =>
                                                                                onValueChange(
                                                                                    "dataSource3Deaths",
                                                                                    e.currentTarget.value,
                                                                                    index
                                                                                )
                                                                            }
                                                                            error={
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource3Deaths`] &&
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource3Deaths`]
                                                                            }
                                                                            helperText={
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource3Deaths`] &&
                                                                                errors[`step_E.nationalLevelDeathOverTimeData[${index}].dataSource3Deaths`]
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                </tr>
                                                            );
                                                        })}
                                                        <tr className="text-center">
                                                            <td colSpan={4}>
                                                                {Object.keys(
                                                                    step_E.nationalLevelDeathOverTimeData as [
                                                                        MalariaDeathsOverTimeData
                                                                    ]
                                                                ).length > 1 && (
                                                                        <IconButton onClick={onRowDelete}>
                                                                            <Delete />
                                                                        </IconButton>
                                                                    )}
                                                                <IconButton onClick={onRowAdd}>
                                                                    <Add />
                                                                </IconButton>
                                                            </td>
                                                        </tr>
                                                    </>
                                                </TableBody>
                                            </>
                                        </Table>
                                    </div>

                                    <div className="text-center">
                                        <Button
                                            className="btn app-btn-secondary mt-3 px-4"
                                            onClick={() => generateLineGraphHandler(step_E)}
                                        >
                                            {t("indicators-responses:Common:GenerateGraph")}
                                        </Button>
                                    </div>
                                </div>

                                <div className="col-xs-12 col-md-12">
                                    <Chart
                                        style={{
                                            height: 350,
                                        }}
                                    >
                                        <ChartTitle text={linechartTitle} />
                                        <ChartLegend position="top" orientation="horizontal" />
                                        <ChartCategoryAxis>
                                            <ChartCategoryAxisItem
                                                title={{ text: xAxisTitle }}
                                                categories={lineChartxAxisTitles}
                                                startAngle={45}
                                            />
                                        </ChartCategoryAxis>
                                        <ChartSeries>
                                            {lineChartData.map((item, idx) => (
                                                <ChartSeriesItem
                                                    key={idx}
                                                    type="line"
                                                    tooltip={{
                                                        visible: true,
                                                    }}
                                                    data={item.data}
                                                    name={item.name}
                                                />
                                            ))}
                                        </ChartSeries>
                                    </Chart>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Indicator_1_1_7_Step_E;
