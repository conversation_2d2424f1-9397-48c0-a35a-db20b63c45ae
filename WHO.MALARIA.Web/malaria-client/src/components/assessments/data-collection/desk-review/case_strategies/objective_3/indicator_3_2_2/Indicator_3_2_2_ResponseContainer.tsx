import { <PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_2/Response_1";
import { StepperModel } from "../../../../../../../models/StepperModel";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import WHOStepper from "../../../../../../controls/WHOStepper";
import useIndicatorResponseCaptureForTabs from "../../../responses/useIndicatorResponseCaptureForTabs";
import Indicator_3_2_2_Step_1 from "./Indicator_3_2_2_Step_1";
import Indicator_3_2_2_Step_2 from "./Indicator_3_2_2_Step_2";
import useFormValidation from "../../../../../../common/useFormValidation";
import { CommonValidationRules, EliminationValidationRules } from "./ValidationRules";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";

/** Renders the Indicator response for 3.2.2 */
function Indicator_3_2_2_ResponseContainer() {
    const { t } = useTranslation(["indicators-responses"]);

    let ValidationRules: IValidationRuleProvider;
    ValidationRules = {
        ...CommonValidationRules,
        ...EliminationValidationRules,
    };

    const steps: Array<StepperModel> = [
        new StepperModel(0, "A", <></>),
        new StepperModel(1, "B", <></>),
    ];

    const [currentStep, setCurrentStep] = useState<number>(0);
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(updateStepIndex(0));
        getResponse();
    }, []);

    // Trrigers whenever user changes the step
    const onStepChange = (curerntStep: number) => {
        setCurrentStep(curerntStep);
        dispatch(updateStepIndex(curerntStep));
    };

    /** Renders the component based on step */
    const renderStepComponent = () => {
        switch (currentStep) {
            case 0:
                return (
                    <Indicator_3_2_2_Step_1
                        step_A={response?.step_A}
                        updateStep_A={updateStep_A}
                        onValueChange={onValueChange}
                    />
                );
            case 1:
                return (
                    <Indicator_3_2_2_Step_2
                        step_B={response.step_B}
                        updateStep_B={updateStep_B}
                    />
                );
        }
    };

    const {
        response,
        updateStep_A,
        updateStep_B,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCaptureForTabs<Response_1>(Response_1.init());

    return (
        <>
            <div className="response-assess-wrapper h-45"></div>
            <div className="response-wrapper">
                {!!Object.keys(errors).length && (
                    <span className="Mui-error d-flex mt-2">
                        *
                        {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:ResponseErrorAToB"
                        )}
                    </span>
                )}
                <WHOStepper
                    alternativeLabel={false}
                    nonLinear={true}
                    steps={steps}
                    activeStep={currentStep}
                    enableStepClick
                    onStepChange={onStepChange}
                >
                    <>{renderStepComponent()}</>
                </WHOStepper>
            </div>
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />+
        </>
    );
}

export default Indicator_3_2_2_ResponseContainer;