﻿import { Button } from "@mui/material";
import { useEffect } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import {
    Response_1,
    InformationalInterview,
} from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_2_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 4.2.1 response for desk review */
const Indicator_4_2_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_4_Indicator_4_2_1_Title"
    );

    const headers = [
        {
            field: "questions",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:Questions"
            ),
        },
        {
            field: "trueFalse",
            label: t(
                "indicators-responses:Common:True"
            ),
        },
        {
            field: "details",
            label: t("indicators-responses:Common:Details"),
        },
    ];

    const columns = [
        {
            field: "organizationForUseOfInformation",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:OrganizationForUseOfInformation"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:ProvideDetailsPlaceholder"
            ),
        },
        {
            field: "organizationPromotesFeedback",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:OrganizationPromotesFeedback"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:HowPlaceholder"
            ),
        },
        {
            field: "organizationEmpowersSurveillanceStaff",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:OrganizationEmpowersSurveillanceStaff"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:HowPlaceholder"
            ),
        },
        {
            field: "actionTakenBasedOnData",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:ActionTakenBasedOnData"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:WhatActionsPlaceholder"
            ),
        },
        {
            field: "dataUsefulToSurveillanceStaff",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:DataUsefulToSurveillanceStaff"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:HowPlaceholder"
            ),
        },
        {
            field: "surveillanceTasksNotBurden",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:SurveillanceTasksNotBurden"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:ExplainReasonsPlaceholder"
            ),
        },
        {
            field: "responsibilityOfHealthCareProviders",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:ResponsibilityOfHealthCareProviders"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:ExplainYesNoPlaceholder"
            ),
        },
        {
            field: "staffPaidOnTime",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:StaffPaidOnTime"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_2_1:ExplainGapsPlaceholder"
            ),
        },
    ];

    const informationalInterviews = columns.map(
        (column: any) => new InformationalInterview(null, null)
    );

    const {
        response,
        onChangeWithIndex,
        onSave,
        onFinalize,
        getResponse,
    } = useIndicatorResponseCapture<Response_1>(
        new Response_1(informationalInterviews)
    );

    useEffect(() => {
        getResponse();
    }, []);

    return (
        <>
            <div className="response-wrapper">


                <Table>
                    <>
                        <TableHeader headers={headers.map((header: any) => header.label)} />
                        <TableBody>
                            <>
                                {columns.map((column: any, index: number) => (
                                    <TableRow key={`column_${column.field}_${index}`}>
                                        <>
                                            <TableCell>
                                                <label>{column.label}</label>
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="trueOrFalse"
                                                    name="trueOrFalse"
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(
                                                            true,
                                                            t("indicators-responses:Common:Yes")
                                                        ),
                                                        new MultiSelectModel(
                                                            false,
                                                            t("indicators-responses:Common:No")
                                                        ),
                                                    ]}
                                                    value={
                                                        response?.informationalInterviews[index]?.trueOrFalse
                                                    }
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onChangeWithIndex(
                                                            e,
                                                            "informationalInterviews",
                                                            index
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <TextBox
                                                    id={`${column.field}_${index}_${Math.random()}`}
                                                    name="details"
                                                    placeholder={column.placeholderFirstColumn}
                                                    fullWidth
                                                    multiline
                                                    rows={3}
                                                    value={
                                                        response?.informationalInterviews[index]?.details ||
                                                        ""
                                                    }
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                        onChangeWithIndex(
                                                            e,
                                                            "informationalInterviews",
                                                            index
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                        </>
                                    </TableRow>
                                ))}
                            </>
                        </TableBody>
                    </>
                </Table>
            </div>
            <SaveFinalizeButton onSave={onSave} onFinalize={onFinalize} />
        </>
    );
};

export default Indicator_4_2_1_Response;
