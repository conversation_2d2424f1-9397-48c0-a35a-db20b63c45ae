﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, useRef, useState, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableBody from "../../../responses/TableBody";
import IndicatorLineGraph from "../IndicatorLineGraph";
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import useMonths from "../../../../../../../services/commonService";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_5/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import parse from "html-react-parser";
import Dropdown from "../../../../../../controls/Dropdown";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import useYears from "../../../../useYears";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";

/** Renders the response for indicator 1.3.5 */
const Indicator_1_3_5_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_1_Indicator_1_3_5_Title"
  );
  const months = useMonths().map((item) => {
    return { key: item.key, value: null };
  });
  const years = useYears();
  const { calculatePercentage } = useCalculation();
  const [isLoaded, setIsLoaded] = useState<boolean>(true);
  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

  const validate = useFormValidation(validationRulesRef.current);

  const {
    response,
    onChange,
    onCannotBeAssessed,
    onChangeWithKey,
    onSave,
    onFinalize,
    onValueChange,
    getResponse,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCapture<Response_1>(
    Response_1.init(months),
    validate
  );
  // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
  const errors = useSelector((state: any) => state.error);
  let isProportionRateValid: boolean = true;

  useEffect(() => {
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;
  }, [response?.cannotBeAssessed]);

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid && isProportionRateValid) {
      onFinalize();
    }
  };

  //Triggers onChange of cannotBeAssessed checkbox
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current = evt.currentTarget.checked
      ? CannotBeAssessedReasonValidationRule
      : ValidationRules;

    onCannotBeAssessed(evt);
  };

  // Triggers when input control's value changes
  const onCaseClassificationChange = (
    caseClassificationRates: Array<KeyValuePair<string, number | null>>
  ) => {
    const classificationValue: Array<KeyValuePair<string, number | null>> =
      caseClassificationRates.map(
        (dataValue: KeyValuePair<string, number | null>) => ({
          key: dataValue.key,
          value: dataValue.value
            ? Math.round(+dataValue.value)
            : dataValue.value === 0
            ? 0
            : null,
        })
      );
    onValueChange("caseClassificationRates", classificationValue);
    setIsLoaded(false);
  };

  // Check Value in Classfication to reload graph on load
  const checkValueInCaseClassification = (): boolean => {
    let checkValue: boolean = false;
    if (isLoaded) {
      response.caseClassificationRates.some(function (
        classiFicationvalue: KeyValuePair<string, number | null>
      ) {
        if (classiFicationvalue.value) {
          return (checkValue = true);
        }
      });
    }

    return checkValue;
  };

  //Check condition for met and not met and return status
  const getMetNotMetStatus = () => {
    const fociClassificationRate = calculatePercentage(
      response["caseClassificationRate"]?.numerator,
      response["caseClassificationRate"]?.denominator
    );

    onValueChange(
      "metNotMetStatus",
      fociClassificationRate >= 80
        ? MetNotMetEnum.Met
        : fociClassificationRate < 50
        ? MetNotMetEnum.NotMet
        : MetNotMetEnum.PartiallyMet
    );
  };

  useEffect(() => {
    getMetNotMetStatus();
  }, [
    response["caseClassificationRate"]?.numerator,
    response["caseClassificationRate"]?.denominator,
  ]);

  //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100
  const calculatePercentageRange = (
    propertyName1: number,
    propertyName2: number
  ) => {
    const proportionRateValue = calculatePercentage(
      propertyName1,
      propertyName2
    );

    const proportionRateExceptionContent = (
      <span className="Mui-error d-flex mb-2">
        * {t("indicators-responses:Common:ResponseProportionError")}
      </span>
    );

    if (proportionRateValue >= 0 && proportionRateValue <= 100) {
      isProportionRateValid = true;
      return proportionRateValue + "%";
    }

    isProportionRateValid = false;
    return proportionRateExceptionContent;
  };

  return (
    <>
      <MetNotMetStatus
        status={response.metNotMetStatus}
        tooltip={t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:MetNotMetTooltip"
        )}
      />

      <div className="response-assess-wrapper">
        <Checkbox
          id="cannotBeAssessed"
          name="cannotBeAssessed"
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response?.cannotBeAssessed}
        />
      </div>
      {!response?.cannotBeAssessed ? (
        <div className="response-wrapper">
          <p className="fw-lighter">
            {t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:ResponseDesc"
            )}
          </p>
          <p>
            {t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:SectionShouldBeCompletedAssessment"
            )}
          </p>
          <div className="mt-3">
            <table className="app-table minWidth">
              <thead>
                <th>
                  <div className="fw-bold"></div>
                </th>
                <th>
                  <div className="fw-bold">
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:NoOfConfirmedCases"
                    )}
                  </div>
                </th>
                <th>
                  <div className="fw-bold">
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:NoOfInvestigatingCases"
                    )}
                    &nbsp;
                    <IconButton className="grid-icon-button" size="small">
                      <Tooltip
                        content={t(
                          "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:InvestigatingCasesInfo"
                        )}
                        isHtml
                      >
                        <InfoIcon fontSize="small" />
                      </Tooltip>
                    </IconButton>
                  </div>
                </th>
                <th>
                  <div className="fw-bold">
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:Rate"
                    )}
                  </div>
                </th>
                <th>
                  <div>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:EvidenceOfFollowUp"
                    )}
                  </div>
                </th>
              </thead>
              <TableBody>
                <>
                  <TableRow
                    className={!!Object.keys(errors).length ? "app-error" : ""}
                  >
                    <>
                      <TableCell>
                        {parse(
                          t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:ClassClassificationRate"
                          )
                        )}
                      </TableCell>
                      <TableCell>
                        <TextBox
                          id="numerator"
                          name="numerator"
                          type="number"
                          fullWidth
                          className="col-form-control inputfocus"
                          InputLabelProps={{ shrink: true }}
                          value={response["caseClassificationRate"]?.numerator}
                          onChange={(
                            e: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            onChangeWithKey(e, "caseClassificationRate");
                            getMetNotMetStatus();
                          }}
                          error={
                            errors["caseClassificationRate.numerator"] &&
                            errors["caseClassificationRate.numerator"]
                          }
                          helperText={
                            errors["caseClassificationRate.numerator"] &&
                            errors["caseClassificationRate.numerator"]
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <TextBox
                          id="denominator"
                          name="denominator"
                          type="number"
                          fullWidth
                          className="col-form-control inputfocus"
                          InputLabelProps={{ shrink: true }}
                          value={
                            response["caseClassificationRate"]?.denominator
                          }
                          onChange={(
                            e: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            onChangeWithKey(e, "caseClassificationRate");
                            getMetNotMetStatus();
                          }}
                          error={
                            errors["caseClassificationRate.denominator"] &&
                            errors["caseClassificationRate.denominator"]
                          }
                          helperText={
                            errors["caseClassificationRate.denominator"] &&
                            errors["caseClassificationRate.denominator"]
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <label>
                          {calculatePercentageRange(
                            response["caseClassificationRate"]?.numerator,
                            response["caseClassificationRate"]?.denominator
                          )}
                        </label>
                      </TableCell>
                      <TableCell>
                        <TextBox
                          id="evidenceOfFollowupOrUse"
                          name="evidenceOfFollowupOrUse"
                          placeholder={t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:TextboxLabel"
                          )}
                          multiline
                          fullWidth
                          rows={2}
                          variant="outlined"
                          value={
                            response["caseClassificationRate"]
                              ?.evidenceOfFollowupOrUse
                          }
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onChangeWithKey(e, "caseClassificationRate")
                          }
                          error={
                            errors[
                              "caseClassificationRate.evidenceOfFollowupOrUse"
                            ] &&
                            errors[
                              "caseClassificationRate.evidenceOfFollowupOrUse"
                            ]
                          }
                          helperText={
                            errors[
                              "caseClassificationRate.evidenceOfFollowupOrUse"
                            ] &&
                            errors[
                              "caseClassificationRate.evidenceOfFollowupOrUse"
                            ]
                          }
                        />
                      </TableCell>
                    </>
                  </TableRow>
                </>
              </TableBody>
            </table>

            <div className="col-xs-12 col-md-4">
              <div className="row mb-2">
                <div className="col-xs-12 col-md-6">
                  <Dropdown
                    id="yearOfData"
                    name="yearOfData"
                    variant="outlined"
                    size="small"
                    label={t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:YearOfData"
                    )}
                    value={response?.yearOfData}
                    options={years.map((year) => {
                      return new MultiSelectModel(
                        year,
                        year.toString(),
                        false,
                        false
                      );
                    })}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      onValueChange("yearOfData", +e.currentTarget.value)
                    }
                    error={errors["yearOfData"] && errors["yearOfData"]}
                    helperText={errors["yearOfData"] && errors["yearOfData"]}
                  />
                </div>
              </div>
            </div>
            <div>
              {/*Show error message if not all radio buttons are selected for Data quality control check in place*/}

              <IndicatorLineGraph
                lineGraphXAxisText={t(
                  "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:Month"
                )}
                lineGraphYAxisText={t(
                  "indicators-responses:DRObjective_1_Responses:Indicator_1_3_3:Rate"
                )}
                description={
                  <>
                    <p></p>
                    <p className="fw-italic">
                      {t(
                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:IndicateRate"
                      )}
                    </p>
                  </>
                }
                renderTextbox={false}
                keyValuePairs={response.caseClassificationRates}
                onUpdatekeyValuePairs={onCaseClassificationChange}
                showGraphOnLoad={checkValueInCaseClassification()}
                details=""
                renderKeysInLabel={true}
                renderAddRemoveButton={false}
                graphTitle={`${t(
                  "indicators-responses:DRObjective_1_Responses:Indicator_1_3_5:DataForCaseClassification"
                )} (${response?.yearOfData || ""})`}
                keyValuePairsErrors={
                  errors["caseClassificationRates"] ||
                  errors["caseClassificationRates.value"]
                }
                className="app-table-scroll"
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="response-wrapper d-flex">
          <TextBox
            id="cannotBeAssessedReason"
            name="cannotBeAssessedReason"
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant="outlined"
            fullWidth
            value={response?.cannotBeAssessedReason || ""}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e)}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}

      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
    </>
  );
};
export default Indicator_1_3_5_Response;
