﻿import { useEffect, useRef, useState, ChangeEvent } from "react";
import { StepperModel } from "../../../../../../../models/StepperModel";
import WHOStepper from "../../../../../../controls/WHOStepper";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import Indicator_2_1_4_Burden_Step_A from "./Indicator_2_1_4_Burden_Step_A";
import Indicator_2_1_4_Burden_Step_B from "./Indicator_2_1_4_Burden_Step_B";
import { useDispatch } from "react-redux";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import { Response_4 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_4";
import useIndicatorResponseCaptureForTabs from "../../../responses/useIndicatorResponseCaptureForTabs";
import TextBox from "../../../../../../controls/TextBox";
import Checkbox from "../../../../../../controls/Checkbox";
import useCalculation from "../../../responses/useCalculation";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import useFormValidation from "../../../../../../common/useFormValidation";
import { CommonValidationRules, BurdenReductionValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";

/** Renders the response for indicator 2.1.4 for burden reduction strategy */
const Indicator_2_1_4_Burden_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculateProportionOfNullAndFalseValues } = useCalculation();
    document.title = t("indicators-responses:app:DR_Objective_2_Indicator_2_1_4_Title");

    //WHO Steppers
    const [currentStep, setCurrentStep] = useState<number>(0);
    let ValidationRules: IValidationRuleProvider;
    ValidationRules = {
        ...CommonValidationRules,
        ...BurdenReductionValidationRules,
    };

    const dispatch = useDispatch();
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        updateStep_A,
        updateStep_B,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCaptureForTabs<Response_4>(
        Response_4.init(),
        validate
    );

    useEffect(() => {
        dispatch(updateStepIndex(0));
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    //Steps for WHO Stepper
    const steps: Array<StepperModel> = [
        new StepperModel(0, "A"),
        new StepperModel(1, "B"),
    ];

    // Triggers when step is changed
    const onStepChange = (currentStep: number) => {
        setCurrentStep(currentStep);
        dispatch(updateStepIndex(currentStep));
    };

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    // Excluded property that are not used in Array Map
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "numberOfCriteria",
        "metNotMetStatus",
    ];

    //Method creates an array of properties that have checked "Yes"
    const getValuesOfModelProperty = (modelKeyName: string) => {
        const columnArray: boolean[] = Object.keys(response?.step_A[modelKeyName])
            .filter((key: string) => !excludedProperties.includes(key))
            .map(
                (rowData: string) =>
                    response?.step_A[modelKeyName][rowData]?.definitionOK
            );

        return columnArray;
    };

    //Calulates the proportion of columns for which the data is passed
    const calProportionOfCaseClassfnDefnTimeFrameValues = () => {
        const caseDefinitionArray: boolean[] =
            getValuesOfModelProperty("caseDefinition");

        const timeframeOkValues: boolean[] = Object.keys(response?.step_B)
            .filter((key: string) => !excludedProperties.includes(key))
            .map((row: string) => {
                return response?.step_B[row]?.timeframeOk;
            });

        return calculateProportionOfNullAndFalseValues([
            caseDefinitionArray,
            timeframeOkValues,
        ])
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const caseClassfnDefnTimeFrameValuesProportion =
            calProportionOfCaseClassfnDefnTimeFrameValues();

        onValueChange(
            "metNotMetStatus",
            caseClassfnDefnTimeFrameValuesProportion === 100
                ? MetNotMetEnum.Met
                : caseClassfnDefnTimeFrameValuesProportion === 0
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response?.step_A?.caseDefinition?.suspectedCase?.definitionOK,
        response?.step_A?.caseDefinition?.presumedCase?.definitionOK,
        response?.step_A?.caseDefinition?.confirmedCase?.definitionOK,
        response?.step_B?.notification?.timeframeOk,
    ]);

    const renderStepComponent = () => {
        switch (currentStep) {
            case 0:
                return (
                    <Indicator_2_1_4_Burden_Step_A
                        step_A={response?.step_A}
                        updateStep_A={updateStep_A}
                        getMetNotMetStatus={getMetNotMetStatus}
                    />
                );
            case 1:
                return (
                    <Indicator_2_1_4_Burden_Step_B
                        step_b={response?.step_B}
                        updateStep_B={updateStep_B}
                        onChange={onChange}
                        calProportionOfCaseClassfnDefnTimeFrameValues={
                            calProportionOfCaseClassfnDefnTimeFrameValues
                        }
                        getMetNotMetStatus={getMetNotMetStatus}
                    />
                );
        }
    };

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MetNotMetTooltip"
                )}
            />

            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <>
                    {!!Object.keys(errors).length && (
                        <span className="Mui-error d-flex mb-2">
                            *
                            {t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseError"
                            )}
                        </span>
                    )}
                    <WHOStepper
                        enableStepClick
                        steps={steps}
                        activeStep={currentStep}
                        alternativeLabel={false}
                        nonLinear={true}
                        onStepChange={onStepChange}
                    >
                        {renderStepComponent()}
                    </WHOStepper>
                </>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    <Button
                        className={classNames("btn", "app-btn-secondary")}
                        onClick={onSave}
                    >
                        {t("indicators-responses:Common:Save")}
                    </Button>
                    <Button
                        className={classNames("btn", "app-btn-primary")}
                        onClick={onResponseFinalize}
                    >
                        {t("indicators-responses:Common:Finalize")}
                    </Button>
                </div>
            </div>
        </>
    );
};

export default Indicator_2_1_4_Burden_Response;