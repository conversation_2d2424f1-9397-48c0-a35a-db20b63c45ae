﻿import * as React from "react";
import { useState } from "react";
import { StepperModel } from "../../../../../../../models/StepperModel";
import { TabModel } from "../../../../../../../models/TabModel";
import WHOStepper from "../../../../../../controls/WHOStepper";
import WHOTabs from "../../../../../../controls/WHOTabs";
import Indicator1_1_1b from "./Indicator_1_1_1_Step_b";
import Indicator1_1_1c from "./Indicator_1_1_1_Step_c";
import Indicator1_1_1d from "./Indicator_1_1_1_Step_d";
import Indicator1_1_1e from "./Indicator_1_1_1_Step_e";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import { useDispatch, useSelector } from "react-redux";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import { useEffect } from "react";
import Response_1_1_1 from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_1/Response_1";
import useIndicatorResponseCaptureForTabs from "../../../../desk-review/responses/useIndicatorResponseCaptureForTabs";
import Indicator_1_1_1_Step_a from "./Indicator_1_1_1_Step_a";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from './ValidationRules';
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 1.1.1 */
const Indicator_1_1_1_Response = () => {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_1_Indicator_1_1_1_Title"
  );

  const [currentTab, setCurrentTab] = useState<number>(0);
  const validate = useFormValidation(ValidationRules);
  const dispatch = useDispatch();
  const {
    response,
    getResponse,
    updateStep_A,
    updateStep_B,
    updateStep_C,
    updateStep_D,
    updateStep_E,
    onSave,
    onFinalize,
    setTrueFlagOnFinalizeButtonClick
  } = useIndicatorResponseCaptureForTabs<Response_1_1_1>(Response_1_1_1.init(), validate);

  //WHO Steppers
  const [currentStep, setCurrentStep] = useState<number>(0);
  const errors = useSelector((state: any) => state.error);

  useEffect(() => {
    dispatch(updateStepIndex(0));
    getResponse();
  }, []);

  //Steps for WHO Stepper
  const steps: Array<StepperModel> = [
    new StepperModel(0, "A"),
    new StepperModel(1, "B"),
    new StepperModel(2, "C"),
    new StepperModel(3, "D"),
    new StepperModel(4, "E"),
  ];

  //WHO tabs
  const tabs: Array<TabModel> = [
    new TabModel(
      1,
      t(
        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:TabLabel1"
      ),
      (
        <Indicator_1_1_1_Step_a
          step_A={response.step_A}
          updateStep_A={updateStep_A}
          section={1}
          graphTitle={t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:TabLabel1"
          )}
        />
      )
    ),
    new TabModel(
      2,
      t(
        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:TabLabel2"
      ),
      (
        <Indicator_1_1_1_Step_a
          step_A={response.step_A}
          updateStep_A={updateStep_A}
          section={2}
          graphTitle={`${t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:BarGraphTitle"
          )} (${response.step_A?.section_1_1_1_b?.yearOfData})`}
        />
      )
    ),
  ];

  //Handler for WHOTabs
  const onChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Triggers when step is changed
  const onStepChange = (currentStep: number) => {
    setCurrentStep(currentStep);
    dispatch(updateStepIndex(currentStep));
  };

  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid) {
      onFinalize();
    }
  }

  const renderStepComponent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="app-tab-wrapper">
            <WHOTabs
              tabs={tabs}
              scrollable={false}
              onChange={onChange}
              value={currentTab}
            >
              <div className="p-3">{tabs[currentTab].children}</div>
            </WHOTabs>
          </div>
        );
      case 1:
        return (
          <Indicator1_1_1b
            step_B={response.step_B}
            updateStep_B={updateStep_B}
          />
        );
      case 2:
        return (
          <Indicator1_1_1c
            step_C={response.step_C}
            updateStep_C={updateStep_C}
          />
        );
      case 3:
        return (
          <Indicator1_1_1d
            step_D={response.step_D}
            updateStep_D={updateStep_D}
          />
        );
      default:
        return (
          <Indicator1_1_1e
            step_E={response.step_E}
            updateStep_E={updateStep_E}
          />
        );
    }
  };

  return (
    <>
      <div className="response-assess-wrapper h-45"></div>
      <div className="response-wrapper">
        {!!Object.keys(errors).length && (
          <span className="Mui-error d-flex mt-2">
            *
            {t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ResponseErrorAToE"
            )}
          </span>
        )}
        <WHOStepper
          enableStepClick
          steps={steps}
          activeStep={currentStep}
          alternativeLabel={false}
          nonLinear={true}
          onStepChange={onStepChange}
        >
          <div className="py-2">{renderStepComponent()}</div>
        </WHOStepper>

        <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
      </div>
    </>
  );
};

export default Indicator_1_1_1_Response;
