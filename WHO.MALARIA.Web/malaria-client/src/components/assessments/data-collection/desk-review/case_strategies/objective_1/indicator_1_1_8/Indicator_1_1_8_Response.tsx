﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import { Add, Delete } from "@mui/icons-material";
import classNames from "classnames";
import { useEffect, useRef, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import TableCell from "../../../responses/TableCell";
import {
    Response_1,
    TreatmentFailure,
} from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_8/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import {
    CommonValidationRules,
    EliminationValidationRule,
} from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";

/** Renders the response for indicator 1.1.8 */
const Indicator_1_1_8_Response = () => {
    const location: any = useLocation();
    const { calculateRate } = useCalculation();
    const strategyId: string = location?.state?.strategyId;
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_1_Indicator_1_1_8_Title"
    );
    let ValidationRules: IValidationRuleProvider;
    StrategiesEnum.BurdenReduction.toLowerCase() !== strategyId
        ? (ValidationRules = {
            ...CommonValidationRules,
            ...EliminationValidationRule,
        })
        : (ValidationRules = {
            ...CommonValidationRules,
        });
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onCannotBeAssessed,
        onChangeOfArrayWithIndex,
        onSave,
        onFinalize,
        onValueChange,
        getResponse,
        onChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(strategyId), validate);

    const errors = useSelector((state: any) => state.error);

    // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
    let isProportionRateValidFor28Days: boolean = true;
    let isProportionRateValidFor42Days: boolean = true;

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }
    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid && isProportionRateValidFor28Days && isProportionRateValidFor42Days) {
            onFinalize();
        }
    };

    // Triggered whenever 'Delete Row' button is clicked.
    const onRowDelete = () => {
        const newTableRows = response?.treatmentFailure.slice(
            0,
            response?.treatmentFailure.length - 1
        );

        onValueChange("treatmentFailure", newTableRows);
    };

    // Triggered whenever 'Add Row' button is clicked.
    const onRowAdd = () => {
        const newRow = TreatmentFailure.init();
        const newTableRows = [...response.treatmentFailure];
        newTableRows.push(newRow);
        onValueChange("treatmentFailure", newTableRows);
    };

    //Method creates the arrays for the particular column to calculate the failure rate
    const calculateFailureRateForProperty = (modelKeyName: string) => {
        let columnData: Array<number> = [];
        Object.keys(response?.treatmentFailure).map((item: string) => {
            columnData.push(response?.treatmentFailure[item][modelKeyName]);
        });

        return calculateRate(columnData, 10);
    };

    //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
    const calculateFailureRateProperty = (propertyName: string) => {
        const proportionRateValue = calculateFailureRateForProperty(propertyName);

        const proportionRateExceptionContent =
            <span className="Mui-error d-flex mb-2">
                * {t("indicators-responses:Common:ResponseProportionError")}
            </span>

        switch (propertyName) {
            case "treatmentFailureAtDay28Percent":
                if (proportionRateValue >= 0 && proportionRateValue <= 100) {
                    isProportionRateValidFor28Days = true;
                    return proportionRateValue + '%';
                }

                isProportionRateValidFor28Days = false;
                return proportionRateExceptionContent;

            case "treatmentFailureAtDay42Percent":
                if (proportionRateValue >= 0 && proportionRateValue <= 100) {
                    isProportionRateValidFor42Days = true;
                    return proportionRateValue + '%';
                }

                isProportionRateValidFor42Days = false;
                return proportionRateExceptionContent;
        }
    }

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {        
        const hasTreatmentFailureValueNull: boolean = response?.treatmentFailure &&
            response?.treatmentFailure[0].treatmentFailureAtDay28Percent !== null
            && response?.treatmentFailure[0].treatmentFailureAtDay42Percent !== null;
   
        const hasMet: Boolean = response?.treatmentFailure.map((treatmentFailure: TreatmentFailure) => ({ treatmentFailureAtDay28Percent: treatmentFailure.treatmentFailureAtDay28Percent, treatmentFailureAtDay42Percent: treatmentFailure.treatmentFailureAtDay42Percent })).every((z: any) => (z.treatmentFailureAtDay28Percent <= 9 && z.treatmentFailureAtDay42Percent <= 9));

        const hasNotMet: Boolean = response?.treatmentFailure.map((treatmentFailure: TreatmentFailure) => ({ treatmentFailureAtDay28Percent: treatmentFailure.treatmentFailureAtDay28Percent, treatmentFailureAtDay42Percent: treatmentFailure.treatmentFailureAtDay42Percent })).some((z: any) => (z.treatmentFailureAtDay28Percent >= 10 && z.treatmentFailureAtDay42Percent >= 10 || z.treatmentFailureAtDay28Percent === null && z.treatmentFailureAtDay42Percent === null));

        onValueChange(
            "metNotMetStatus",
            hasMet && hasTreatmentFailureValueNull
                ? MetNotMetEnum.Met
                : hasNotMet
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );

    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response.treatmentFailure,
        response.treatmentFailure.treatmentFailureAtDay28Percent,
        response.treatmentFailure.treatmentFailureAtDay42Percent,
    ]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:MetNotMetTooltip"
                )}
            />

            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="response-content">
                        <div className="row mb-3">
                            <div className="col-xs-12 col-md-6">
                                {/*Show error message if hasTESBeenCarriedOut is selected NO*/}
                                {errors["hasTESBeenCarriedOut"] && (
                                    <span className="Mui-error d-flex mb-2">
                                        *
                                        {t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseError"
                                        )}
                                    </span>
                                )}
                                <div className="radio-wrapper">
                                    <label>
                                        {t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:ResponseDesc"
                                        )}
                                    </label>
                                    <RadioButtonGroup
                                        id="hasTESBeenCarriedOut"
                                        name="hasTESBeenCarriedOut"
                                        row
                                        color="primary"
                                        options={[
                                            new MultiSelectModel(
                                                true,
                                                t("indicators-responses:Common:Yes")
                                            ),
                                            new MultiSelectModel(
                                                false,
                                                t("indicators-responses:Common:No")
                                            ),
                                        ]}
                                        value={response?.hasTESBeenCarriedOut}
                                        onChange={onChange}
                                    />
                                    <div className="row g-0">
                                        <div className="col-xs-12 col-md-6 pe-5">
                                            {t(
                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:ResponseRadioActionYes"
                                            )}
                                        </div>

                                        <div className="col-xs-12 col-md-6">
                                            {t(
                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:ResponseRadioActionNo"
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {response?.hasTESBeenCarriedOut && (
                                <>
                                    {" "}
                                    <div className="row my-3">
                                        <p>
                                            {t(
                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:ProvideDetails"
                                            )}
                                        </p>

                                        <div className="col-xs-12 col-md-3">
                                            <TextBox
                                                id="noOfSentinelSites"
                                                name="noOfSentinelSites"
                                                type="number"
                                                InputLabelProps={{ shrink: true }}
                                                label={t(
                                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:NoOfSentinelSites"
                                                )}
                                                value={response?.noOfSentinelSites}
                                                inputProps={{
                                                    min: 0,
                                                    max: 100,
                                                    maxLength: 3,
                                                }}
                                                fullWidth
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                    onChange(e);
                                                }}
                                                error={
                                                    errors["noOfSentinelSites"] &&
                                                    errors["noOfSentinelSites"]
                                                }
                                                helperText={
                                                    errors["noOfSentinelSites"] &&
                                                    errors["noOfSentinelSites"]
                                                }
                                            />
                                        </div>
                                        <div className="col-xs-12 col-md-3">
                                            <TextBox
                                                id="year"
                                                type="number"
                                                name="year"
                                                label={t(
                                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:Year"
                                                )}
                                                multiline
                                                InputLabelProps={{ shrink: true }}
                                                rows={1}
                                                variant="outlined"
                                                fullWidth
                                                maxLength={4}
                                                value={response?.year}
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                    onValueChange(
                                                        e.currentTarget.name,
                                                        +e.currentTarget.value
                                                    )
                                                }
                                                error={errors["year"] && errors["year"]}
                                                helperText={errors["year"] && errors["year"]}
                                            />
                                        </div>
                                        <div className="col-xs-12 col-md-6">
                                            <TextBox
                                                id="summaryDetails"
                                                name="summaryDetails"
                                                label={t(
                                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:ProvideSummary"
                                                )}
                                                multiline
                                                InputLabelProps={{ shrink: true }}
                                                rows={1}
                                                variant="outlined"
                                                fullWidth
                                                value={response?.summaryDetails}
                                                onChange={onChange}
                                                error={
                                                    errors["summaryDetails"] && errors["summaryDetails"]
                                                }
                                                helperText={
                                                    errors["summaryDetails"] && errors["summaryDetails"]
                                                }
                                            />
                                        </div>
                                    </div>
                                    <div className="mt-3">
                                        <table className="app-table">
                                            <thead>
                                                <th>
                                                    <div className="fw-bold">
                                                        {t(
                                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:DrugName"
                                                        )}
                                                    </div>
                                                </th>
                                                <th>
                                                    <div className="fw-bold">
                                                        {t(
                                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:TreatmentFailurePatientsDay28"
                                                        )}
                                                    </div>
                                                </th>
                                                <th>
                                                    <div className="fw-bold">
                                                        {t(
                                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:TreatmentFailurePatientsDay42"
                                                        )}
                                                    </div>
                                                </th>
                                                {StrategiesEnum.BurdenReduction.toLowerCase() !==
                                                    strategyId && (
                                                        <th>
                                                            <div className="fw-bold">
                                                                {t(
                                                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:PositivePatientOnDay3"
                                                                )}
                                                            </div>
                                                        </th>
                                                    )}
                                            </thead>
                                            <tbody>
                                                <>
                                                    {Object.keys(
                                                        response?.treatmentFailure as [TreatmentFailure]
                                                    ).map((row: any, index: number) => {
                                                        return (
                                                            <tr key={`row_${index}`}>
                                                                <TableCell>
                                                                    <TextBox
                                                                        id={`${index}_${Math.random()}`}
                                                                        className="col-form-control inputfocus"
                                                                        name="drugName"
                                                                        value={
                                                                            response?.treatmentFailure[index]
                                                                                ?.drugName
                                                                        }
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                        ) =>
                                                                            onChangeOfArrayWithIndex(
                                                                                e,
                                                                                "treatmentFailure",
                                                                                index
                                                                            )
                                                                        }
                                                                        error={
                                                                            errors[
                                                                            `treatmentFailure[${index}].drugName`
                                                                            ] &&
                                                                            errors[
                                                                            `treatmentFailure[${index}].drugName`
                                                                            ]
                                                                        }
                                                                        helperText={
                                                                            errors[
                                                                            `treatmentFailure[${index}].drugName`
                                                                            ] &&
                                                                            errors[
                                                                            `treatmentFailure[${index}].drugName`
                                                                            ]
                                                                        }
                                                                    />
                                                                </TableCell>
                                                                <TableCell>
                                                                    <TextBox
                                                                        id={`${index}_${Math.random()}`}
                                                                        className="col-form-control inputfocus"
                                                                        name="treatmentFailureAtDay28Percent"
                                                                        type="number"
                                                                        inputProps={{
                                                                            max: 100,
                                                                            min: 0,
                                                                            maxLength: 3,
                                                                        }}
                                                                        fullWidth
                                                                        value={
                                                                            response?.treatmentFailure[index]
                                                                                ?.treatmentFailureAtDay28Percent
                                                                        }
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                        ) => {
                                                                            onChangeOfArrayWithIndex(
                                                                                e,
                                                                                "treatmentFailure",
                                                                                index
                                                                            )
                                                                            getMetNotMetStatus();
                                                                        }
                                                                        }
                                                                        error={
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay28Percent`
                                                                            ] &&
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay28Percent`
                                                                            ]
                                                                        }
                                                                        helperText={
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay28Percent`
                                                                            ] &&
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay28Percent`
                                                                            ]
                                                                        }
                                                                    />
                                                                </TableCell>
                                                                <TableCell>
                                                                    <TextBox
                                                                        id={`${index}_${Math.random()}`}
                                                                        className="col-form-control inputfocus"
                                                                        name="treatmentFailureAtDay42Percent"
                                                                        type="number"
                                                                        inputProps={{
                                                                            max: 100,
                                                                            min: 0,
                                                                            maxLength: 3,
                                                                        }}
                                                                        fullWidth
                                                                        value={
                                                                            response?.treatmentFailure[index]
                                                                                ?.treatmentFailureAtDay42Percent
                                                                        }
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>
                                                                        ) => {
                                                                            onChangeOfArrayWithIndex(
                                                                                e,
                                                                                "treatmentFailure",
                                                                                index
                                                                            )
                                                                            getMetNotMetStatus();
                                                                        }
                                                                        }
                                                                        error={
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay42Percent`
                                                                            ] &&
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay42Percent`
                                                                            ]
                                                                        }
                                                                        helperText={
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay42Percent`
                                                                            ] &&
                                                                            errors[
                                                                            `treatmentFailure[${index}].treatmentFailureAtDay42Percent`
                                                                            ]
                                                                        }
                                                                    />
                                                                </TableCell>
                                                                {StrategiesEnum.BurdenReduction.toLowerCase() !==
                                                                    strategyId && (
                                                                        <TableCell>
                                                                            <TextBox
                                                                                id={`${index}_${Math.random()}`}
                                                                                className="col-form-control inputfocus"
                                                                                name="patientsPositiveOnDay3Percent"
                                                                                type="number"
                                                                                inputProps={{
                                                                                    max: 100,
                                                                                    min: 0,
                                                                                    maxLength: 3,
                                                                                }}
                                                                                fullWidth
                                                                                value={
                                                                                    response?.treatmentFailure[index]
                                                                                        ?.patientsPositiveOnDay3Percent
                                                                                }
                                                                                onChange={(
                                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                                ) =>
                                                                                    onChangeOfArrayWithIndex(
                                                                                        e,
                                                                                        "treatmentFailure",
                                                                                        index
                                                                                    )
                                                                                }
                                                                                error={
                                                                                    errors[
                                                                                    `treatmentFailure[${index}].patientsPositiveOnDay3Percent`
                                                                                    ] &&
                                                                                    errors[
                                                                                    `treatmentFailure[${index}].patientsPositiveOnDay3Percent`
                                                                                    ]
                                                                                }
                                                                                helperText={
                                                                                    errors[
                                                                                    `treatmentFailure[${index}].patientsPositiveOnDay3Percent`
                                                                                    ] &&
                                                                                    errors[
                                                                                    `treatmentFailure[${index}].patientsPositiveOnDay3Percent`
                                                                                    ]
                                                                                }
                                                                            />
                                                                        </TableCell>
                                                                    )}
                                                            </tr>
                                                        );
                                                    })}
                                                    <tr className="text-center">
                                                        <td
                                                            colSpan={
                                                                StrategiesEnum.BurdenReduction.toLowerCase() !==
                                                                    strategyId
                                                                    ? 4
                                                                    : 3
                                                            }
                                                        >
                                                            {Object.keys(
                                                                response?.treatmentFailure as [TreatmentFailure]
                                                            ).length > 1 && (
                                                                    <IconButton onClick={onRowDelete}>
                                                                        <Delete />
                                                                    </IconButton>
                                                                )}
                                                            <IconButton onClick={onRowAdd}>
                                                                <Add />
                                                            </IconButton>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            {t(
                                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:FailureRate"
                                                            )}
                                                        </td>

                                                        <TableCell>
                                                            <label>
                                                                {calculateFailureRateProperty(
                                                                    "treatmentFailureAtDay28Percent"
                                                                )}
                                                            </label>
                                                        </TableCell>

                                                        <TableCell>
                                                            <label>
                                                                {calculateFailureRateProperty(
                                                                    "treatmentFailureAtDay42Percent"
                                                                )}
                                                            </label>
                                                        </TableCell>
                                                        {StrategiesEnum.BurdenReduction.toLowerCase() !==
                                                            strategyId && <td></td>}
                                                    </tr>
                                                </>
                                            </tbody>
                                        </table>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        onChange={onChange}
                        value={response?.cannotBeAssessedReason}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_1_1_8_Response;
