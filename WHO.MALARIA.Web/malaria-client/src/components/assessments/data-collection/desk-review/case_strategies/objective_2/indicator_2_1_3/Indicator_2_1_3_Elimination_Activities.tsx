﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import React, { useEffect } from "react";
import TableFooter from "../../../responses/TableFooter";
import { IconButton } from "@mui/material";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import useCalculation from "../../../responses/useCalculation";

type Indicator_2_1_3_Props = {
    response: any;
    updateStep_A: (step_A: any) => void;
    onValueChange: (field: string, value: any) => void;
};

/** Renders the response for indicator 2.1.3 step 1 */
function Indicator_2_1_3_Elimination_Activities(props: Indicator_2_1_3_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_A, proportionRate } = props.response;
    const { updateStep_A, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);
    const { calculateProportionRate } = useCalculation();

    useEffect(() => {
        const proportionRate = calculateProportionRateForProperty("activityInPlace", "surveillanceImplemented");

        onValueChange("proportionRate", proportionRate);
    }, [step_A]);

    /** Excluded property that are not used in Array Map */
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
    ];

    // Triggered whenever the textbox control values are changed and update response
    const onFieldValueChange = (
        fieldName: string,
        value: any,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_A,
            [modelKeyName]: {
                ...step_A[modelKeyName],
                [fieldName]: value,
            },
        };

        updateStep_A(_response);
    };

    // Triggered whenever the radio control values are changed and update response
    const onRadioButtonValueChange = (
        fieldName: string,
        value: any,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_A,
            [modelKeyName]: {
                ...step_A[modelKeyName],
                [fieldName]: value === "false" ? false : true,
            },
        };

        updateStep_A(_response);
    };

    const headers = [
        {
            field: "eliminationActivity",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:EliminationActivity"
            ),
        },
        {
            field: "activityInPlace",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ActivityInPlace"
            ),
        },
        {
            field: "surveillanceImplemented",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:SurveillanceImplemented"
            ),
        },
        {
            field: "dataIntegrated",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:DataIntegrated"
            ),
        },
        {
            field: "dataLinkage",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:DataLinkage"
            ),
        },
        {
            field: "detailsOfData",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:DetailsOfData"
            ),
        },
        {
            field: "nationalLevel",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:NationalLevel"
            ),
        },
        {
            field: "subNationalLevel",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:SubNationalLevel"
            ),
        },
        {
            field: "serviceDelivery",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ServiceDelivery"
            ),
        },
        {
            field: "challenges",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:Challenges"
            ),
        },
    ];

    const columns: any = {
        caseInvestigation: {
            field: "caseInvestigation",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:CaseInvestigation"
            ),
            placeholderOne: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:HowIsCaseInvestigation"
            ),
            placeholderTwo: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ReviewData"
            ),
            placeholderThree: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:LeadsOnInvestigation"
            ),
            placeholderFour: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ScreensContacts"
            ),
        },
        caseClassification: {
            field: "caseClassification",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:CaseClassification"
            ),
        },
        focusInvestigation: {
            field: "focusInvestigation",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:FocusInvestigation"
            ),
        },
        focusClassificiation: {
            field: "focusClassificiation",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:FocusClassification"
            ),
        },
        activeCaseDetection: {
            field: "activeCaseDetection",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ActiveCaseDetection"
            ),
        },
        reactiveDetection: {
            field: "reactiveDetection",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ReactiveDetection"
            ),
        },
        proactiveDetection: {
            field: "proactiveDetection",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ProactiveDetection"
            ),
        },
    };

    //Method creates the arrays for two different property to calculate the proportion rate
    const calculateProportionRateForProperty = (
        modelKeyNameOne: string,
        modelKeyNameTwo: string
    ) => {
        let columnOneSum: number = 0;
        let columnTwoSum: number = 0;

        Object.keys(step_A)
            .filter((key: string) => !excludedProperties.includes(key))
            .map((item: string) => {
                columnOneSum += step_A[item]?.[modelKeyNameOne];
                columnTwoSum += step_A[item]?.[modelKeyNameTwo];
            });

        return calculateProportionRate(columnOneSum, columnTwoSum);
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ResponseDesc"
                    )}
                </p>
                <div>
                    {
                        //Show error message if user does not select 'Yes' or 'No' for all activity in place and surveillance implemented
                        !!Object.keys(errors).filter((key: string) => !excludedProperties.includes(key)).length && (
                            <span className="Mui-error d-flex mb-2">
                                *
                                {t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ResponseStepAError"
                            )}
                            </span>                            
                        )
                    }
                    {
                        errors["proportionRate"] && (
                            <span className="Mui-error d-flex mb-2">{errors["proportionRate"]}</span>
                        )
                    }
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {Object.keys(step_A)
                                        .filter((key) => !excludedProperties.includes(key))
                                        .map((row: string, index: any) => (
                                            <TableRow key={`row_${columns.field}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <span>{columns[row]?.label}</span>
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="activityInPlace"
                                                            name="activityInPlace"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={step_A[row]?.activityInPlace}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onRadioButtonValueChange(
                                                                    "activityInPlace",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.${row}.activityInPlace`
                                                                ] &&
                                                                errors[
                                                                `step_A.${row}.activityInPlace`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="surveillanceImplemented"
                                                            name="surveillanceImplemented"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={
                                                                step_A[row]?.surveillanceImplemented
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onRadioButtonValueChange(
                                                                    "surveillanceImplemented",
                                                                    e.currentTarget.value,
                                                                    row
                                                                );
                                                            }}
                                                            error={
                                                                errors[
                                                                `step_A.${row}.surveillanceImplemented`
                                                                ] &&
                                                                errors[
                                                                `step_A.${row}.surveillanceImplemented`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="dataIntegratedWithRoutineCaseNotificationData"
                                                            name="dataIntegratedWithRoutineCaseNotificationData"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={
                                                                step_A[row]
                                                                    ?.dataIntegratedWithRoutineCaseNotificationData
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onRadioButtonValueChange(
                                                                    "dataIntegratedWithRoutineCaseNotificationData",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="dataLinkage"
                                                            name="dataLinkage"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={step_A[row]?.dataLinkage}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onRadioButtonValueChange(
                                                                    "dataLinkage",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="detailsOnDataLinkageToTheIndexCase"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={
                                                                step_A[row]?.detailsOnDataLinkageToTheIndexCase
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "detailsOnDataLinkageToTheIndexCase",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                            placeholder={columns[row]?.placeholderOne}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="nationalLevelOfInvolvement"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={step_A[row]?.nationalLevelOfInvolvement}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "nationalLevelOfInvolvement",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                            placeholder={columns[row]?.placeholderTwo}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="subNationalLevelOfInvolvement"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={step_A[row]?.subNationalLevelOfInvolvement}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "subNationalLevelOfInvolvement",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                            placeholder={columns[row]?.placeholderThree}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="serviceDeliveryLevelOfInvolvement"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={
                                                                step_A[row]?.serviceDeliveryLevelOfInvolvement
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "serviceDeliveryLevelOfInvolvement",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                            placeholder={columns[row]?.placeholderFour}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}_${Math.random()}`}
                                                            name="challengesWithReporting"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={step_A[row]?.challengesWithReporting}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "challengesWithReporting",
                                                                    e.currentTarget.value,
                                                                    row
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                </>
                            </TableBody>
                            <TableFooter>
                                <>
                                    <TableCell>
                                        <>
                                            {t(
                                                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:EliminationActivitiesFooterTitle"
                                            )}

                                            <IconButton className="grid-icon-button">
                                                <Tooltip
                                                    content={t(
                                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:EliminationActivitiesTooltip"
                                                    )}
                                                    isHtml
                                                >
                                                    <InfoIcon fontSize="small" />
                                                </Tooltip>
                                            </IconButton>
                                        </>
                                    </TableCell>
                                    <TableCell colSpan={10}>
                                        <label>{errors["proportionRate"] ? <span className="Mui-error d-flex">{errors["proportionRate"]}</span>
                                            : `${proportionRate}%`}</label>
                                    </TableCell>
                                </>
                            </TableFooter>
                        </>
                    </Table>
                </div>
            </div>
        </>
    );
}

export default Indicator_2_1_3_Elimination_Activities;