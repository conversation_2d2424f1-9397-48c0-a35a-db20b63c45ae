﻿import { Button } from "@mui/material";
import React, { useState } from "react";
import parse from "html-react-parser";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";

/** Renders the indicator 2.5.1 response  */
const Indicator_2_5_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_2_Indicator_2_5_1_Title");

    const [indicator2_5_1, setIndicator2_5_1] = useState<{}>(); //TODO: Change with response model

    // Triggered by all HTMLInputElement whenever the values are changed
    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        setIndicator2_5_1({
            ...indicator2_5_1,
            [evt.target.name]: evt.target.value,
        });
    };

    return (
        <>
            <div className="response-wrapper">

                <div className="row mb-3">
                    <div className="col-xs-12 col-md-8">

                        <div className="row mb-5">
                            <div className="col-xs-12 col-md-12">
                                <TextBox
                                    id="mutationsdetectedresults"
                                    label={t(
                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:MDATextboxLabelProportion"
                                    )}
                                    multiline
                                    rows={10}
                                    variant="outlined"
                                    fullWidth
                                    InputLabelProps={{
                                        shrink: true,
                                    }}
                                />
                            </div>
                        </div>

                        <div className="row">
                            <div className="col-xs-12 col-md-12">
                                <TextBox
                                    id="mutationsdetectedresults"
                                    label={parse(t(
                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:ITPITextboxLableTwo"
                                    ))}
                                    placeholder={t(
                                        "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:ITPITextboxPlaceholder"
                                    )}
                                    multiline
                                    rows={10}
                                    variant="outlined"
                                    fullWidth
                                    InputLabelProps={{
                                        shrink: true,
                                    }}
                                />
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    <Button className={classNames("btn", "app-btn-secondary")}>
                        {t("indicators-responses:Common:Save")}
                    </Button>
                    <Button className={classNames("btn", "app-btn-primary")}>
                        {t("indicators-responses:Common:Finalize")}
                    </Button>
                </div>
            </div>
        </>
    );
};

export default Indicator_2_5_1_Response;