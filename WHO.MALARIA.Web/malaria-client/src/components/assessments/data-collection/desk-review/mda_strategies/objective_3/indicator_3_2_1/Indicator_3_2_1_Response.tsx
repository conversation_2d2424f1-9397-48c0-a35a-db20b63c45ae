﻿import React, { ChangeEvent, useEffect, useRef } from 'react';
import { Button, IconButton, TextField } from "@mui/material";
import { Add, Delete } from "@mui/icons-material";
import classNames from "classnames";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader, { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import TableRow from "../../../responses/TableRow";
import { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';
import ValidationRules from '../../../mda_strategies/objective_3/indicator_3_2_1/ValidationRules';
import useFormValidation from '../../../../../../common/useFormValidation';
import useIndicatorResponseCapture from '../../../responses/useIndicatorResponseCapture';
import { NumberOfRecordingForm, Response_1 } from '../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_1/Response_1';
import { useSelector } from 'react-redux';
import InformationDialog from '../../../../../../common/InformationDialog';
import FileUploader from '../../../../../../controls/FileUploader';
import DatePicker from '../../../../../../controls/DatePicker';
// import { Date | null } from "@material-ui/pickers/typings/date";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { v4 as uuidv4 } from 'uuid';
import { DeskReviewAssessmentResponseStatus, StrategiesEnum } from "../../../../../../../models/Enums";
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import { useLocation } from 'react-router-dom';
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response indicator 3.2.1 */
function Indicator_3_2_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_2_1_Title");
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    const [isShowInformationDialog, setIsShowInformationDialog] =
        useState<boolean>(false);

    //Holds the validation rules and it gets changed when cannot be assessed is set to true.
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onValueChange,
        onChange,
        onCannotBeAssessed,
        onChangeOfArrayWithIndex,
        getResponse,
        onChangeInArrayWithIndexForProperty,
        setTrueFlagOnFinalizeButtonClick,
        saveResponseAndFiles,
        getResponseDocuments,
        addDeletedDocumentIds,
        convertToUTC
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(strategyId), validate);

    const errors = useSelector((state: any) => state.error);

    useEffect(() => {
        getResponse();
        getResponseDocuments();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const headerRows = [
        { field: "", label: "" },
        {
            field: "nameOfReportingToolSourceDocument",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:NameOfRecordingTool"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:OpdRegister"
            ),
        },
        {
            field: "toolType",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:ToolType"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:ToolTypePlaceholder"
            ),
        },
        {
            field: "yearToolWasIntroduced",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:YearToolWasIntroduced"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:YearToolPlaceholder"
            ),
        },
        {
            field: "frequencyDataReported",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:FrequenceyDataIsRecorded"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:DailyMonthly"
            ),
        },
        {
            field: "personResponsibleForReporting",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:ResponsiblePerson"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:HealthCareWorker"
            ),
        },
        {
            field: "recipientListVariablesReported",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:ListOfVariablesRecorded"
            ),
            placeholder: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_1:RecipientListVariablesReportedPlaceholder"
            ),
        },
        {
            field: "linkOrScreenshot",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:Screenshot"
            ),
            placeholder: "",
        },
        {
            field: "lastUpdatedDate",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:DateOfLastUpdated"
            ),
            placeholder: "",
        },
        { field: "", label: "" },
    ];

    // Assigning previous records in an array and pushing an empty object in case new row is added with blank data
    const addNewRow = () => {
        onValueChange("recordingTools", [...response?.recordingTools, new NumberOfRecordingForm(Math.round(new Date().getTime() / 1000).toString())]);
    };

    // Remove last record from the recording tools
    const removeRow = (colIndex: number) => {
        if (colIndex === 0)
            // first element
            return;

        const documentId = response.recordingTools[response.recordingTools.length - 1]?.documentId;
        if (documentId) {
            addDeletedDocumentIds(documentId);
        }

        const _response = response.recordingTools.slice(
            0,
            response?.recordingTools.length - 1
        );
        onValueChange("recordingTools", _response);
    };

    // Renders the AddDeleteIcon component
    const AddDeleteIcon = ({ rowIndex = 0, colIndex = 0 }) => {
        return (
            <>
                {colIndex === response?.recordingTools.length - 1 && (
                    <IconButton
                        hidden={rowIndex < headerRows.length - 1}
                        onClick={addNewRow}
                    >
                        <Add className="icon-button-primary" />
                    </IconButton>
                )}

                {colIndex > 0 && colIndex === response?.recordingTools.length - 1 && (
                    <IconButton
                        hidden={rowIndex < headerRows.length - 1}
                        onClick={() => removeRow(colIndex)}
                    >
                        <Delete className="icon-button-primary" />
                    </IconButton>
                )}
            </>
        );
    };

    //Returns file uploader, visibility and delete icon for screenshot
    const AddScreenshotControls = ({ colIndex = 0 }) => {
        const url: string = setScreenshot(colIndex);

        if (url) {
            return <>
                <IconButton title={t("indicators-responses:Common.ClickToView")}>
                    <VisibilityIcon onClick={() => window.open(url)} className="icon-button-primary" />
                </IconButton>
                <IconButton title={t("indicators-responses:Common.Delete")}>
                    <DeleteIcon onClick={() => onFileDelete(colIndex)} className="icon-button-primary" />
                </IconButton>
            </>;
        }

        return <FileUploader
            id={`linkOrScreenshot${colIndex}`}
            multiple
            accept=".png, .jpg, .jpeg"
            onChange={(
                evt: React.ChangeEvent<HTMLInputElement>
            ) => onFileUpload(colIndex, evt)}
        />;

    }

    //Set the validation error in UI for screenshot
    const SetScreenshotError = ({ colIndex = 0 }) => {
        let message: string = "";

        switch (true) {
            case !!errors[`recordingTools[${colIndex}].documentId`]:
                message = errors[`recordingTools[${colIndex}].documentId`];
                break;
            case !!errors[`recordingTools[${colIndex}].file`]:
                message = errors[`recordingTools[${colIndex}].file`]
                break;
        }

        return <span className="Mui-error d-flex mb-2">
            {message}
        </span>
    }

    //Process data on save and calls processDataAndSave method based on the isFinalized flag
    const onSave = () => {
        processDataAndSave(false)
    }

    //Process data on finalize and calls processDataAndSave method based on the isFinalized flag
    const onResponseFinalize = () => {
        processDataAndSave(true)
    }

    //Process data set the date control value then save or finalize
    const processDataAndSave = (isFinalized: boolean) => {
        const recordingTools = [...response?.recordingTools];
        recordingTools.forEach((numberOfRecordingForm: NumberOfRecordingForm) => {
            numberOfRecordingForm.lastUpdatedDate =
                numberOfRecordingForm.lastUpdatedDate
                    ? convertToUTC(new Date((numberOfRecordingForm.lastUpdatedDate)))
                    : null;
        });

        onValueChange("recordingTools", recordingTools);

        const files: Array<KeyValuePair<string, File>> = response.recordingTools.map((x: NumberOfRecordingForm) => ({ key: x.documentId, value: x.file }));

        // triggers on click of finalize button, performs validations and then action is performed
        if (isFinalized) {
            const isFormValid = validate(response);
            setTrueFlagOnFinalizeButtonClick();
            if (isFormValid) {
                if (saveResponseAndFiles(files, DeskReviewAssessmentResponseStatus.Completed))
                    setIsShowInformationDialog(true);
            }
        } else {
            saveResponseAndFiles(files, DeskReviewAssessmentResponseStatus.InProgress);
        }
    };

    //Upload screenhot and save in the state
    const onFileUpload = (index: number, evt: React.ChangeEvent<HTMLInputElement>) => {
        if (evt.target.files) {
            const recordingTools = [...response.recordingTools];
            recordingTools[index].documentId = uuidv4();
            recordingTools[index].file = evt.target.files[0];
            onValueChange("recordingTools", recordingTools);
        }
    }

    //Triggers when user click on delete button of File uploader
    const onFileDelete = (index: number): void => {
        const recordingTools = [...response.recordingTools];
        const recordingTool = recordingTools[index];
        const documentId = recordingTool.documentId;

        recordingTool.documentId = "";
        recordingTool.file = null;

        onValueChange("recordingTools", recordingTools);

        if (response?.documents) {
            const documents = response.documents.filter((x: any) => x.id !== documentId);
            onValueChange("documents", documents);
        }

        addDeletedDocumentIds(documentId);
    };

    //Generate URL from uploaded file or file contents which is base64 string
    const setScreenshot = (index: number) => {
        const recordingTool = response?.recordingTools[index];
        const file = recordingTool?.file;
        if (file) {
            return URL.createObjectURL(file);
        }

        //Get file content (base64 string)
        const content = response?.documents?.find((x: any) => x.id === recordingTool?.documentId)?.fileContent;
        if (content) {
            const byteCharacters = atob(content);
            const byteNumbers = new Array(byteCharacters.length);
            for (let byteCharIndex = 0; byteCharIndex < byteCharacters.length; byteCharIndex++) {
                byteNumbers[byteCharIndex] = byteCharacters.charCodeAt(byteCharIndex);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });
            return URL.createObjectURL(blob);
        }

        return "";
    }

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            <InformationDialog
                title={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:AssessChildInformationDialogTitle"
                )}
                content={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:AssessChildInformationDialogContent"
                )}
                open={isShowInformationDialog}
                onClick={() => {
                    setIsShowInformationDialog(false);
                }}
            />

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="respone-content">
                        <div>
                            {t(
                                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:MdaResponseDesc"
                            )}
                        </div>
                        <div className="mt-2">
                            {t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:InformationSystem")}
                        </div>
                        <div className="mt-2 fst-italic">
                            {t(
                                "indicators-responses:Common:NoteToCompleteAssessment"
                            )}
                        </div>
                        <div className="table-responsive">
                            <Table className="table app-table">
                                <>
                                    <TableHead>
                                        <>
                                            {headerRows.map((header: any, index: number) => (
                                                <TableHeaderCell key={`${header.field}_${index}`}>
                                                    <span className="fw-bold">{header.label}</span>
                                                </TableHeaderCell>
                                            ))}
                                        </>
                                    </TableHead>
                                    <TableBody>
                                        <>
                                            {Object.keys(
                                                response?.recordingTools as [NumberOfRecordingForm]
                                            ).map((row: any, colIndex: number) => {
                                                return (
                                                    <>
                                                        <TableRow key={`row_${colIndex}`}
                                                            className={
                                                                !!Object.keys(errors).length ? "app-error" : ""
                                                            }>
                                                            <>
                                                                {headerRows.map(
                                                                    (header: any, rowIndex: number) => (
                                                                        <>
                                                                            {
                                                                                <TableCell
                                                                                    width="300px"
                                                                                    key={header.field + "_" + colIndex}
                                                                                >
                                                                                    <>
                                                                                        <label hidden={rowIndex !== 0}>
                                                                                            {`${t(
                                                                                                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:RecordingTool"
                                                                                            )} ${colIndex + 1}`}
                                                                                        </label>
                                                                                        {header.field ===
                                                                                            "linkOrScreenshot" && (
                                                                                                <>
                                                                                                    <AddScreenshotControls colIndex={colIndex} />
                                                                                                    <SetScreenshotError colIndex={colIndex} />
                                                                                                </>
                                                                                            )}
                                                                                        {header.field ===
                                                                                            "lastUpdatedDate" && (
                                                                                                <DatePicker
                                                                                                    id={`${header?.field}_${rowIndex}_${colIndex}`}
                                                                                                    value={
                                                                                                        response?.recordingTools[
                                                                                                        colIndex
                                                                                                        ]?.[header.field]
                                                                                                    }
                                                                                                    onChange={(
                                                                                                        date: Date | null | null,
                                                                                                        value?: string | null
                                                                                                    ) => {
                                                                                                        onChangeInArrayWithIndexForProperty(
                                                                                                            "recordingTools",
                                                                                                            "lastUpdatedDate",
                                                                                                            value as string,
                                                                                                            colIndex
                                                                                                        );
                                                                                                    }}
                                                                                                    error={
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ] &&
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ]
                                                                                                    }
                                                                                                    helperText={
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ] &&
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ]
                                                                                                    }
                                                                                                    placeholder={t("Common.YearMonthDate")}
                                                                                                />
                                                                                            )}
                                                                                        {header.field !==
                                                                                            "linkOrScreenshot" &&
                                                                                            header.field !==
                                                                                            "lastUpdatedDate" && (
                                                                                                <TextBox
                                                                                                    id={`${header?.field}_${rowIndex}_${colIndex}`}
                                                                                                    name={header.field}
                                                                                                    multiline
                                                                                                    rows={2}
                                                                                                    variant="outlined"
                                                                                                    fullWidth
                                                                                                    hidden={
                                                                                                        rowIndex === 0 ||
                                                                                                        rowIndex ===
                                                                                                        headerRows.length - 1
                                                                                                    }
                                                                                                    placeholder={
                                                                                                        colIndex === 0
                                                                                                            ? header.placeholder
                                                                                                            : ""
                                                                                                    }
                                                                                                    value={
                                                                                                        response?.recordingTools[
                                                                                                        colIndex
                                                                                                        ]?.[header.field]
                                                                                                    }
                                                                                                    onChange={(
                                                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                                                    ) =>
                                                                                                        onChangeOfArrayWithIndex(
                                                                                                            e,
                                                                                                            "recordingTools",
                                                                                                            colIndex
                                                                                                        )
                                                                                                    }
                                                                                                    error={
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ] &&
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ]
                                                                                                    }
                                                                                                    helperText={
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ] &&
                                                                                                        errors[
                                                                                                        `recordingTools[${colIndex}].${header.field}`
                                                                                                        ]
                                                                                                    }
                                                                                                />
                                                                                            )}
                                                                                        <AddDeleteIcon
                                                                                            rowIndex={rowIndex}
                                                                                            colIndex={colIndex}
                                                                                        />
                                                                                    </>
                                                                                </TableCell>
                                                                            }
                                                                        </>
                                                                    )
                                                                )}
                                                            </>
                                                        </TableRow>
                                                    </>
                                                );
                                            })}

                                            <TableRow>
                                                <>
                                                    <TableCell colSpan={2}>
                                                        <span>
                                                            {t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_1:TotalNumberOfRecordingTools")}
                                                        </span>
                                                    </TableCell>
                                                    <TableCell colSpan={8}>
                                                        <span>{response?.recordingTools?.length}</span>
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        </>
                                    </TableBody>
                                </>
                            </Table>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_2_1_Response;