﻿import * as React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableRow from "../../../responses/TableRow";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import parse from "html-react-parser";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { Response_2 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_2";
import ValidationRules from "../../../mda_strategies/objective_2/indicator_2_1_4/ValidationRules";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";
import { ChangeEvent, useEffect, useRef } from "react";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 2.1.4 */
const Indicator_2_1_4_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_2_Indicator_2_1_4_Title");

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        onChangeWithKey,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_2>(Response_2.init(), validate);

    //Contains all the excluded properties in the array which are not needed
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason"
    ];

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const headers = [
        {
            field: "",
            label: ""
        },
        {
            field: "WHODefinition",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHODefinition"),
        },
        {
            field: "countryDefinition",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CountryDefinition"),
        },
        {
            field: "definitionOK",
            label: parse(t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:DefinitionOK"
            )),
        },
    ];

    const columns: any = {
        definition1: {
            field: "definition1",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDADefination"),
            labelTwo: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDADefinition1"),
        },
        definition2: {
            field: "definition2",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHORecommendation"),
            labelTwo: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDADefinition2"),
        },
        adverseEvent: {
            field: "adverseEvent",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDAAdverseEvent"),
            labelTwo: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDADefinition3"),
        },
        adverseDrugReaction: {
            field: "adverseDrugReaction",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDAAdverseDrugReaction"),
            labelTwo: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDADefinition4"),
        },
        seriousAdverseEvent: {
            field: "seriousAdverseEvent",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDASeriousAdverseEvent"),
            labelTwo: parse(t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:MDADefinition5")),
        },
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="response-content">
                        <div className="row">
                            <div className="col-xs-12 col-md-12">
                                <p>
                                    {t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:IndicatorResponseDesc")}
                                </p>
                                <p>
                                    <a href="https://www.who.int/publications/i/item/WHO-HTM-GMP-2016.6" target="_blank">
                                        {t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:SMCResponseDescLink")}
                                    </a>
                                </p>
                                {!!Object.keys(errors).length && (
                                    <span className="Mui-error d-flex mb-2">
                                        * {t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseOtherError")}
                                    </span>
                                )}

                                <Table>
                                    <>
                                        <TableHeader
                                            headers={headers.map((header: any) => header.label)}
                                        />
                                        <TableBody>
                                            <>
                                                {Object.keys(response).filter(
                                                    (key: string) => !excludedProperties.includes(key)
                                                ).map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{columns[modelKeyName]?.labelOne}</>
                                                            </TableCell>
                                                            <TableCell>
                                                                <>{columns[modelKeyName]?.labelTwo}</>
                                                            </TableCell>
                                                            <TableCell>
                                                                <TextBox
                                                                    id={`${columns.field}_${index}}`}
                                                                    name="country"
                                                                    fullWidth
                                                                    multiline
                                                                    rows={3}
                                                                    value={response[modelKeyName]?.country}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                    error={errors[`${modelKeyName}.country`] && errors[`${modelKeyName}.country`]}
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <RadioButtonGroup
                                                                    id="isOk"
                                                                    name="isOk"
                                                                    color="primary"
                                                                    options={[
                                                                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                                    ]}
                                                                    value={response[modelKeyName]?.isOk}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                    error={errors[`${modelKeyName}.isOk`] && errors[`${modelKeyName}.isOk`]}
                                                                />
                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}
                                            </>
                                        </TableBody>
                                    </>
                                </Table>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_2_1_4_Response;