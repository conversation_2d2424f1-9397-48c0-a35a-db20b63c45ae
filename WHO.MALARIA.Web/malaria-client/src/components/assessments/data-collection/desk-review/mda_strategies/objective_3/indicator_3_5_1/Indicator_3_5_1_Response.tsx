﻿import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";

/** Renders the response indicator 3.5.1 */
function Indicator_3_5_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_5_1_Title");

    const headers = [
        {
            field: "",
            label: ""
        },
        {
            field: "dataCleaning",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataCleaning")
        },
        {
            field: "dataReviewMeetings",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataReviewMeetings")
        },
        {
            field: "dataQualityAssessments",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualityAssessments")
        },
        {
            field: "monitoringDataQuality",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MonitoringDataQuality")
        }
    ];

    const columns = [
        {
            field: "frequencyValidation",
            label: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:FrequencyValidation"),
            placeholderFirst: t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:egMonthly")
        },
    ];

    return (
        <>
            <div className="response-wrapper">
                <p>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:MDAResponseDesc")}</p>

                <div>
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    <TableRow>
                                        <>
                                            <TableCell>
                                                <>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_5_1:DataQualityAssurance")}</>
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="surveillancestrategies"
                                                    name="surveillancestrategies"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                    ]}
                                                    value={true}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="surveillancestrategies"
                                                    name="surveillancestrategies"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                    ]}
                                                    value={true}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="surveillancestrategies"
                                                    name="surveillancestrategies"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                    ]}
                                                    value={true}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <RadioButtonGroup
                                                    id="surveillancestrategies"
                                                    name="surveillancestrategies"
                                                    row
                                                    color="primary"
                                                    options={[
                                                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                    ]}
                                                    value={true}
                                                />
                                            </TableCell>
                                        </>
                                    </TableRow>

                                    {columns.map((column: any, index: number) => (
                                        <TableRow key={`row_${index}`}>
                                            <>
                                                <TableCell>
                                                    <>{column.label}</>
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id="reason"
                                                        name="reason"
                                                        variant="outlined"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        placeholder={column.placeholderFirst}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id="reason"
                                                        name="reason"
                                                        variant="outlined"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                        placeholder={column.placeholderFirst}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id="reason"
                                                        name="reason"
                                                        variant="outlined"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id="reason"
                                                        name="reason"
                                                        variant="outlined"
                                                        multiline
                                                        rows={3}
                                                        fullWidth
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                                </>
                            </TableBody>
                        </>
                    </Table>
                </div>
            </div>

            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    <Button className={classNames("btn", "app-btn-secondary")}>
                        {t("indicators-responses:Common:Save")}
                    </Button>

                    <Button className={classNames("btn", "app-btn-primary")}>
                        {t("indicators-responses:Common:Finalize")}
                    </Button>
                </div>
            </div>
        </>
    );
}

export default Indicator_3_5_1_Response;