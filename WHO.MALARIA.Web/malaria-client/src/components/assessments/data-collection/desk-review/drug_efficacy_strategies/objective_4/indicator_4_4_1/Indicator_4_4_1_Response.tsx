﻿import { <PERSON><PERSON>, Icon<PERSON>utton } from "@mui/material";
import React, { ChangeEvent, useEffect, useRef } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableFooter from "../../../responses/TableFooter";
import Checkbox from "../../../../../../controls/Checkbox";
import InfoIcon from "@mui/icons-material/Info";
import Tooltip from "../../../../../../controls/Tooltip";
import ValidationRules from "./ValidationRules";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { Response_3 } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_4_1/Response_3";
import useCalculation from "../../../responses/useCalculation";
import { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import DatePicker from '../../../../../../controls/DatePicker';
// import { Date | null } from "@material-ui/pickers/typings/date";
import { TrainingDateDetails } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_4_1/Response_1";


/** Renders the indicator 4.4.1 response for desk review */
const Indicator_4_4_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_4_Indicator_4_4_1_Title");
    const { calculatePercentageOfYesNo } = useCalculation();

    const headers = [
        {
            field: "training",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:Training"
            ),
        },
        {
            field: "nationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:NationalLevel"
            ),
        },
        {
            field: "subnationalLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:SubnationalLevel"
            ),
        },
        {
            field: "serviceDeliveryLevel",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ServiceDeliveryLevel"
            ),
        },
    ];

    const textboxRowLabel: any = {
        "attendant": {
            field: "attendant",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:Attendants"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ProgramStaffPlaceholder"
            ),
        },
        "trainingFrequency": {
            field: "trainingFrequency",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:FrequencyOfTraining"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:AnnuallyPlaceholder"
            ),
        }
    };

    const rowDataDatePicker: any = {
        lastDateOfTraining: {
            field: "lastDateOfTraining",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:DateOfLastTraining"
            ),
        },
    };

    const radioButtonsRowLabel: any = {
        "dataCollection": {
            field: "dataCollection",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:DataCollectionHeading"
            ),
        },
        "dataReporting": {
            field: "dataReporting",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:DataReportingHeading"
            ),
        },
        "dataQualityReview": {
            field: "conductingDataQualityReview",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ConductingDataQualityReviewHeading"
            ),
        },
        "dataAnalysis": {
            field: "conductingDataAnalysis",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ConductingDataAnalysisHeading"
            ),
        },
        "disseminationReport": {
            field: "preparingDisseminationReports",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:PreparingDisseminationReports"
            ),
        },
        "supervision": {
            field: "supervision",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:Supervision"
            ),
        },
        "publicPrivateSectorTraining": {
            field: "trainingInPublicPrivateSectors",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:TrainingInPublicPrivateSectors"
            ),
        },
    };

    //Holds the validation rules and it gets changed when cannot be assessed is set to true.
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        onChangeWithKey,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        setTrueFlagOnFinalizeButtonClick,
        onValueChange,
        convertToUTC
    } = useIndicatorResponseCapture<Response_3>(Response_3.init(), validate);

    //Contains all the excluded properties for the rows with radio buttons which are not needed
    const excludedRadioButtonProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "hasTraining",
        "attendant",
        "trainingFrequency",
        "lastDateOfTraining",
    ];

    //Contains all the included properties for the textbox controls which are needed
    const includedTextboxProperties: Array<string> = [
        "attendant",
        "trainingFrequency"
    ];

    //Contains all the included date picker properties in the array
    const includedDatePickerProperties: Array<string> = [
        "lastDateOfTraining"
    ];

    //On click of date picker value updated in field
    const onDatePickerValueChange = (object: string, field: string, value: any) => {
        const lastDateOfProperty: TrainingDateDetails = response[object];
        const responseDateUpdate = {
            ...lastDateOfProperty,
            [field]: convertToUTC(new Date(value))
        };

        onValueChange(object, responseDateUpdate);
    }

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    //Method calculates the surveillance task percentage that have checked "Yes"
    const calculateSurveillanceTaskYesNoPercentage = (propertyName: string) => {
        const propertyArray: boolean[] = Object.keys(response)
            .filter((key: string) => !excludedRadioButtonProperties.includes(key))
            .map((modelKeyName: string) => response[modelKeyName][propertyName]);

        return calculatePercentageOfYesNo(propertyArray);
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="row mb-3">
                        <div className="col-xs-12 col-md-12">
                            <label>
                                {t(
                                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:TrainingCarriedOut"
                                )}
                            </label>
                            <RadioButtonGroup
                                id="hasTraining"
                                name="hasTraining"
                                row
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={response?.hasTraining}
                                onChange={onChange}
                            />
                        </div>
                    </div>

                    {response?.hasTraining && (
                        <>
                            <p>
                                {t(
                                    "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:DrugEfficacyResponseDesc"
                                )}
                            </p>
                            {
                                //Show error message if user does not select 'Yes' or 'No' for all national, sub-national and service delivery
                                !!Object.keys(errors).length &&
                                <span className="Mui-error d-flex mb-2">
                                    * {t("indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ResponseError")}
                                </span>
                            }
                            <Table>
                                <>
                                    <TableHeader
                                        headers={headers.map((header: any) => header.label)}
                                    />
                                    <TableBody>
                                        <>
                                            {Object.keys(response)
                                                .filter(
                                                    (key: string) =>
                                                        !excludedRadioButtonProperties.includes(key)
                                                )
                                                .map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{radioButtonsRowLabel[modelKeyName]?.label}</>
                                                            </TableCell>
                                                            <TableCell>
                                                                <RadioButtonGroup
                                                                    id="nationalLevel"
                                                                    name="nationalLevel"
                                                                    color="primary"
                                                                    options={[
                                                                        new MultiSelectModel(
                                                                            true,
                                                                            t("indicators-responses:Common:Yes")
                                                                        ),
                                                                        new MultiSelectModel(
                                                                            false,
                                                                            t("indicators-responses:Common:No")
                                                                        ),
                                                                    ]}
                                                                    value={response[modelKeyName]?.nationalLevel}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => {
                                                                        onChangeWithKey(e, modelKeyName)
                                                                    }}
                                                                    error={errors[`${modelKeyName}.nationalLevel`] && errors[`${modelKeyName}.nationalLevel`]}
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <RadioButtonGroup
                                                                    id="subNationalLevel"
                                                                    name="subNationalLevel"
                                                                    color="primary"
                                                                    options={[
                                                                        new MultiSelectModel(
                                                                            true,
                                                                            t("indicators-responses:Common:Yes")
                                                                        ),
                                                                        new MultiSelectModel(
                                                                            false,
                                                                            t("indicators-responses:Common:No")
                                                                        ),
                                                                    ]}
                                                                    value={response[modelKeyName]?.subNationalLevel}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                    error={errors[`${modelKeyName}.subNationalLevel`] && errors[`${modelKeyName}.subNationalLevel`]}
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <RadioButtonGroup
                                                                    id="serviceDeliveryLevel"
                                                                    name="serviceDeliveryLevel"
                                                                    color="primary"
                                                                    options={[
                                                                        new MultiSelectModel(
                                                                            true,
                                                                            t("indicators-responses:Common:Yes")
                                                                        ),
                                                                        new MultiSelectModel(
                                                                            false,
                                                                            t("indicators-responses:Common:No")
                                                                        ),
                                                                    ]}
                                                                    value={
                                                                        response[modelKeyName]?.serviceDeliveryLevel
                                                                    }
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                    error={errors[`${modelKeyName}.serviceDeliveryLevel`] && errors[`${modelKeyName}.serviceDeliveryLevel`]}
                                                                />
                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}

                                            {Object.keys(response)
                                                .filter(
                                                    (key: string) =>
                                                        includedTextboxProperties.includes(key)
                                                )
                                                .map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{textboxRowLabel[modelKeyName]?.label}</>
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    id={`${textboxRowLabel[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="nationalLevel"
                                                                    placeholder={
                                                                        textboxRowLabel[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    fullWidth
                                                                    rows={1}
                                                                    variant="outlined"
                                                                    multiline
                                                                    value={response[modelKeyName]?.nationalLevel}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                />
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    id={`${textboxRowLabel[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="subNationalLevel"
                                                                    placeholder={
                                                                        textboxRowLabel[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    fullWidth
                                                                    rows={1}
                                                                    variant="outlined"
                                                                    multiline
                                                                    value={response[modelKeyName]?.subNationalLevel}
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                />
                                                            </TableCell>

                                                            <TableCell>
                                                                <TextBox
                                                                    id={`${textboxRowLabel[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="serviceDeliveryLevel"
                                                                    placeholder={
                                                                        textboxRowLabel[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    fullWidth
                                                                    rows={1}
                                                                    variant="outlined"
                                                                    multiline
                                                                    value={
                                                                        response[modelKeyName]?.serviceDeliveryLevel
                                                                    }
                                                                    onChange={(
                                                                        e: React.ChangeEvent<HTMLInputElement>
                                                                    ) => onChangeWithKey(e, modelKeyName)}
                                                                />
                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}

                                            {Object.keys(response)
                                                .filter(
                                                    (key: string) =>
                                                        includedDatePickerProperties.includes(key)
                                                )
                                                .map((modelKeyName: string, index: number) => (
                                                    <TableRow key={`row_${modelKeyName}_${index}`}>
                                                        <>
                                                            <TableCell>
                                                                <>{rowDataDatePicker[modelKeyName]?.label}</>
                                                            </TableCell>
                                                            <TableCell>
                                                                <DatePicker
                                                                    id={`${rowDataDatePicker[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="national"
                                                                    placeholder={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    value={response[modelKeyName]?.national}
                                                                    onChange={(
                                                                        date: Date | null | null,
                                                                        value?: string | null
                                                                    ) => {
                                                                        onDatePickerValueChange(
                                                                            modelKeyName,
                                                                            "national",
                                                                            value as string
                                                                        );
                                                                    }}
                                                                    error={
                                                                        errors[`${modelKeyName}.national`] &&
                                                                        errors[`${modelKeyName}.national`]
                                                                    }
                                                                    helperText={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    onKeyDown={(e) => {
                                                                        e.preventDefault();
                                                                    }}
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <DatePicker
                                                                    id={`${rowDataDatePicker[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="subNational"
                                                                    placeholder={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    value={response[modelKeyName]?.subNational}
                                                                    onChange={(
                                                                        date: Date | null | null,
                                                                        value?: string | null
                                                                    ) => {
                                                                        onDatePickerValueChange(
                                                                            modelKeyName,
                                                                            "subNational",
                                                                            value as string
                                                                        );
                                                                    }}
                                                                    error={
                                                                        errors[`${modelKeyName}.subNational`] &&
                                                                        errors[`${modelKeyName}.subNational`]
                                                                    }
                                                                    helperText={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    onKeyDown={(e) => {
                                                                        e.preventDefault();
                                                                    }}
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <DatePicker
                                                                    id={`${rowDataDatePicker[modelKeyName]?.field
                                                                        }_${index}_${Math.random()}`}
                                                                    name="serviceDeliveryLevel"
                                                                    placeholder={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    value={response[modelKeyName]?.serviceDeliveryLevel}
                                                                    onChange={(
                                                                        date: Date | null | null,
                                                                        value?: string | null
                                                                    ) => {
                                                                        onDatePickerValueChange(
                                                                            modelKeyName,
                                                                            "serviceDeliveryLevel",
                                                                            value as string
                                                                        );
                                                                    }}
                                                                    error={
                                                                        errors[`${modelKeyName}.serviceDeliveryLevel`] &&
                                                                        errors[`${modelKeyName}.serviceDeliveryLevel`]
                                                                    }
                                                                    helperText={
                                                                        rowDataDatePicker[modelKeyName]
                                                                            ?.placeholderFirstColumn
                                                                    }
                                                                    onKeyDown={(e) => {
                                                                        e.preventDefault();
                                                                    }}
                                                                />
                                                            </TableCell>
                                                        </>
                                                    </TableRow>
                                                ))}
                                        </>
                                    </TableBody>

                                    <TableFooter>
                                        <>
                                            <TableCell>
                                                <span>
                                                    {t(
                                                        "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ProportionOfRelevantStaff"
                                                    )}

                                                    <IconButton className="grid-icon-button">
                                                        <Tooltip
                                                            content={t(
                                                                "indicators-responses:DRObjective_4_Responses:Indicator_4_4_1:ProportionOfRelevantStaffTooltip"
                                                            )}
                                                            isHtml
                                                        >
                                                            <InfoIcon fontSize="small" />
                                                        </Tooltip>
                                                    </IconButton>
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <label>{calculateSurveillanceTaskYesNoPercentage("nationalLevel")}%</label>
                                            </TableCell>
                                            <TableCell>
                                                <label>{calculateSurveillanceTaskYesNoPercentage("subNationalLevel")}%</label>
                                            </TableCell>
                                            <TableCell>
                                                <label>{calculateSurveillanceTaskYesNoPercentage("serviceDeliveryLevel")}%</label>
                                            </TableCell>
                                        </>
                                    </TableFooter>
                                </>
                            </Table>
                        </>
                    )}
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_4_4_1_Response;