import React, { useEffect, ChangeEvent, useRef } from "react";
import parse from "html-react-parser";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_7/Response_1";
import useFormValidation from "../../../../../../common/useFormValidation";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { useSelector } from "react-redux";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import Checkbox from "../../../../../../controls/Checkbox";
import ValidationRules from "./ValidationRules";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 1.3.7 */
function Indicator_1_3_7_Response() {
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t("app.DR_Objective_1_Indicator_1_3_7_Title");

  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
  const validate = useFormValidation(validationRulesRef.current);
  const errors = useSelector((state: any) => state.error);

  const {
    response,
    onCannotBeAssessed,
    onChange,
    getResponse,
    onSave,
    onFinalize,
    setTrueFlagOnFinalizeButtonClick
  } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid) {
      onFinalize();
    }
  };

  useEffect(() => {
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

  }, [response?.cannotBeAssessed]);

  //Triggers onChange of cannotBeAssessed checkbox 
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current =
      evt.currentTarget.checked
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;

    onCannotBeAssessed(evt);
  }

  return (
    <>
      <div className="response-assess-wrapper">
        <Checkbox
          id="cannotBeAssessed"
          name="cannotBeAssessed"
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response?.cannotBeAssessed}
        />
      </div>
      {!response?.cannotBeAssessed ? (
        <div className="response-wrapper">
          <p>
            {parse(t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_7:DrugEfficacyResponseDesc"
            ))}
          </p>
          <div className="mt-5">
            <TextBox
              id="challengesOfUsingMalariaSurveillanceData"
              name="challengesOfUsingMalariaSurveillanceData"
              label={t("indicators-responses:Common:ProvideDetails")}
              rows={5}
              multiline
              fullWidth
              value={response?.challengesOfUsingMalariaSurveillanceData || ''}
              onChange={onChange}
              error={
                errors["challengesOfUsingMalariaSurveillanceData"] &&
                errors["challengesOfUsingMalariaSurveillanceData"]
              }
              helperText={
                errors["challengesOfUsingMalariaSurveillanceData"] &&
                errors["challengesOfUsingMalariaSurveillanceData"]
              }
            />
          </div>
        </div>
      ) :
        <div className="response-wrapper d-flex">
          <TextBox
            id="cannotBeAssessedReason"
            name="cannotBeAssessedReason"
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant="outlined"
            fullWidth
            value={response?.cannotBeAssessedReason}
            onChange={onChange}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      }
      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
    </>
  );
}

export default Indicator_1_3_7_Response;