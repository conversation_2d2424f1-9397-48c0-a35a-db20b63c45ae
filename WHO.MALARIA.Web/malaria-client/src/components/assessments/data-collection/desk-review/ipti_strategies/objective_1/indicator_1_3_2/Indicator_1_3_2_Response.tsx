import { <PERSON><PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { ChangeEvent, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import parse from "html-react-parser";
import { Response_2 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_2/Response_2";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { useSelector } from "react-redux";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import useCalculation from "../../../responses/useCalculation";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 1.3.2 */
function Indicator_1_3_2_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_3_2_Title");

    /** Excluded property that are not used in Array Map */
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason"
    ];

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithKey,
        getResponse,
        onSave,
        onFinalize,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_2>(Response_2.init(), validate);

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize buttons, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const headers = [
        {
            field: "WHODefinition",
            label: t(
                "indicators-responses:Common:DataUse"
            ),
        },
        {
            field: "countryDefinition",
            label: t(
                "indicators-responses:Common:Evidence"
            ),
        },
        {
            field: "definitionOK",
            label: t(
                "indicators-responses:Common:DocumentsData"
            ),
        },
        {
            field: "definitionOK",
            label: t(
                "indicators-responses:Common:Links"
            ),
        },
    ];

    const rowsData: any = {
        improvementsInFeedbackAndSupervision: {
            label: t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ITPIImprovementInFeedback"
            ),
        },
        initiateSurveillanceTrainingAndDataAnalysis: {
            label: t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:IPTIInitiateSurveillanceTraining"
            )
        }
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p className="fw-lighter">
                        {parse(t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:IPTIResponseDesc"
                        ))}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ITPIOneShouldBeTicked"
                        )}
                    </p>
                    <p className="fw-lighter">
                        {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:DocumentsDetailsDesc")}
                    </p>
                    {
                        //Show error message if not all radio buttons are selected for Data quality control check in place
                        errors["improvementsInFeedbackAndSupervision.evidence"] && (
                            <span className="Mui-error d-flex mb-2">
                                *
                                {t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ResponseError"
                                )}
                            </span>
                        )
                    }
                    <div className="mt-3">
                        <Table className="app-table">
                            <>
                                <TableHeader
                                    headers={headers.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {Object.keys(response)
                                            .filter((key: string) => !excludedProperties.includes(key))
                                            .map((modelKeyName: string, index: number) => (
                                                <TableRow key={`row_${modelKeyName}_${index}`}>
                                                    <>
                                                        <TableCell>{rowsData[modelKeyName].label}</TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="evidence"
                                                                name="evidence"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes")
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No")
                                                                    ),
                                                                ]}
                                                                value={response[modelKeyName]?.evidence}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onChangeWithKey(e, modelKeyName)}
                                                                error={
                                                                    errors[`${modelKeyName}.evidence`] &&
                                                                    errors[`${modelKeyName}.evidence`]
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id="details"
                                                                name="details"
                                                                placeholder={t(
                                                                    "indicators-responses:Common:DetailsForBothResponses"
                                                                )}
                                                                fullWidth
                                                                multiline
                                                                rows={3}
                                                                value={response[modelKeyName]?.details || ""}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onChangeWithKey(e, modelKeyName)}
                                                                error={
                                                                    errors[`${modelKeyName}.details`] &&
                                                                    errors[`${modelKeyName}.details`]
                                                                }
                                                                helperText={
                                                                    errors[`${modelKeyName}.details`] &&
                                                                    errors[`${modelKeyName}.details`]
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id="links"
                                                                name="links"
                                                                placeholder={t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:LinksPlaceholder")}
                                                                fullWidth
                                                                multiline
                                                                rows={3}
                                                                value={response[modelKeyName]?.links || ""}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onChangeWithKey(e, modelKeyName)}
                                                                error={
                                                                    errors[`${modelKeyName}.links`] && errors[`${modelKeyName}.links`]
                                                                }
                                                                helperText={
                                                                    errors[`${modelKeyName}.links`] && errors[`${modelKeyName}.links`]
                                                                }
                                                            />
                                                        </TableCell>
                                                    </>
                                                </TableRow>
                                            ))}
                                    </>
                                </TableBody>
                            </>
                        </Table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                        helperText={
                            errors[`cannotBeAssessedReason`] &&
                            errors[`cannotBeAssessedReason`]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_1_3_2_Response;
