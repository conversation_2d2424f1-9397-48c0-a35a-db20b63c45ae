﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import { ChangeEvent, useEffect, useRef } from "react";
import { Button } from "@mui/material";
import classNames from "classnames";
import Checkbox from "../../../../../../controls/Checkbox";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { Response_3 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_3";
import ValidationRules from "../../../ipti_strategies/objective_2/indicator_2_1_4/ValidationRules";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import parse from "html-react-parser";

/** Renders the response for indicator 2.1.4 */
const Indicator_2_1_4_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_2_Indicator_2_1_4_Title");

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        onChangeWithKey,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_3>(Response_3.init(), validate);

    //Contains all the excluded properties in the array which are not needed
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason"
    ];

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;
        onCannotBeAssessed(evt);
    }

    const headers = [
        { field: "", label: "" },
        {
            field: "WHODefinition",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHODefinition"
            ),
        },
        {
            field: "countryDefinition",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CountryDefinition"
            ),
        },
        {
            field: "definitionOK",
            label: parse(t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:DefinitionOK"
            )),
        },
    ];

    const columns: any = {
        definition1: {
            field: "definition1",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:IPTiLableOne"),
            labelTwo: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:IptiNote"),
        },
        definition2: {
            field: "definition2",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHORecommendation"),
            labelTwo: parse(t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHOEncouragement")),
        },
        definition3: {
            field: "definition3",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ContaIndications"),
            labelTwo: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHOInfants"),
        },
        definition4: {
            field: "definition4",
            labelOne: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:IPTiLableFour"),
            labelTwo: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:IPTiAgainstSMC"),
        },
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:IndicatorResponseDesc"
                        )}
                    </p>
                    <p>
                        <a href="https://www.who.int/publications/i/item/WHO-HTM-GMP-2016.6" target="_blank">
                            {t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseLink"
                            )}
                        </a>
                    </p>

                    {!!Object.keys(errors).length && (
                        <span className="Mui-error d-flex mb-2">
                            * {t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseOtherError")}
                        </span>
                    )}
                    <div>
                        <Table>
                            <>
                                <TableHeader
                                    headers={headers.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {Object.keys(response).filter(
                                            (key: string) => !excludedProperties.includes(key)
                                        ).map((modelKeyName: string, index: number) => (
                                            <TableRow key={`row_${modelKeyName}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{columns[modelKeyName]?.labelOne}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <>{columns[modelKeyName]?.labelTwo}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}}`}
                                                            name="country"
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={response[modelKeyName]?.country}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={errors[`${modelKeyName}.country`] && errors[`${modelKeyName}.country`]}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="isOk"
                                                            name="isOk"
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                                new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                            ]}
                                                            value={response[modelKeyName]?.isOk}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={errors[`${modelKeyName}.isOk`] && errors[`${modelKeyName}.isOk`]}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                    </>
                                </TableBody>
                            </>
                        </Table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_2_1_4_Response;
