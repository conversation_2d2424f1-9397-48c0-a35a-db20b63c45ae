﻿import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import React, { ChangeEvent, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import Checkbox from "../../../../../../controls/Checkbox";
import { But<PERSON>, IconButton } from "@mui/material";
import classNames from "classnames";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import TableFooter from "../../../responses/TableFooter";
import { AccessUser, Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_6_1/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 3.6.1 */
function Indicator_3_6_1_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_6_1_Title");
    const { calculateNullPercentageInArray } = useCalculation();

    const headers = [
        {
            field: "dataAccessDetails",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:DataAccessDetails"
            ),
        },
        {
            field: "national",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:National"
            ),
        },
        {
            field: "subnational",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:Subnational"
            ),
        },
        {
            field: "serviceDelivery",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:ServiceDelivery"
            ),
        },
    ];

    const columns = [
        {
            field: "rolesOfIndividuals",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:RolesOfIndividuals"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:RolesOfIndividualsFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:RolesOfIndividualsSecondPlaceholder"
            ),
            placeholderThirdColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:RolesOfIndividualsThirdPlaceholder"
            ),
        },
        {
            field: "methodOfAccess",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:MethodOfAccess"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:MethodOfAccessFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:MethodOfAccessSecondPlaceholder"
            ),
            placeholderThirdColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:MethodOfAccessThirdPlaceholder"
            ),
        },
        {
            field: "whatCanBeAccessed",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:WhatCanBeAccessed"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:WhatCanBeAccessedFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:WhatCanBeAccessedSecondPlaceholder"
            ),
            placeholderThirdColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:WhatCanBeAccessedThirdPlaceholder"
            ),
        },
        {
            field: "canDataBeAccessed",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:CanDataBeAccessed"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:CanDataBeAccessedFirstPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:CanDataBeAccessedSecondPlaceholder"
            ),
            placeholderThirdColumn: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:CanDataBeAccessedThirdPlaceholder"
            ),
        },
    ];

    const accessUsers = columns.map(
        (column: any) => new AccessUser(column.field, null, null, null)
    );

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onCannotBeAssessed,
        onChange,
        onChangeWithIndex,
        onSave,
        onFinalize,
        getResponse,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_1>(
        new Response_1(false, null, null, accessUsers),
        validate
    );

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    //Returns the percentage of the columns which doesnt have any null value
    const calculateDataFilledPercentage = () => {
        const nationalArray: Array<string> = Array();
        const subNationalArray: Array<string> = Array();
        const serviceDeliveryArray: Array<string> = Array();

        for (const key in response?.accessUsers) {
            nationalArray.push(response?.accessUsers[key].national);
            subNationalArray.push(response?.accessUsers[key].subNational);
            serviceDeliveryArray.push(response?.accessUsers[key].serviceDelivery);
        }

        return calculateNullPercentageInArray([
            nationalArray,
            subNationalArray,
            serviceDeliveryArray,
        ]);
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:ITNResponseDesc"
                        )}
                    </p>
                    <p className="fst-italic">
                        {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:NoteToCompleteAssessment"
                        )}
                    </p>

                    <div>
                        {/*Show error message if not all National level data access details are filled*/}
                        {!!Object.keys(errors).length && (
                            <span className="Mui-error d-flex mb-2">
                                *{" "}
                                {t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:ResponseError"
                                )}
                            </span>
                        )}
                        <Table>
                            <>
                                <TableHeader
                                    headers={headers.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {columns.map((column: any, index: number) => (
                                            <TableRow key={`column_${column.field}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{column.label}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${column.field}_${index}_${Math.random()}`}
                                                            name="national"
                                                            placeholder={column.placeholderFirstColumn}
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={response?.accessUsers[index]?.national}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onChangeWithIndex(e, "accessUsers", index)
                                                            }}
                                                            error={
                                                                errors[`accessUsers[${index}].national`] &&
                                                                errors[`accessUsers[${index}].national`]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${column.field}_${index}_${Math.random()}`}
                                                            name="subNational"
                                                            placeholder={column.placeholderSecondColumn}
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={response?.accessUsers[index]?.subNational}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onChangeWithIndex(e, "accessUsers", index)
                                                            }}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${column.field}_${index}_${Math.random()}`}
                                                            name="serviceDelivery"
                                                            placeholder={column.placeholderThirdColumn}
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={
                                                                response?.accessUsers[index]?.serviceDelivery
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onChangeWithIndex(e, "accessUsers", index)
                                                            }}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                    </>
                                </TableBody>
                                <TableFooter>
                                    <>
                                        <TableCell>
                                            <>
                                                <span>
                                                    {t(
                                                        "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:ProportionOfHealthSystemLevels"
                                                    )}

                                                    <IconButton className="grid-icon-button">
                                                        <Tooltip
                                                            content={t(
                                                                "indicators-responses:DRObjective_3_Responses:Indicator_3_6_1:TableFooterProportionsTooltip"
                                                            )}
                                                            isHtml
                                                        >
                                                            <InfoIcon fontSize="small" />
                                                        </Tooltip>
                                                    </IconButton>
                                                </span>
                                            </>
                                        </TableCell>
                                        <TableCell colSpan={8}>
                                            <label>{calculateDataFilledPercentage()}%</label>
                                        </TableCell>
                                    </>
                                </TableFooter>
                            </>
                        </Table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_6_1_Response;