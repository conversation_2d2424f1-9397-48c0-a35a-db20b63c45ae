import { <PERSON><PERSON> } from "@mui/material";
import React, { ChangeEvent, useEffect, useRef } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { Response_2 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_3_1/Response_2";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 2.3.1 response for desk review */
const Indicator_2_3_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t(
        "indicators-responses:app:DR_Objective_2_Indicator_2_3_1_Title"
    );

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    // Excluded property that are not used in Array Map
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "guideline1",
    ];

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithKey,
        getResponse,
        onSave,
        onFinalize,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_2>(Response_2.init(), validate);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const headersGuidlines = [
        {
            field: "",
            label: "",
        },
        {
            field: "guideline",
            label: `${t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ITNRoutine")}
      ${t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Guideline")}`,
        }
    ];

    const headers = [
        {
            field: "healthSectors",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:HealthSectors"
            ),
        },
        {
            field: "receivedGuidelines",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ReceivedGuidelines"
            ),
        },
        {
            field: "details",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Details"
            ),
        },
    ];

    const columnsGuidelinesTextbox = [
        {
            field: "name",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:GuidelineName"
            ),
        },
        {
            field: "linkToCopy",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:LinkToCopy"
            ),
        },
    ];

    const columnsGuidelinesRadio = [
        {
            field: "internetAvailability",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:IsReliableConnection"
            ),
        },
    ];

    const columns: any = {
        "publicSector": {
            field: "publicSector",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Public"),
        },
        "privateFormal": {
            field: "privateFormal",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:PrivateFormal"),
        },
        "privateInformal": {
            field: "privateInformal",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:PrivateInformal"),
        },
        "community": {
            field: "community",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:Community"),
        },
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ITNRoutineResponseDesc")}
                    </p>
                    <p>
                        <i>
                            {t("indicators-responses:Common:GuideLineDetailInformation")}
                        </i>
                    </p>

                    <Table>
                        <>
                            <TableHeader
                                headers={headersGuidlines.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {columnsGuidelinesTextbox.map(
                                        (column: any, index: number) => (
                                            <TableRow key={`row_${column.label}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{column.label}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={column.field}
                                                            name={column.field}
                                                            fullWidth
                                                            value={response["guideline1"]?.[column.field]}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, "guideline1")}
                                                            error={
                                                                errors[`guideline1.${column.field}`] &&
                                                                errors[`guideline1.${column.field}`]
                                                            }
                                                            helperText={
                                                                errors[`guideline1.${column.field}`] &&
                                                                errors[`guideline1.${column.field}`]
                                                            }
                                                        />
                                                    </TableCell>                                                                             </>
                                            </TableRow>
                                        )
                                    )}

                                    {columnsGuidelinesRadio.map((column: any, index: number) => (
                                        <TableRow key={Math.random().toString()}>
                                            <>
                                                <TableCell>
                                                    <>{column.label}</>
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id={column.field}
                                                        name={column.field}
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={response["guideline1"]?.[column.field]}
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, "guideline1")}
                                                        error={
                                                            errors[`guideline1.${column.field}`] &&
                                                            errors[`guideline1.${column.field}`]
                                                        }
                                                        helperText={
                                                            errors[`guideline1.${column.field}`] &&
                                                            errors[`guideline1.${column.field}`]
                                                        }
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                                </>
                            </TableBody>
                        </>
                    </Table>

                    <p className="pt-3">
                        {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ResponseDesc1"
                        )}
                    </p>

                    {
                        !!Object.keys(errors).length && (
                            <span className="Mui-error d-flex mb-2">
                                * {t("indicators-responses:DRObjective_2_Responses:Indicator_2_3_1:ResponseOtherError")}
                            </span>
                        )}
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {Object.keys(response).filter(
                                        (key: string) => !excludedProperties.includes(key)
                                    ).map((modelKeyName: string, index: number) => (
                                        <TableRow key={`row_${modelKeyName}_${index}`}>
                                            <>
                                                <TableCell>
                                                    <>{columns[modelKeyName]?.label}</>
                                                </TableCell>
                                                <TableCell>
                                                    <RadioButtonGroup
                                                        id="hasGuidelineReceived"
                                                        name="hasGuidelineReceived"
                                                        color="primary"
                                                        options={[
                                                            new MultiSelectModel(
                                                                true,
                                                                t("indicators-responses:Common:Yes")
                                                            ),
                                                            new MultiSelectModel(
                                                                false,
                                                                t("indicators-responses:Common:No")
                                                            ),
                                                        ]}
                                                        value={
                                                            response[modelKeyName]?.hasGuidelineReceived
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, modelKeyName)}
                                                        error={
                                                            errors[
                                                            `${modelKeyName}.hasGuidelineReceived`
                                                            ] &&
                                                            errors[
                                                            `${modelKeyName}.hasGuidelineReceived`
                                                            ]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id="guidelineDetails"
                                                        name="guidelineDetails"
                                                        fullWidth
                                                        value={
                                                            response[modelKeyName]?.guidelineDetails
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => onChangeWithKey(e, modelKeyName)}
                                                        error={
                                                            errors[
                                                            `${modelKeyName}.guidelineDetails`
                                                            ] &&
                                                            errors[`${modelKeyName}.guidelineDetails`]
                                                        }
                                                    />
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                                </>
                            </TableBody>
                        </>
                    </Table>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_2_3_1_Response;