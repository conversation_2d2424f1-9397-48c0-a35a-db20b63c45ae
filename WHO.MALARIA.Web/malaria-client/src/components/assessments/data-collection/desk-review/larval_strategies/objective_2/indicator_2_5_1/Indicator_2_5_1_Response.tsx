﻿import { But<PERSON> } from "@mui/material";
import React, { useState } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableFooter from "../../../responses/TableFooter";
import TableRow from "../../../responses/TableRow";

/** Renders the indicator 2.5.1 response for desk review */
const Indicator_2_5_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_2_5_1_Title");

    const [indicator2_5_1, setIndicator2_5_1] = useState<{}>(); //TODO: Change with response model

    // Triggered by all HTMLInputElement whenever the values are changed
    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        setIndicator2_5_1({
            ...indicator2_5_1,
            [evt.target.name]: evt.target.value,
        });
    };

    const headers = [
        {
            field: "",
            label: "",
        },
        {
            field: "currency",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:Currency"
            ),
        },
        {
            field: "value",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:Value"
            ),
        },
    ];

    const columns = [
        {
            field: "estimatedNationalBudget",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:EstimatedNationalBudget"
            ),
        },
        {
            field: "fundsAvailableForMalariaSurveillance",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:FundsAvailableForMalariaSurveillance"
            ),
        },
        {
            field: "domesticFunds",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:DomesticFunds"
            ),
        },
    ];

    return (
        <>
            <div className="response-wrapper">
               
                <Table>
                    <>
                        <TableHeader headers={headers.map((header: any) => header.label)} />
                        <TableBody>
                            <>
                                {columns.map((column: any, index: number) => (
                                    <TableRow key={Math.random().toString()}>
                                        <>
                                            <TableCell>
                                                <>{column.label}</>
                                            </TableCell>
                                            <TableCell>
                                                <TextBox id={column.field} name={column.field} />
                                            </TableCell>
                                            <TableCell>
                                                <TextBox id={column.field} name={column.field} />
                                            </TableCell>
                                        </>
                                    </TableRow>
                                ))}
                            </>
                        </TableBody>
                        <TableFooter>
                            <>
                                <TableCell>
                                    <span>
                                        {t(
                                            "indicators-responses:DRObjective_2_Responses:Indicator_2_5_1:PercentageGap"
                                        )}
                                    </span>
                                </TableCell>
                                <TableCell>
                                    <span>40%</span>
                                </TableCell>
                                <TableCell>
                                    <></>
                                </TableCell>
                            </>
                        </TableFooter>
                    </>
                </Table>
            </div>

            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    <Button
                        className={classNames("btn", "app-btn-secondary")}
                    >
                        {t("indicators-responses:Common:Save")}
                    </Button>

                    <Button className={classNames("btn", "app-btn-primary")}>
                        {t("indicators-responses:Common:Finalize")}
                    </Button>
                </div>
            </div>
        </>
    );
};

export default Indicator_2_5_1_Response;