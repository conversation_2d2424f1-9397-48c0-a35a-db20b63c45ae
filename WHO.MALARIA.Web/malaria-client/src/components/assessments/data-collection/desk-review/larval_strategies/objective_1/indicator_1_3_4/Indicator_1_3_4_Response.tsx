﻿import React, { useRef, useEffect, ChangeEvent } from "react";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TextBox from "../../../../../../controls/TextBox";
import Checkbox from "../../../../../../controls/Checkbox";
import { TableCell, TableRow } from "@mui/material";
import useCalculation from "../../../responses/useCalculation";
import { Response_2 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_4/Response_2";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 1.3.4 response for desk review */
function Indicator_1_3_4_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_3_4_Title");
    const { calculatePercentage } = useCalculation();

    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "dataAreNotUsed"
    ];

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        getResponse,
        onSave,
        onChange,
        onFinalize,
        onCannotBeAssessed,
        onChangeWithKey,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_2>(Response_2.init(), validate);

    const errors = useSelector((state: any) => state.error);

    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };
    //  This gets called only first time when component renders
    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const healthSystemLevelLabel: any = {
        nationalLevel: t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:National"),
        regionalLevel: t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:Regional"),
        districtLevel: t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:District"),
    };

    return (
        <>
            <div className="response-assess-wrapper">
                {!response?.dataAreNotUsed &&
                    <Checkbox
                        id="cannotBeAssessed"
                        name="cannotBeAssessed"
                        label={t("indicators-responses:Common:IndicatorNoAssess")}
                        onChange={onCannotBeAssessedChange}
                        checked={response?.cannotBeAssessed}
                    />
                }
                {!response?.cannotBeAssessed && (
                    <Checkbox
                        id="dataAreNotUsed"
                        name="dataAreNotUsed"
                        label={t("indicators-responses:Common:DataAreNotUsed")}
                        onChange={onChange}
                        checked={response?.dataAreNotUsed}
                    />
                )}
            </div>
            {!(response?.cannotBeAssessed || response?.dataAreNotUsed) ? (
                <div className="response-wrapper">
                    <p>
                        {parse(t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:ResponseDescription"
                        ))}
                    </p>
                    {
                        //Show error message if data from at least one health system is not entered
                        !!Object.keys(errors).length && (
                            <span className="Mui-error d-flex mb-2">
                                * {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:ResponseError")}
                            </span>
                        )
                    }
                    <div className="mt-2">
                        <Table width="100%" className="app-table">
                            <>
                                <TableHeader
                                    headers={[
                                        t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:HealthSystemLevel"
                                        ),
                                        t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:MonthlyBulletins"
                                        ),
                                        t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:NoOfMonths"
                                        ),
                                        t(
                                            "indicators-responses:Common:Rate"
                                        ),
                                    ]}
                                />
                                <TableBody>
                                    <>
                                        {Object.keys(response)
                                            .filter((key) => !excludedProperties.includes(key))
                                            .map((modelKeyName: string, index: number) => (
                                                <TableRow key={`row_${modelKeyName}_${index}`}>
                                                    <>
                                                        <TableCell>
                                                            <>{healthSystemLevelLabel[modelKeyName]}</>
                                                        </TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id="noOfMonthlyBulletinsProduced"
                                                                name="noOfMonthlyBulletinsProduced"
                                                                type="number"
                                                                value={
                                                                    response[modelKeyName]
                                                                        ?.noOfMonthlyBulletinsProduced
                                                                }
                                                                inputProps={{
                                                                    min: 0,
                                                                    maxLength: 2,
                                                                }}
                                                                fullWidth
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onChangeWithKey(e, modelKeyName)}
                                                                error={
                                                                    errors[`${modelKeyName}.noOfMonthlyBulletinsProduced`] &&
                                                                    errors[`${modelKeyName}.noOfMonthlyBulletinsProduced`]
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id="noOfWeeksOrMonths"
                                                                name="noOfWeeksOrMonths"
                                                                type="number"
                                                                value={response[modelKeyName]?.noOfWeeksOrMonths}
                                                                inputProps={{
                                                                    min: 0,
                                                                    maxLength: 2,
                                                                }}
                                                                fullWidth
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => onChangeWithKey(e, modelKeyName)}
                                                                error={
                                                                    errors[`${modelKeyName}.noOfWeeksOrMonths`] &&
                                                                    errors[`${modelKeyName}.noOfWeeksOrMonths`]
                                                                }
                                                                disabled={modelKeyName === 'nationalLevel' ? true : false}
                                                            />
                                                        </TableCell>
                                                        <TableCell className="ps-3">
                                                            <label>
                                                                {errors[`${modelKeyName}.proportionRate`] ?
                                                                    <span className="Mui-error d-flex"> {errors[`${modelKeyName}.proportionRate`]}</span>
                                                                    : `${calculatePercentage(
                                                                        response[modelKeyName]
                                                                            ?.noOfMonthlyBulletinsProduced,
                                                                        response[modelKeyName]
                                                                            ?.noOfWeeksOrMonths
                                                                    )}%`}
                                                            </label>
                                                        </TableCell>
                                                    </>
                                                </TableRow>
                                            ))}
                                    </>
                                </TableBody>
                            </>
                        </Table>
                    </div>
                </div>
            ) :
                <>
                    {response?.cannotBeAssessed ?
                        (

                            <div className="response-wrapper d-flex">
                                <TextBox
                                    id="cannotBeAssessedReason"
                                    name="cannotBeAssessedReason"
                                    label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                                    multiline
                                    rows={10}
                                    variant="outlined"
                                    fullWidth
                                    value={response?.cannotBeAssessedReason || ""}
                                    onChange={onChange}
                                    error={
                                        errors["cannotBeAssessedReason"] &&
                                        errors["cannotBeAssessedReason"]
                                    }
                                    helperText={
                                        errors["cannotBeAssessedReason"] &&
                                        errors["cannotBeAssessedReason"]
                                    }
                                />
                            </div>
                        ) :
                        (
                            <>

                            </>
                        )
                    }
                </>
            }

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_1_3_4_Response;
