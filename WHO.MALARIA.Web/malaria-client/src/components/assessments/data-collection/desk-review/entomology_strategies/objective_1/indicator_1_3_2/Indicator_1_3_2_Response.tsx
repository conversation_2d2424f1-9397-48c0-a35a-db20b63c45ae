import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import parse from "html-react-parser";

/** Renders the response for indicator 1.3.2 */
function Indicator_1_3_2_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_3_2_Title");

    const [checked, setChecked] = useState(false);

    // Triggered whenever the chekbox are changed
    const onCheckboxClick = () => {
        setChecked(!checked);
    };

    const headers = [
        {
            field: "WHODefinition",
            label: t(
                "indicators-responses:Common:DataUse"
            ),
        },
        {
            field: "countryDefinition",
            label: t(
                "indicators-responses:Common:Evidence"
            ),
        },
        {
            field: "definitionOK",
            label: t(
                "indicators-responses:Common:DocumentsData"
            ),
        },
        {
            field: "definitionOK",
            label: t(
                "indicators-responses:Common:Links"
            ),
        },
    ];

    const rows = [
        {
            label: t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ITPIImprovementsInDataQuality"
            ),
        },
        {
            label: t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:IPTIInitiateSurveillanceTraining"
            )
        }
    ];


    return (
        <>
            <div>
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    checked={checked} // TOOD: dynamic
                    onClick={onCheckboxClick}
                    label={t(
                        "indicators-responses:Common:IndicatorNoAssess"
                    )}
                />
            </div>

            {!checked ? (
                <div className="response-wrapper">
                    <p className="fw-lighter">
                        {parse(t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:ResponseDesc"
                        ))}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t(
                            "indicators-responses:Common:NoteToCompleteTheAssessment"
                        )}
                    </p>
                    <p className="fw-lighter">
                        {t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:DocumentsDetailsDesc")}
                    </p>
                    <div className="mt-3">
                        <Table className="app-table">
                            <>
                                <TableHeader
                                    headers={headers.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {rows.map((row: any, index: number) => (
                                            <TableRow key={`row_${Math.random().toString()}_${index}`}>
                                                <>
                                                    <TableCell>{row.label}</TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="evidence"
                                                            name="evidence"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                                                                new MultiSelectModel(false, t("indicators-responses:Common:No")),
                                                            ]}
                                                            value={true}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="details"
                                                            name="details"
                                                            placeholder={t(
                                                                "indicators-responses:Common:DetailsForBothResponses"
                                                            )}
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id="links"
                                                            name="links"
                                                            placeholder={t("indicators-responses:DRObjective_1_Responses:Indicator_1_3_2:LinksPlaceholder")}
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                    </>
                                </TableBody>
                            </>
                        </Table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="indicatornoassessreasons"
                        label={t(
                            "indicators-responses:Common:IndicatorNoAssessReasons"
                        )}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                    />
                </div>
            )}

            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    <Button
                        className={classNames("btn", "app-btn-secondary")}
                    >
                        {t("indicators-responses:Common:Save")}
                    </Button>
                    <Button className={classNames("btn", "app-btn-primary")}>
                        {t("indicators-responses:Common:Finalize")}
                    </Button>
                </div>
            </div>
        </>
    );
}

export default Indicator_1_3_2_Response;
