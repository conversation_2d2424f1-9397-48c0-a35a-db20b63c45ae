﻿import { Button } from "@mui/material";
import React, { useEffect, useRef, ChangeEvent } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_9/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 1.1.9 response for desk review */
const Indicator_1_1_9_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_1_9_Title");

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onCannotBeAssessed,
        getResponse,
        onSave,
        onFinalize,
        onChange,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <div className="response-content">
                        <div className="row mb-3">
                            <div className="col-xs-12 col-md-6">
                                <div className="row">
                                    <div className="col-xs-12 col-md-12">
                                        <label>
                                            {t(
                                                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_9:EntoMolecularAnalysisSelection"
                                            )}
                                        </label>
                                        <RadioButtonGroup
                                            id="isMolecularAnalysisCarriedOutForDrugResistance"
                                            name="isMolecularAnalysisCarriedOutForDrugResistance"
                                            row
                                            color="primary"
                                            options={[
                                                new MultiSelectModel(
                                                    true,
                                                    t("indicators-responses:Common:Yes")
                                                ),
                                                new MultiSelectModel(
                                                    false,
                                                    t("indicators-responses:Common:No")
                                                ),
                                            ]}
                                            value={
                                                response?.isMolecularAnalysisCarriedOutForDrugResistance
                                            }
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                onChange(e);
                                            }}
                                            error={
                                                errors[
                                                "isMolecularAnalysisCarriedOutForDrugResistance"
                                                ] &&
                                                errors["isMolecularAnalysisCarriedOutForDrugResistance"]
                                            }
                                            helperText={
                                                errors[
                                                "isMolecularAnalysisCarriedOutForDrugResistance"
                                                ] &&
                                                errors["isMolecularAnalysisCarriedOutForDrugResistance"]
                                            }
                                        />
                                    </div>
                                </div>

                                {response?.isMolecularAnalysisCarriedOutForDrugResistance && (
                                    <div className="row">
                                        <div className="col-xs-12 col-md-12">
                                            <TextBox  
                                                id="detailsOnMethodForDrugResistance"
                                                name="detailsOnMethodForDrugResistance"
                                                label={t(
                                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_9:MutationsDetectedResults"
                                                )}
                                                className="lp-text inputfocus"
                                                multiline
                                                rows={10}
                                                variant="outlined"
                                                fullWidth
                                                value={response?.detailsOnMethodForDrugResistance}
                                                onChange={onChange}
                                                error={
                                                    errors["detailsOnMethodForDrugResistance"] &&
                                                    errors["detailsOnMethodForDrugResistance"]
                                                }
                                                helperText={
                                                    errors["detailsOnMethodForDrugResistance"] &&
                                                    errors["detailsOnMethodForDrugResistance"]
                                                }
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_1_1_9_Response;
