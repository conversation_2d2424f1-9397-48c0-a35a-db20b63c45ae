﻿import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next';
import { useNavigate,  useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { UserAssessmentPermission } from "../../../models/PermissionModel";
import FileUploader from "../../controls/FileUploader";
import classNames from "classnames";
import { Constants } from "../../../models/Constants";
import { shellTableService } from "../../../services/shellTableService"
import { RespondentTypeModel } from '../../../models/QuestionBank/RespondentTypeModel';
import { questionBankService } from '../../../services/questionBankService';
import { AssessmentStatus } from '../../../models/Enums';
import { DataAnalysisReportFile } from '../../../models/ReportModel/ReportModel';
import Table from "../data-collection/desk-review/responses/Table";
import TableHeader from "../data-collection/desk-review/responses/TableHeader";
import TableBody from "../data-collection/desk-review/responses/TableBody";
import TableRow from "../data-collection/desk-review/responses/TableRow";
import TableCell from "../data-collection/desk-review/responses/TableCell";

/** Survey result component to upload shell table */
const SurveyResult = () => {
    const { t } = useTranslation();
    document.title = t("app.SurveyResultTitle");

    const navigate = useNavigate();
    const location: any = useLocation();
    const assessmentId = location?.state?.assessmentId;
    const assessmentStatus = location?.state?.status;
    const [respondentTypes, setRespondentTypes] = useState<Array<RespondentTypeModel>>([RespondentTypeModel.init()]);
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const [isValidFileExtension, setIsValidFileExtension] = useState<boolean>(false);
    const [dataShellReportFiles, setDataShellReportFiles] = useState<Array<DataAnalysisReportFile>>([]);
    // bind respondent types
    const bindRespondentTypes = () => {
        questionBankService
            .getRespondentTypesById(assessmentId)
            .then((response: Array<RespondentTypeModel>) => {
                if (response.length > 0) {
                    setRespondentTypes(response);
                }
            });
    };

    // Triggers whenever user clicks on upload button
    const uploadFile = (evt: React.ChangeEvent<HTMLInputElement>) => {
        // check file extension        
        const fileExtension: string | undefined = evt.target.files ? evt.target.files[0].name.split('.').pop() : "";
        if (Constants.Common.ValidExcelExtensions.indexOf(fileExtension as string) === -1) {
            setIsValidFileExtension(true);
            return;
        } else {
            setIsValidFileExtension(false);
        }

        // FormData object to pass along with file so it can be mapped at server side
        const formData = new FormData();
        formData.append("AssessmentId", assessmentId);
        formData.append("File", evt.target.files ? evt.target.files[0] : "");

        shellTableService.uploadFile(formData).then((response) => {
            if (response) {
                getUploadedShellReport(assessmentId); 
            }
        });
    }

    useEffect(() => {
        getUploadedShellReport(assessmentId); 
        bindRespondentTypes();
    }, []);

   // Get Uploaded  Shell Report Files
    const getUploadedShellReport = (assessmentId: string) => {
        questionBankService.getUploadedShellReports(assessmentId).then((response) => {
            setDataShellReportFiles(response.reportFiles);
            
        });
    }

     // Header rows
     const headersRows:string[] = [
        t("Report.FileName"),
        t("Report.FileSize"),
    ];

    // Checks if all questionnaires are finalized and sets the flag
 const areAllQuestionnairesFinalized: boolean = respondentTypes.every((respondentData: RespondentTypeModel) => respondentData.isFinalized === true);
    return (
        <>
            <div className="response-action-wrapper h-200 d-flex align-items-center justify-content-center">
                <div className="button-action-section d-flex justify-content-center p-3">
                    {
                        (areAllQuestionnairesFinalized && userPermission.canUploadFile) ?
                            <>                                
                                <FileUploader
                                    key={`fileuploader_${Math.random()}`}
                                    id="template"
                                    linkCss={classNames("btn", "app-btn-secondary", "ms-2")}
                                    onChange={uploadFile}
                                    accept=".xlsx, .xls"
                                    label={t("Assessment.DataCollection.QuestionBankSurvey.UploadShellTable")}
                                >
                                    <></>
                                </FileUploader>
                            
                            </>
                            :
                            <>
                                {assessmentStatus != AssessmentStatus.Published ?
                                    <>
                                        {t("Assessment.DataCollection.QuestionBankSurvey.FinalizeRespondentType")}
                                    </>
                                    :
                                    <>
                                        {t("Assessment.DataCollection.QuestionBankSurvey.PublishAssessment")}
                                    </>
                                }
                            </>
                    }
                </div>                
            </div>
            <>       
            {isValidFileExtension &&
                <span className="d-flex justify-content-center Mui-error"> {t("Exception.InvalidFile")} </span>
            }
            </>

            <>
                {dataShellReportFiles.length ?
                    (
                        <Table>
                            <>
                                <TableHeader
                                    headers={headersRows.map((header: string) => header)}
                                />
                                <TableBody>
                                    <>
                                        {dataShellReportFiles.map((dataAnalysisReportFile: DataAnalysisReportFile, index: number) => {
                                            return (
                                                <TableRow key={`file${index}_${Math.random()}`}>
                                                    <>
                                                        <TableCell>{dataAnalysisReportFile.name}</TableCell>
                                                        <TableCell>{dataAnalysisReportFile.size}</TableCell>       
                                                    </>
                                                </TableRow>
                                            )
                                        }
                                        )}
                                    </>
                                </TableBody>
                            </>
                        </Table>
                    ) 
                    :
                    <>
                        <p className="p-4">
                            {t("Report.Message.NoFileUploaded")}
                        </p>
                    </>
                }
            </>      
        </>
    )
}

export default SurveyResult;