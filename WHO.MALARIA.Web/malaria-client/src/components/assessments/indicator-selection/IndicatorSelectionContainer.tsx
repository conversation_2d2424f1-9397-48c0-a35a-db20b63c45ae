import React, { useEffect, useState } from "react";
import RadioButtonGroup from "../../controls/RadioButtonGroup";
import Tabs, { WHOTab } from "../../controls/Tabs";
import IndicatorsSelection from "./IndicatorsSelection";

import { assessmentService } from "../../../services/assessmentService";
import classNames from "classnames";
import classes from "../assessment.module.scss";
import localClasses from "./scopedefinition.module.scss";
import { AssessmentApproach } from "../../../models/Enums";
import MultiSelectModel from "../../../models/MultiSelectModel";
import { useTranslation } from "react-i18next";
import {
    GetObjectiveModel,
    GetSubObjectiveModel,
} from "../../../models/ScopeDefinitionModel";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import SubObjectives from "./SubObjectives";
import UserMessage from "../../common/UserMessage";
import { UtilityHelper } from "../../../utils/UtilityHelper";

type ScopeDefinitionProps = {
    children?: React.ReactElement;
    assessmentApproach: number;
    canDisable: boolean;
    onPreviousButtonClick: () => void;
};

/** Renders Scope Definition scren */
const IndicatorSelectionContainer = (props: ScopeDefinitionProps) => {
    const {
        children,
        assessmentApproach,
        canDisable,
        onPreviousButtonClick,
    } = props;

    const { t } = useTranslation();
    document.title = t("app.IndicatorSelectionTitle");
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const [subObjectiveId, setSubObjectiveId] = useState("");
    const [loading, setLoading] = useState(false);
    const [indicatorApproach, setIndicatorApproach] =
        useState<number>(assessmentApproach);

    const [objectives, setObjectives] = useState<Array<GetObjectiveModel>>([]);
    const [subObjectives, setSubObjectives] = useState<
        Array<GetSubObjectiveModel>
    >([]);
    const [filteredSubObjectives, setfilteredSubObjectives] = useState<
        Array<GetSubObjectiveModel>
    >([]);

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        bindObjectives();
    }, [currentlanguage]);

    // bind objectives
    const bindObjectives = () => {
        setLoading(true);
        assessmentService
            .getObjectives()
            .then((objectives: Array<GetObjectiveModel>) => {
                setObjectives(objectives);
                bindSubObjectives(objectives[0]?.id);
                setLoading(false);
            });
    };

    // bind sub-objectives
    const bindSubObjectives = (categoryId: string) => {
        assessmentService
            .getSubObjectives(categoryId)
            .then((subObjectives: Array<GetSubObjectiveModel>) => {
                setSubObjectives(subObjectives);
                setSubObjectiveId(subObjectives[0]?.id);
                setfilteredSubObjectives(subObjectives);
                setLoading(false);
            });
    };

    // triggers whenever user changes the category tabs
    const onTabCategoryChange = (currentTabIndex: number) => {
        setLoading(true);
        const _subObjectives = subObjectives.filter(
            (subObj: GetSubObjectiveModel) =>
                subObj.objectiveId === objectives[currentTabIndex].id
        );

        setCurrentIndex(currentTabIndex);
        setfilteredSubObjectives(_subObjectives);
        bindSubObjectives(objectives[currentTabIndex].id);
    };

    // triggers whenever user changes the sub-category
    const onSubCategoryChange = (subObjectiveId: string) => {
        setSubObjectiveId(subObjectiveId);
    };

    const indicatorApproaches: Array<MultiSelectModel> = [
        new MultiSelectModel(
            AssessmentApproach.Rapid,
            t("Assessment.ScopeDefinition.RapidApproach"),
            false,
            false
        ),
        //TODO: Need to uncomment later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination 
        //new MultiSelectModel(
        //    AssessmentApproach.Tailored,
        //    t("Assessment.ScopeDefinition.TailoredApproach"),
        //    false,
        //    false
        //),
        //new MultiSelectModel(
        //    AssessmentApproach.Comphrehensive,
        //    t("Assessment.ScopeDefinition.ComprehensiveApproach"),
        //    false,
        //    false
        //),
    ];

    // set the assessment approach
    const onAssessmentApproachChange = (approach: AssessmentApproach) => {
        setIndicatorApproach(approach);
    };
    // check if scope definition can be rendered
    const canRenderScopeDefinition = true;

    if (objectives.length === 0 && subObjectives.length === 0) {
        return (
            <div className="flex-container">
                <h6>
                    <HourglassEmptyIcon />
                    {t("Assessment.ScopeDefinition.IndicatorLoadingMessage")}
                </h6>
            </div>
        );
    }
    return (
        <div className={classNames(localClasses.wrapper)}>
            {/*TODO: Below lines needs to be uncomment later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination */}
            {/*<small >*/}
            {/*    {t("Assessment.ScopeDefinition.SelectIndicatorsApproach")}*/} 
            {/*</small>*/}
            <div className={classNames("border", "mt-2", "rounded", classes.br_b)}>
                <fieldset disabled={canDisable}>
                    <div className={classNames("px-4", "py-1", classes.br_b)}>
                        <RadioButtonGroup
                            key={`select`}
                            row
                            color="primary"
                            className="d-flex justify-content-between"
                            options={indicatorApproaches}
                            value={indicatorApproach}
                            defaultChecked={true} //TODO: Needs to remove default check later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination 
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                setIndicatorApproach(parseInt(e.target.value));
                            }}
                        />
                    </div>
                </fieldset>
                <div className="cagtegory_header">
                    <Tabs
                        tabs={objectives.map((objective: GetObjectiveModel): WHOTab => {
                            return { id: objective.id, label: objective.name };
                        })}
                        className={classNames("d-flex", "px-3", classes.tabWrapper)}
                        activeTabIndex={currentIndex}
                        onClick={onTabCategoryChange}
                    >
                        <div
                            className={classNames(
                                "d-flex",
                                "align-items-stretch",
                                "mt-5",
                                classes.categoryWrapper,
                                {
                                    "justify-content-center": !canRenderScopeDefinition,
                                }
                            )}
                        >
                            {canRenderScopeDefinition ? (
                                <>
                                    <div
                                        className={classNames(localClasses.subCategoriesWrapper)}
                                    >
                                        {!loading ? (
                                            <SubObjectives
                                                objectiveId={objectives[currentIndex]?.id as string}
                                                subObjectiveId={subObjectiveId}
                                                subObjectives={filteredSubObjectives}
                                                onClick={onSubCategoryChange}
                                            />
                                        ) : (
                                            <UserMessage
                                                className={classNames(classes.noRecordFound)}
                                                content={t("Common.Loading")}
                                            />
                                        )}
                                    </div>
                                    <div
                                        className={classNames(
                                            "indicators-wrapper",
                                            localClasses.indicatorsWrapper
                                        )}
                                    >
                                        <IndicatorsSelection
                                            objectiveId={objectives[currentIndex].id as string}
                                            subObjectiveId={subObjectiveId}
                                            indicatorApproach={indicatorApproach}
                                            canDisable={canDisable}
                                            onPreviousButtonClick={onPreviousButtonClick}
                                            onAssessmentApproachChange={onAssessmentApproachChange}
                                        />
                                    </div>
                                </>
                            ) : (
                                <UserMessage
                                    className={classNames(classes.noRecordFound)}
                                    content={t(
                                        "Assessment.ScopeDefinition.IndicatorNoRecordFound"
                                    )}
                                />
                            )}
                        </div>
                    </Tabs>
                </div>

                <div>{children}</div>
            </div>
        </div>
    );
};

export default IndicatorSelectionContainer;
