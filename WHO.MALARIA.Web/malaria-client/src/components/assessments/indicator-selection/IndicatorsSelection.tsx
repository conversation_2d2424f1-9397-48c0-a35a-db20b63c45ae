import classNames from "classnames";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import InfoIcon from "@mui/icons-material/Info";
import DataGrid from "../../controls/DataGrid";
import Tooltip from "../../controls/Tooltip";
import { assessmentService } from "../../../services/assessmentService";
import {
    AssessmentApproach,
    AssessmentStatus,
    AssessmentTabs,
    DialogAction,
    StrategyStepper,
} from "../../../models/Enums";
import {
    GridCellProps,
    GridColumnProps,
    GridHeaderCellProps,
} from "@progress/kendo-react-grid";
import { getter } from "@progress/kendo-data-query";
import { IconButton, Link } from "@mui/material";
import React, { useState } from "react";

import useIndicatorSelection from "./useIndicatorSelection";

import { StrategySelectionModel } from "../../../models/ScopeDefinitionModel";
import Checkbox from "../../controls/Checkbox";
import {
    GetIndicatorsRequestModel,
    SaveIndicatorSelectionRequestModel,
} from "../../../models/RequestModels/ScopeDefinitionRequestModel";
import { GetIndicatorModel } from "../../../models/IndicatorModel";
import { useEffect } from "react";
import { useNavigate,  useLocation } from "react-router-dom";
import { Preference, ValidationStatus } from "../../../models/Enums";

import { UtilityHelper } from "../../../utils/UtilityHelper";
import { Constants } from "../../../models/Constants";
import { notificationService } from "../../../services/notificationService";
import { ErrorModel } from "../../../models/ErrorModel";
import classes from "./scopedefinition.module.scss";
import ConfirmationDialog from "../../common/ConfirmationDialog";
import { UserAssessmentPermission } from "../../../models/PermissionModel";
import { FinalizeAssessment } from "../../../models/AssessmentModel";
import { authService } from "../../../services/authService";
import { setAssessmentTabIndex } from "../../../redux/ducks/assessment-tab";
import { setUserAssessmentPermission } from "../../../redux/ducks/user-permission";

type IndicatorSelectionProps = {
    canDisable: boolean;
    subObjectiveId: string;
    objectiveId: string;
    indicatorApproach: number;
    onPreviousButtonClick: () => void;
    onAssessmentApproachChange: (approach: AssessmentApproach) => void;
};

const DATA_ITEM_KEY = "id";
const SELECTED_FIELD = "selected";
const idGetter = getter(DATA_ITEM_KEY);

/** Renders the Indicator Selection */
function IndicatorsSelection(props: IndicatorSelectionProps) {
    const { t } = useTranslation();
    const location: any = useLocation();
    const state = location?.state;
    const status = location?.state.status;
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const permission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const assessmentId = location?.state?.assessmentId;
    const {
        canDisable,
        objectiveId,
        subObjectiveId,
        indicatorApproach,
        onAssessmentApproachChange,
        onPreviousButtonClick
    } = props;

    const { canFinalize } = permission;

    const [headerChecked, setHeaderChecked] = useState<boolean>(false);
    const [indicators, setIndicators] = useState<Array<GetIndicatorModel>>([]);
    const [switchApproachDialog, setSwitchApproachDialog] =
        useState<boolean>(false);

    const {
        onIndicatorChange,
        getAssessmentIndicators,
        onAllIndicatorSelected,
        onSubmit,
    } = useIndicatorSelection(assessmentId, indicatorApproach);

    const strategySelection: StrategySelectionModel = useSelector(
        (state: any) => state.scopeDefinition.strategySelection
    );

    const indicatorSelection: SaveIndicatorSelectionRequestModel = useSelector(
        (state: any) => state.scopeDefinition.indicatorSelection
    );

    const [openConfirmation, setOpenConfirmation] = useState<boolean>(false);

    const currentUser = authService.getCurrentUser();

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        bindIndicators();
    }, [currentlanguage]);

    useEffect(() => {
        setHeaderChecked(false);
    }, [subObjectiveId, objectiveId]);

    // The indicator checkbox is checked based on the approach chosen.
    useEffect(() => {
        if (indicators.length) {

            switch (indicatorApproach) {

                case AssessmentApproach.Comphrehensive:
                    const allIds: Array<string> = indicators.map(
                        ({ id }) => id
                    );

                    onAllIndicatorSelected(allIds);
                    break;

                case AssessmentApproach.Rapid:
                    const highPriorityIndicatorIds: Array<string> = indicators
                        .filter(
                            (indicator: GetIndicatorModel) =>
                                indicator.indicatorPriority === Preference.Priority
                        )
                        .map((indicator: GetIndicatorModel) => indicator.id);

                    onAllIndicatorSelected(highPriorityIndicatorIds);
                    break;
            }
        }
    }, [indicatorApproach, indicators, currentlanguage]);

    // find the indicatorIds
    const { indicatorIds } = indicatorSelection;

    // Create data to be bound
    const bindIndicators = () => {
        if (!isVaildParam()) {
            return;
        }

        const requestModel: GetIndicatorsRequestModel = createParam();
        return assessmentService
            .getIndicators(requestModel)
            .then((indicators: Array<GetIndicatorModel>) => {
                setIndicators(
                    indicators.filter(
                        (indicator: GetIndicatorModel) =>
                            indicator.indicatorPriority === Preference.Priority  //TODO: needs to remove filter later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination with only prioritized indicators 
                    ).map((indicator: GetIndicatorModel, index: number) => {
                        return {
                            [SELECTED_FIELD]: false,
                            sr: ++index,
                            ...indicator,
                        };
                    })
                );
                const highPriorityIndicatorIds = indicators
                    .filter(
                        (indicator: GetIndicatorModel) =>
                            indicator.indicatorPriority === Preference.Priority
                    )
                    .map((indicator: GetIndicatorModel) => indicator.id);
                // get the mapped indicators with assessment
                getAssessmentIndicators(highPriorityIndicatorIds);
            });
    };

    // get indicators by sub-objective
    const getIndicatorsBySubObjectiveId = (
        indicators: Array<GetIndicatorModel>
    ) => {
        const filteredIndicators = indicators.filter(
            (indicator: GetIndicatorModel) =>
                indicator.subObjectiveId === subObjectiveId 
        );

        return filteredIndicators;
    };

    const highPriorityIndicatorIds = indicators
        .filter(
            (indicator: GetIndicatorModel) =>
                indicator.indicatorPriority === Preference.Priority
        )
        .map((indicator: GetIndicatorModel) => indicator.id);

    // render checkbox as first column
    const renderCheckBox = (props: GridCellProps) => {
        return (
            <td
                className={classNames({
                    [classes.disabled]:
                        parseInt(props.dataItem["indicatorPriority"]) ===
                        Preference.Priority,
                })}
            >
                <Checkbox
                    color="primary"
                    className="p-0"
                    label=""
                    name="indicator"
                    disabled={
                        parseInt(props.dataItem["indicatorPriority"]) ===
                        Preference.Priority
                    }
                    value={props.dataItem["id"]}
                    disableRipple
                    disableFocusRipple
                    disableTouchRipple
                    inputProps={{ "aria-label": "secondary checkbox" }}
                    checked={[...highPriorityIndicatorIds, ...indicatorIds].includes(
                        props.dataItem["id"] || false
                    )}
                    onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                        setHeaderChecked(false);
                        onIndicatorChange(evt);
                    }}
                />
            </td>
        );
    };

    // get list of all indicators belonging to sub-objective
    const indicatorIdsForSubObjective = indicators
        .filter(
            (indicator: GetIndicatorModel) =>
                indicator.subObjectiveId === subObjectiveId
        )
        .map((indicator: GetIndicatorModel) => indicator.id);

    // check if all indicators are selected for sub-objective
    const markHederChecked =
        indicatorSelection.indicatorIds.filter((id: string) =>
            indicatorIdsForSubObjective.includes(id)
        ).length === indicatorIdsForSubObjective.length && indicatorIdsForSubObjective.length > 0; //TODO: needs to remove and condition later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination 

    // create column definition
    const getColDefs = (): Array<GridColumnProps> => {
        return [
            {
                width: "120px",
                field: SELECTED_FIELD,
                cell: (props: GridCellProps) => renderCheckBox(props),
                headerClassName: classes.headerCell,
                headerCell: (props: GridHeaderCellProps) => (
                    <span>
                        <Checkbox
                            className="p-0"
                            label=""
                            name="selectAll"
                            checked={headerChecked || markHederChecked}
                            onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                setHeaderChecked(evt.target.checked);
                                if (!evt.target.checked) {
                                    onAllIndicatorSelected([
                                        ...new Set([
                                            ...indicatorSelection.indicatorIds,
                                            ...highPriorityIndicatorIds,
                                        ]),
                                    ]);
                                } else {
                                    const allIds = getIndicatorsBySubObjectiveId(indicators).map(
                                        ({ id }) => id
                                    );
                                    onAllIndicatorSelected([
                                        ...new Set([
                                            ...indicatorSelection.indicatorIds,
                                            ...allIds,
                                            ...highPriorityIndicatorIds,
                                        ]),
                                    ]);
                                }
                            }}
                        />
                    </span>
                ),
            },
            {
                title: "Indicator",
                sortable: true,
                cell: (props: GridCellProps) => (
                    <td
                        className={classNames({
                            [classes.disabled]:
                                parseInt(props.dataItem["indicatorPriority"]) ===
                                Preference.Priority,
                        })}
                    >
                        <span>{props.dataItem["sequence"]}</span>
                        <span className="ps-2">{props.dataItem["name"]} </span>
                        <IconButton className="grid-icon-button">
                            <Tooltip content={props.dataItem["description"]} isHtml>
                                <InfoIcon fontSize="small" />
                            </Tooltip>
                        </IconButton>
                    </td>
                ),
            },
        ];
    };

    // validate the param before sending
    const isVaildParam = () => {
        const sessionStorageValue: string = UtilityHelper.getFromSessionStorage(
            Constants.SessionStorageKey.STRATEGY_SELECTION_NAV_IDS
        );
        if (sessionStorageValue.length === 0) {
            notificationService.sendMessage(
                new ErrorModel(
                    "Invalid arugments to fetch indicators. Please select strategies.",
                    ValidationStatus.Failed
                )
            );
            return false;
        }
        return true;
    };

    // create parameter for request model
    const createParam = () => {
        // if both are empty then get the object from session Storage
        const sessionStorageValue: string = UtilityHelper.getFromSessionStorage(
            Constants.SessionStorageKey.STRATEGY_SELECTION_NAV_IDS
        );

        const _strategySelection: StrategySelectionModel =
            JSON.parse(sessionStorageValue);

        // check if user has selected 'Both' option
        let caseStrategyIds: Array<string> = [];
        if (
            _strategySelection.caseStrategyIds.length > 0 &&
            _strategySelection.caseStrategyIds[0].indexOf(",") > -1
        ) {
            caseStrategyIds = _strategySelection.caseStrategyIds[0].split(",");
        } else {
            caseStrategyIds = _strategySelection.caseStrategyIds;
        }

        return new GetIndicatorsRequestModel(
            caseStrategyIds,
            _strategySelection.malariaControlStrategyIds
        );
    };

    // triggered whenever finalized buttons is clicked
    const onButtonClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onFinalize();
                break;
            case DialogAction.Close:
                setOpenConfirmation(false);
                break;
        }
    };

    // triggers whenever user clicks on 'Yes' button of confirmation dialog buton
    const onFinalize = () => {
        const assessmentId = location?.state?.assessmentId;

        assessmentService
            .finalizeAssessment(
                new FinalizeAssessment(currentUser.userId, assessmentId)
            )
            .then((success: boolean) => {
                navigate("/assessment/data-collection/desk-review", { state: { ...state, status: AssessmentStatus.Finalized } });
                dispatch(setAssessmentTabIndex(AssessmentTabs.DataCollection));
                getAssessmentPermission();
            });

        setOpenConfirmation(false);
    };

    // get an assessment permission for user            
    const getAssessmentPermission = () => {
        const assessmentId = location?.state?.assessmentId;
        if (!assessmentId) return;

        assessmentService
            .getPermission(assessmentId)
            .then((permission: UserAssessmentPermission) => {
                dispatch(setUserAssessmentPermission(permission))
            });
    }

    // triggers whenever form is submitted
    const handleSubmit = (evt: React.FormEvent<HTMLFormElement>) => {
        evt.preventDefault();
        const isValid = validateIndicatorType();
        if (isValid) {
            onSubmit(evt);
        }
    };

    // check the validation type for assessment approach before submitting the data
    const validateIndicatorType = () => {
        switch (indicatorApproach) {
            case AssessmentApproach.Rapid:
                if (
                    highPriorityIndicatorIds.length <
                    indicatorSelection.indicatorIds.length
                ) {
                    setSwitchApproachDialog(true);
                    return false;
                }
                //TODO: Below lines needs to be uncomment later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination 
                //else if (indicatorSelection.indicatorIds.length === indicators.length) {
                //    setSwitchApproachDialog(true);
                //    return false;
                //}
                break;
            case AssessmentApproach.Comphrehensive:
                if (indicators.length > indicatorSelection.indicatorIds.length) {
                    setSwitchApproachDialog(true);
                    return false;
                }
                break;
            case AssessmentApproach.Tailored:
                if (highPriorityIndicatorIds.length === indicatorSelection.indicatorIds.length) {
                    setSwitchApproachDialog(true);
                    return false;
                }

                if (indicators.length === indicatorSelection.indicatorIds.length) {
                    setSwitchApproachDialog(true);
                    return false;
                }
                break;
        }

        return true;
    };

    // Triggers whenever confirmation dialog buttons are clicked
    const onConfirmationButtonClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                //If all indicators are selected then we are switching to Comphrehensive type.
                if (indicators.length === indicatorSelection.indicatorIds.length) {
                    onAssessmentApproachChange(AssessmentApproach.Comphrehensive);
                }
                else {
                    onAssessmentApproachChange(AssessmentApproach.Tailored);
                }

                setSwitchApproachDialog(false);
                break;

            case DialogAction.Close:
                setSwitchApproachDialog(false);
                break;
        }
    };

    // get message for validation
    const getConfirmationDialogMessage = () => {
        switch (indicatorApproach) {
            case AssessmentApproach.Comphrehensive:
                return t("Assessment.ScopeDefinition.AssessmentApproachComphrehensiveDialogMessage");

            case AssessmentApproach.Rapid:
                if (indicatorSelection.indicatorIds.length === indicators.length) {
                    return t("Assessment.ScopeDefinition.AssessmentApproachRapidToComprehensiveDialogMessage");
                }
                return t("Assessment.ScopeDefinition.AssessmentApproachRapidDialogMessage");

            case AssessmentApproach.Tailored:
                if (indicatorSelection.indicatorIds.length === indicators.length) {
                    return t("Assessment.ScopeDefinition.AssessmentApproachRapidToComprehensiveDialogMessage");
                }
                return t("Assessment.ScopeDefinition.AssessmentApproachTailoeredDialogMessage");
        }
        return "";
    };

    if (!assessmentId) {
        return (
            <div className="flex-container">
                <h6>
                    Please select an
                    <Link onClick={() => navigate("/assessments", { replace: true })}>
                        Assessment
                    </Link>
                    first.
                </h6>
            </div>
        );
    }

    return (
        <form onSubmit={handleSubmit}>
            <ConfirmationDialog
                title={t("Assessment.ScopeDefinition.AssessmentApproachDialogTitle")}
                content={getConfirmationDialogMessage()}
                open={switchApproachDialog}
                onClick={onConfirmationButtonClick}
            />
            <fieldset
                disabled={canDisable}
                className={classNames(classes.indicatorWrapper)}
            >
                <DataGrid
                    className="k-grid-wrapper"
                    columns={getColDefs()}
                    data={getIndicatorsBySubObjectiveId(indicators)}
                    dataItemKey={DATA_ITEM_KEY}
                    selectedField={SELECTED_FIELD}
                    selectable={{
                        enabled: true,
                        drag: false,
                        cell: true,
                        mode: "single",
                    }}
                />
            </fieldset>
            <div className="button-action-section button-bottom d-flex justify-content-center p-3">
                <button
                    type="button"
                    className={classNames("btn", "app-btn-secondary")}
                    onClick={onPreviousButtonClick}
                >
                    {t("Common.Previous")}
                </button>
                {!canDisable && (
                    <button
                        type="submit"
                        className={classNames("btn", "app-btn-primary")}
                    >
                        {t("Common.Save")}
                    </button>
                )}
                {/*Shows finalize button once indicators are saved and assessment status is typeselected and ist's hidden once
                 assessemnt is finalized */}
                {!canDisable && (canFinalize && (status <= AssessmentStatus.Finalized && status >= AssessmentStatus.TypeSelected)) && (
                    <button
                        className="btn app-btn-primary"
                        onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                            evt.preventDefault();
                            setOpenConfirmation(true);
                        }}
                    >
                        {t("Assessment.ScopeDefinition.Finalize")}
                    </button>
                )}
            </div>
            <ConfirmationDialog
                open={openConfirmation}
                title={t("Assessment.ScopeDefinition.FinalizeDialogTitle")}
                content={t("Assessment.ScopeDefinition.FinalizeDialogContent")}
                onClick={onButtonClick}
            />
        </form>
    );
}

export default IndicatorsSelection;
