import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate,  useLocation } from "react-router-dom";
import { AssessmentApproach, AssessmentStatus, ValidationStatus } from "../../../models/Enums";
import { BaseMessageModel } from "../../../models/ErrorModel";
import { GetIndicatorModel } from "../../../models/IndicatorModel";
import { SaveIndicatorSelectionRequestModel } from "../../../models/RequestModels/ScopeDefinitionRequestModel";
import { updateIndicatorSelection } from "../../../redux/ducks/scopedefinition";
import { assessmentService } from "../../../services/assessmentService";
import { notificationService } from "../../../services/notificationService";

/** Custom hook for indicator selection */
const useIndicatorSelection = (
    assessmentId = "",
    assessmentApproach = AssessmentApproach.Rapid
) => {
    const navigate = useNavigate();
    const location: any = useLocation();
    const state = location?.state;
    const indicatorSelection: SaveIndicatorSelectionRequestModel = useSelector(
        (state: any) => state.scopeDefinition.indicatorSelection
    );

    const dispatch = useDispatch();

    /** Triggers whenever indicators are changed */
    const onIndicatorChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        let selectedIndicatorIds = [...indicatorSelection.indicatorIds];

        if (evt.target.checked) {
            selectedIndicatorIds = [...selectedIndicatorIds, evt.target.value];
        } else {
            selectedIndicatorIds = selectedIndicatorIds.filter(
                (id: string) => id !== evt.target.value
            );
        }

        const _updatedIndicators: SaveIndicatorSelectionRequestModel =
            new SaveIndicatorSelectionRequestModel(
                assessmentId,
                [...new Set(selectedIndicatorIds)],
                assessmentApproach
            );
        dispatch(updateIndicatorSelection(_updatedIndicators));
    };

    /** Triggered by select all checkbox on a grid */
    const onAllIndicatorSelected = (selectedIndicatorIds: Array<string>) => {
        const _updatedIndicators: SaveIndicatorSelectionRequestModel =
            new SaveIndicatorSelectionRequestModel(
                assessmentId,
                [...new Set(selectedIndicatorIds)],
                assessmentApproach
            );
        dispatch(updateIndicatorSelection(_updatedIndicators));
    };

    // check the validity of the form
    const isValid = (): boolean => {
        if (!assessmentId) {
            sendNotification(
                "AssessmentId is missing. Please select the assessment first."
            );
            return false;
        }

        if (indicatorSelection.indicatorIds.length == 0) {
            sendNotification("Please select the indicators.");
            return false;
        }

        return true;
    };

    /** Send user notification */
    const sendNotification = (
        message: string,
        status: number = ValidationStatus.Failed
    ) => {
        notificationService.sendMessage(new BaseMessageModel(message, status));
    };

    /** Get assessment indicators
     * @pram List of HighPriorityIndicators Ids which needs to be filled default
     */
    const getAssessmentIndicators = (highPriorityIndicators: Array<string>) => {
        assessmentService
            .getAssessmentIndicators(assessmentId)
            .then((indicators: Array<GetIndicatorModel>) => {
                convertToIndicatorSelectionModel(indicators, highPriorityIndicators);
            });
    };

    // Convert GetIndicatorModel to SaveIndicatorSelectionRequestModel
    const convertToIndicatorSelectionModel = (
        indicators: Array<GetIndicatorModel>,
        highPriorityIndicators: Array<string>
    ) => {
        let indicatorIds = indicators.map(
            (indicator: GetIndicatorModel) => indicator.id
        );
        // combine both indicators
        const allIndicatorIds = [...highPriorityIndicators, ...indicatorIds];
        indicatorIds = [...new Set(allIndicatorIds)];
        dispatch(
            updateIndicatorSelection(
                new SaveIndicatorSelectionRequestModel(
                    assessmentId,
                    indicatorIds,
                    location?.state?.approach
                )
            )
        );
    };

    /** Triggers whenever user submits the form */
    const onSubmit = (evt: React.FormEvent<HTMLFormElement>) => {
        if (!isValid()) return;

        assessmentService
            .saveIndicatorSelection(
                new SaveIndicatorSelectionRequestModel(
                    assessmentId,
                    indicatorSelection.indicatorIds,
                    assessmentApproach
                )
            )
            .then((success: boolean) => {
                if (success) {                                     
                    const status = AssessmentStatus.TypeSelected;
                    const approach = assessmentApproach;
                    navigate("/assessment/scope-definition/indicator", { replace: true, state: { ...state, status, approach } });
                }
            });
    };

    return {
        onIndicatorChange,
        getAssessmentIndicators,
        onAllIndicatorSelected,
        onSubmit,
    };
};

export default useIndicatorSelection;
