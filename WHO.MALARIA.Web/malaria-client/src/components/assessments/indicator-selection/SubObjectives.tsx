import { useEffect, useState } from "react";

import classNames from "classnames";
import classes from "./scopedefinition.module.scss";
import RightIcon from "@mui/icons-material/KeyboardArrowRight";
import { GetSubObjectiveModel } from "../../../models/ScopeDefinitionModel";
import { useTranslation } from "react-i18next";
import UserMessage from "../../common/UserMessage";

type SubCategoryProps = {
  objectiveId: string;

  subObjectiveId: string;

  subObjectives?: Array<GetSubObjectiveModel>;

  defaultSubCategoryId?: string;

  onClick: (subCatId: string) => void;
};

/** Renders Sub-Objectives for Objective */
const SubObjectives = (props: SubCategoryProps) => {
  const { subObjectives, objectiveId, subObjectiveId, onClick } = props;

  const { t } = useTranslation();

  const [currentSubObjectiveId, setCurrentSubObjectiveId] =
    useState<string>(subObjectiveId);

  useEffect(() => {
    setCurrentSubObjectiveId(subObjectiveId);
  }, [subObjectiveId]);

  if (subObjectives && subObjectives.length === 0)
    return (
      <UserMessage
        className="fw-lighter px-4"
        content={t("Assessment.ScopeDefinition.SubObjectiveNoRecordFound")}
      />
    );

  return (
    <ul>
      {subObjectives?.map((subCat: GetSubObjectiveModel, index: number) => (
        <li
          key={`${subCat.name}`}
          className={classNames("d-flex", {
            [classes.selected]: currentSubObjectiveId === subCat?.id,
          })}
          onClick={() => {
            setCurrentSubObjectiveId(subCat.id);
            onClick(subCat.id);
          }}
        >
          <span className="pe-2">{subCat.sequence}</span>
          {subCat.name}
          {currentSubObjectiveId === subCat?.id && <RightIcon />}
        </li>
      ))}
    </ul>
  );
};

export default SubObjectives;
