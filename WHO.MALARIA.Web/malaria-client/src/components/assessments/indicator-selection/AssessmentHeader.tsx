import classNames from "classnames";
import LeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import classes from "../assessment.module.scss";
import { IconButton } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Constants } from "../../../models/Constants";

/** Renders Assessment header */
const AssessmentHeader = ({ country = "", percentage = undefined }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const selectedReaportYears = sessionStorage.getItem(
    Constants.SessionStorageKey.SELECTED_REPORT_YEAR
  );

  return (
    <div className={classNames("row", classes.br_b, classes.assessmentHeader)}>
      <div
        className={classNames(
          "col-md-4",
          "d-flex",
          "justify-content-left",
          "align-items-center"
        )}
      >
        <IconButton onClick={() => navigate("/assessments")}>
          <LeftIcon />
        </IconButton>
        <span className={classNames("fw-bold", "text-uppercase")}>
          {t("Assessment.AllAssessments")}
        </span>
        <span style={{ display: "none", paddingLeft: "10px" }}>
          {selectedReaportYears}
        </span>
      </div>
      <div
        className={classNames("col-md-4", "d-flex", "justify-content-center")}
      >
        <span className={classNames("mt-2", "fw-bold")}>{country}</span>
      </div>
    </div>
  );
};

export default AssessmentHeader;
