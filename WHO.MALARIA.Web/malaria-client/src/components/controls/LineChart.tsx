import * as React from "react";

import {
  Chart,
  ChartCategoryAxis,
  ChartCategoryAxisItem,
  ChartProps,
  ChartSeries,
  ChartSeriesItem,
  ChartTitle,
  ChartValueAxis,
  ChartValueAxisItem
} from "@progress/kendo-react-charts";

import "hammerjs";

interface ILineChart {
  chartTitle: string; //Chart title
  xAxisTitle: string; //Chart x-axis title
  xAxisArr: Array<string | number>; //Chart x-axis values (eg. jan,feb)
  lineData: Array<any>; //Chart Data
  lineChartType: any; // Chart type
}

type PieChartProps = ILineChart & ChartProps;

/** Renders Line Chart with values */
const LineChart = (props: PieChartProps) => {
  const { chartTitle, xAxisTitle, xAxisArr, lineChartType, lineData, } = props;

  return (
    <Chart {...props}>
      <ChartTitle text={chartTitle} font="14px Noto Sans" />
      <ChartValueAxis>
        <ChartValueAxisItem
          min={0}
          max={110}
          majorUnit={20}
          minorUnit={10}
          />
      </ChartValueAxis>
      <ChartCategoryAxis>
        <ChartCategoryAxisItem
          title={{ text: xAxisTitle }}
          categories={xAxisArr}
        />
      </ChartCategoryAxis>
      <ChartSeries>
        {lineData.map((item, index: number) => (
          <ChartSeriesItem key={`ChartSeriesItem${index}`} type={lineChartType} data={item} />
        ))}
      </ChartSeries>
    </Chart>
  );
};

export default LineChart;
