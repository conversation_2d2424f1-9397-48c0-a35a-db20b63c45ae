import React, { Suspense } from "react";
import ReactDOM from "react-dom/client";

import "bootstrap/dist/js/bootstrap.js";
import reportWebVitals from "./reportWebVitals";

import { Provider } from "react-redux";
import "bootstrap/dist/css/bootstrap.min.css";
import "flag-icon-css/css/flag-icon.min.css";

import "@progress/kendo-theme-material/dist/all.css";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import "./index.scss";

import App from "./App";
import "./i18n";
import store from "./redux/configureStore";

const LoadingMarkup = () => {
  return (
    <div className="py-4 text-center">
      <h3>Loading..</h3>
    </div>
  );
};
const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(
  <Suspense fallback={<LoadingMarkup />}>
    <Provider store={store}>
      <App />
    </Provider>
  </Suspense>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
