export class GetIndicatorModel {
    constructor(
        public id: string,
        public name: string,
        public sequence: string,
        public description: string,
        public order: number,
        public indicatorPriority: number,
        public subObjectiveId: string,
        public status: number | string
    ) {
        this.id = id;
        this.name = name;
        this.subObjectiveId = subObjectiveId;
        this.sequence = sequence;
        this.description = description;
        this.order = order;
        this.indicatorPriority = indicatorPriority;
        this.status = status;
    }
}