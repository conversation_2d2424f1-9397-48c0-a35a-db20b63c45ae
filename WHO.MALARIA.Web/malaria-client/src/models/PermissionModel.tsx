export class UserAssessmentPermission {
  constructor(
    public canViewDetails: boolean | null,
    public canConfigure: boolean | null,
    public canChangeManager: boolean | null,
    public canChangeCountry: boolean | null,
    public canUpdateStrategies: boolean | null,
    public canUpdateIndicators: boolean | null,
    public canFinalize: boolean | null,
    public canCollectData: boolean | null,
    public canPublish: boolean | null,
    public canUploadFile: boolean | null,
    public canCreateOrUpdateServiceLevel: boolean | null,
    public canDeleteServiceLevel: boolean | null,
    public canFinalizeServiceLevel: boolean | null,
    public canSaveOrFinalizeDRIndicatorResponse: boolean | null,
    public canCreateOrUpdateQuestionBank: boolean | null,
    public canFinalizeQuestionBank: boolean | null,
    public canEditQuestionBankRespondentType: boolean | null,
    public canSaveOrFinalizeDeskLevelDQAVariables: boolean | null,
    public canGenerateOrUploadDeskLevelDQATemplate: boolean | null,
    public canEditAndSaveDeskLevelDQASummaryResult: boolean | null,
    public canFinalizeDeskLevelDQASummaryResult: boolean | null,
    public canExportDeskLevelDQASummary: boolean | null,
    public showResultsOnGlobalDashboard: boolean | null,
    public canExportScoreCard: boolean | null
  ) {
    this.canViewDetails = canViewDetails;
    this.canConfigure = canConfigure;
    this.canChangeManager = canChangeManager;
    this.canChangeCountry = canChangeCountry;
    this.canUpdateStrategies = canUpdateStrategies;
    this.canUpdateIndicators = canUpdateIndicators;
    this.canFinalize = canFinalize;
    this.canCollectData = canCollectData;
    this.canPublish = canPublish;
    this.canUploadFile = canUploadFile;
    this.canCreateOrUpdateServiceLevel = canCreateOrUpdateServiceLevel;
    this.canDeleteServiceLevel = canDeleteServiceLevel;
    this.canFinalizeServiceLevel = canFinalizeServiceLevel;
    this.canSaveOrFinalizeDRIndicatorResponse =
      canSaveOrFinalizeDRIndicatorResponse;
    this.canCreateOrUpdateQuestionBank = canCreateOrUpdateQuestionBank;
    this.canFinalizeQuestionBank = canFinalizeQuestionBank;
    this.canEditQuestionBankRespondentType = canEditQuestionBankRespondentType;
    this.canSaveOrFinalizeDeskLevelDQAVariables =
      canSaveOrFinalizeDeskLevelDQAVariables;
    this.canGenerateOrUploadDeskLevelDQATemplate =
      canGenerateOrUploadDeskLevelDQATemplate;
    this.canEditAndSaveDeskLevelDQASummaryResult =
      canEditAndSaveDeskLevelDQASummaryResult;
    this.canFinalizeDeskLevelDQASummaryResult =
      canFinalizeDeskLevelDQASummaryResult;
    this.canExportDeskLevelDQASummary = canExportDeskLevelDQASummary;
    this.showResultsOnGlobalDashboard = showResultsOnGlobalDashboard;
    this.canExportScoreCard = canExportScoreCard;
  }

  [index: string]: boolean | null;

  static init = () =>
    new UserAssessmentPermission(
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    );
}
