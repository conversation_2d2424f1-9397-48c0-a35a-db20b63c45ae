import format from "date-fns/format";
import { AssessmentApproach } from "./Enums";
import { UserAssessmentPermission } from "./PermissionModel";

export class AssessmentGridModel {
  constructor(
    public assessmentId: string,
    public status: string,
    public name: string,
    public startDate: string,
    public endDate: string,
    public type: string,
    public approach: number = AssessmentApproach.Rapid,
    public canConfigure: boolean = false,
    public canViewDetails: boolean = false,
    public displayStatus: string,
    public caseStrategyType: string,
    public role: number
  ) {
    this.assessmentId = assessmentId;
    this.status = status;
    this.name = name;
    this.startDate = format(new Date(startDate), "yyyy/MM/dd");
    this.endDate = format(new Date(endDate), "yyyy/MM/dd");
    this.type = type;
    this.approach = approach;
    this.canConfigure = canConfigure;
    this.canViewDetails = canViewDetails;
    this.displayStatus = displayStatus;
    this.caseStrategyType = caseStrategyType;
    this.role = role;
  }
}
export class BaseAssessmentModel {
  constructor(
    public startDate: Date,
    public endDate: Date,
    public manager: string,
    public editors: Array<string>,
    public reviewers: Array<string>
  ) {
    this.startDate = startDate;
    this.endDate = endDate;
    this.manager = manager;
    this.editors = editors;
    this.reviewers = reviewers;
  }
}
export class CreateAssessmentModel extends BaseAssessmentModel {
  constructor(
    public countryId: string,
    public startDate: Date,
    public endDate: Date,
    public manager: string,
    public editors: Array<string>,
    public reviewers: Array<string>,
    public permissions: UserAssessmentPermission
  ) {
    super(startDate, endDate, manager, editors, reviewers);
    this.permissions = permissions;
    this.countryId = countryId;
  }

  static init = () =>
    new CreateAssessmentModel(
      "",
      new Date(),
      new Date(),
      "",
      [],
      [],
      UserAssessmentPermission.init()
    );
}

export class UpdateAssessmentRequestModel extends BaseAssessmentModel {
  constructor(
    public assessmentId: string,
    public country: string,
    public startDate: Date,
    public endDate: Date,
    public manager: string,
    public editors: Array<string>,
    public reviewers: Array<string>
  ) {
    super(startDate, endDate, manager, editors, reviewers);

    this.assessmentId = assessmentId;
    this.country = country;
  }

  static init = () =>
    new UpdateAssessmentRequestModel(
      "",
      "",
      new Date(),
      new Date(),
      "",
      [],
      []
    );
}
export class FinalizeAssessment {
  constructor(public currentUserId: string, public assessmentId: string) {
    this.currentUserId = currentUserId;
    this.assessmentId = assessmentId;
  }
}
