﻿import * as Cookies from "js-cookie";
import { Constants } from "../models/Constants";
import CryptoJS from "crypto-js";
import { authService } from "../services/authService";
import { KeyValuePair } from "../models/DeskReview/KeyValueType";
import { getMonth, getYear } from "date-fns";
import format from "date-fns/format";
import parse from "html-react-parser";
import PeopleIcon from "@mui/icons-material/People";
import InfoIcon from "../images/InfoIcon.svg";
import ReactGA from "react-ga4";
import { Language, UserRoleEnum } from "../models/Enums";
import LanguageTranslationWrapperComponent from "../components/assessments/data-collection/desk-review/responses/LanguageTranslationWrapperComponent";

interface MonthsLocaleData {
  name: string;
  name_fr: string;
}

/** Application based Utility helper which holds the common methods used through out the application */
export class UtilityHelper {
  /** Check if the logged in user is new */
  static isNewUser = (): boolean => {
    const isNewUser = Cookies.get(Constants.Common.newUserCookieName);

    return isNewUser ? true : false;
  };

  /**
   * Retrieve cookie details by key
   * @param key: key name
   */
  static getCookieValue = (key: string): string => {
    return Cookies.get(key) || "";
  };

  /**
   * Decrypt the chipertext
   * @param ciphertextB64: ciphertextB64 string as input
   */
  static Decrypt = (ciphertextB64: string): string => {
    if (!ciphertextB64) return "";

    var key = CryptoJS.enc.Utf8.parse(Constants.Common.EncryptionKey); // Convert into WordArray (using Utf8)
    var iv = CryptoJS.lib.WordArray.create([0x00, 0x00, 0x00, 0x00]); // Use zero vector as IV
    var decrypted = CryptoJS.AES.decrypt(ciphertextB64, key, { iv: iv }); // By default: CBC, PKCS7

    return decrypted.toString(CryptoJS.enc.Utf8);
  };
  /** returns empty string */
  static IsEmpty = () => "";

  /** Check if the role exists for logged in user */
  static isInRole = (roles: Array<number>) => {
    const currentUser = authService.getCurrentUser();
    const selectedUserType = sessionStorage.getItem(
      Constants.SessionStorageKey.SELECTED_USER_TYPE
    );

    if (selectedUserType == null) {
      if (!currentUser.userType) return false;

      return roles.includes(currentUser.userType);
    } else {
      if (selectedUserType == null) return false;
      return roles.includes(+selectedUserType);
    }
  };

  /** Save item to a sessions storage
   * @param key of string type
   * @param value of any kind
   */
  static saveToSessionStorage = (key: string, value: any) => {
    sessionStorage.setItem(key, JSON.stringify(value));
  };

  /** Get the value from session storage
   * @param key of string type
   */
  static getFromSessionStorage = (key: string): string => {
    return sessionStorage.getItem(key) || "";
  };

  /** Remove an item from sessionStorage
   * @param key as string
   */
  static clearSessionStorage = (key: string) => {
    sessionStorage.removeItem(key);
  };

  /** Allows user to download the content */
  static download = (data: any, fileName: string) => {
    const textUrl = URL.createObjectURL(data);
    const element = document.createElement("a");
    element.setAttribute("href", textUrl);
    element.setAttribute("download", fileName);
    element.style.display = "none";
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  /** Return 12 Month Key Value List */
  static useMonths(): Array<KeyValuePair<string, number | null>> {
    const months = [
      { key: Constants.Common.Months[0], value: null },
      { key: Constants.Common.Months[1], value: null },
      { key: Constants.Common.Months[2], value: null },
      { key: Constants.Common.Months[3], value: null },
      { key: Constants.Common.Months[4], value: null },
      { key: Constants.Common.Months[5], value: null },
      { key: Constants.Common.Months[6], value: null },
      { key: Constants.Common.Months[7], value: null },
      { key: Constants.Common.Months[8], value: null },
      { key: Constants.Common.Months[9], value: null },
      { key: Constants.Common.Months[10], value: null },
      { key: Constants.Common.Months[11], value: null },
    ];
    return months;
  }

  // Get from Date
  static getFromDate = (year: number, month: number) => {
    return format(new Date(year, month, 1), Constants.Common.DefaultDateFormat);
  };

  // Get to Date
  static getToDate = (year: number, month: number) => {
    return format(
      new Date(year, month + 1, 0),
      Constants.Common.DefaultDateFormat
    );
  };

  // Get end Date
  static getEndDate = (date: string) => {
    const month = getMonth(new Date(date));
    const year = getYear(new Date(date));
    if (month > 0) {
      return UtilityHelper.getToDate(year + 1, month - 1);
    } else {
      return UtilityHelper.getToDate(year, 11);
    }
  };

  // Get locale months from date
  static getLocaleMonths = (date: string) => {
    const regularExpression = /^[^/]+/;
    const localeMonth =
      date != null ? date.substring(0, date.lastIndexOf("/")) : "";
    const month: MonthsLocaleData = Constants.Common.MonthsLocale.filter(
      (item) => item.name_fr === localeMonth
    )[0];
    if (month) {
      const newDate = date.replace(regularExpression, month.name);
      return newDate;
    } else return date;
  };

  // Get proper date format from Mon/yyyy
  static getValidDateFormat = (currentlanguage: string, date: string) => {
    if (date != null) {
      const validDate: string[] = date.split("/");
      let monthDate: string = "";

      if (currentlanguage === Language.en) {
        monthDate =
          "" + (new Date(validDate[0] + " 01, " + validDate[1]).getMonth() + 1);
      } else {
        const monthLocale: MonthsLocaleData =
          Constants.Common.MonthsLocale.filter(
            (item) => item.name_fr === validDate[0]
          )[0];
        monthDate =
          "" +
          (new Date(monthLocale.name + " 01, " + validDate[1]).getMonth() + 1);
      }

      if (monthDate.length < 2) {
        monthDate = "0" + monthDate;
      }

      return [validDate[1], monthDate, "01"].join("/");
    }
    return "";
  };

  //Get date only from datetime
  static formatDate(dateWithTime: any) {
    const date: Date = new Date(dateWithTime);
    let month: string = "" + (date.getMonth() + 1);
    let day: string = "" + date.getDate();
    const year: number = date.getFullYear();

    if (month.length < 2) month = "0" + month;
    if (day.length < 2) day = "0" + day;

    return [year, month, day].join("/");
  }

  //Tracking analytics event
  static onEventAnalytics = (
    category: string,
    action: string,
    label: string
  ) => {
    ReactGA.event(action, { category, label });
  };

  // Get start Date
  static getStartDate = (date: string) => {
    const month = getMonth(new Date(date));
    const year = getYear(new Date(date));
    if (month > 11) {
      return UtilityHelper.getFromDate(year + 1, 0);
    } else {
      return UtilityHelper.getFromDate(year, month + 1);
    }
  };

  // Get how to assess guidelines
  static getHowToAssessGuidelines = (
    howToAssess: string,
    title: string,
    whatYouNeed: string,
    interview: string = ""
  ) => {
    return (
      <ul>
        {title && <li className="drawer-title">{parse(title)}</li>}
        {howToAssess && (
          <li>
            <h5 className="line-after">
              <LanguageTranslationWrapperComponent
                keyId={"Common.HowToAssess"}
              />
              <img src={InfoIcon} className="info-icon img-fluid" />
            </h5>
            {parse(howToAssess)}
          </li>
        )}
        {whatYouNeed && (
          <li className="mt-3">
            <h5 className="line-after">
              <LanguageTranslationWrapperComponent
                keyId={"Common.WhatYouNeed"}
              />
            </h5>
            {parse(whatYouNeed)}
          </li>
        )}
        {interview && (
          <li className="mt-3">
            <h5 className="line-after">
              <LanguageTranslationWrapperComponent keyId={"Common.Interview"} />
            </h5>
            <PeopleIcon />
            {parse(interview)}
          </li>
        )}
      </ul>
    );
  };

  // Get role name
  static getRoleName = (userType: number) => {
    switch (userType) {
      case UserRoleEnum.Viewer:
        return "Viewer";
      case UserRoleEnum.Manager:
        return "Manager";
      case UserRoleEnum.SuperManager:
        return "SuperManager";
      case UserRoleEnum.WHOAdmin:
        return "WHOAdmin";
    }
  };

  //check date is valid or not
  static isValidDate(dateString: Date): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }
}
