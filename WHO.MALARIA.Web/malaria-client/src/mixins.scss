﻿/****************** FONT SIZE MIXIN START ******************/
@mixin font-size($sizeValue: 12) {
  font-size: $sizeValue;
}

@mixin line-height($heightValue: 20) {
  line-height: $heightValue;
}

@mixin letter-spacing($letter-spacing: 0) {
  letter-spacing: $letter-spacing;
}

@mixin font-size-line-height(
  $sizeValue: 12,
  $heightValue: 20,
  $letter-spacing: 0
) {
  @include font-size($sizeValue);
  @include line-height($heightValue);
  @include letter-spacing($letter-spacing);
}

/****************** FONT SIZE MIXIN END ******************/

/****** BORDER RADIUS MIXIN START ******/

@mixin border-radius($radius) {
  border-radius: $radius;
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
}

/****** BORDER RADIUS MIXIN END ******/

/****************** GLOBAL VARIABLES START ******************/
$font-stack: "Noto Sans", sans-serif;

/****************** GLOBAL COLOR STARTS ******************/
$primary_color: #196aaa;
$secondary_color: #230050;
$white_color: #ffffff;
$black_color: #000000;
$form_bg_color: #f7f8fb;
$body_text_color: #646464;
$dropdown_bg_color: #190a37;
$danger_color: #fa3c4b;
$transparent_color: transparent;
$grid_bg_color: #f3f6f8;
$green_color: #1fbb54;
$orange_color: #db7e59;
$grey_color: #ececec;

/****************** GLOBAL COLOR ENDS ******************/

/****************** FONT SIZE VARIABLES START ******************/

$font_size_9: 9px;
$font_size_10: 10px;
$font_size_11: 11px;
$font_size_12: 12px;
$font_size_13: 13px;
$font_size_14: 14px;
$font_size_15: 15px;
$font_size_16: 16px;
$font_size_17: 17px;
$font_size_18: 18px;
$font_size_20: 20px;
$font_size_22: 22px;
$font_size_23: 23px;
$font_size_24: 24px;
$font_size_26: 26px;
$font_size_28: 28px;
$font_size_30: 30px;

/****************** FONT SIZE VARIABLES END ******************/
