{"name": "maleria-client", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@babel/code-frame": "^7.14.5", "@material-ui/icons": "^4.11.2", "@progress/kendo-file-saver": "^1.1.1", "@testing-library/jest-dom": "^5.11.10", "@testing-library/react": "^11.2.6", "@testing-library/user-event": "^12.8.3", "@types/jest": "^26.0.22", "@types/react-dom": "^17.0.3", "axios": "^0.21.1", "classnames": "^2.3.1", "d3-geo": "^1.11.7", "hammerjs": "^2.0.8", "i18next": "^20.2.2", "i18next-browser-languagedetector": "^6.1.1", "i18next-http-backend": "^1.2.4", "js-cookie": "^2.2.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-ga": "^3.3.0", "react-i18next": "^11.8.15", "react-scripts": "4.0.3", "sass": "^1.52.3", "terser": "^5.5.1", "topojson-client": "^3.1.0", "typescript": "^4.9.5", "uuid": "^8.3.2", "web-vitals": "^1.1.1"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "start": "vite"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@date-io/date-fns": "^1.3.13", "@material-ui/core": "^4.11.4", "@material-ui/lab": "^4.0.0-alpha.58", "@material-ui/pickers": "^3.3.10", "@popperjs/core": "^2.9.2", "@progress/kendo-data-query": "^1.5.5", "@progress/kendo-drawing": "^1.10.1", "@progress/kendo-licensing": "^1.1.4", "@progress/kendo-react-animation": "^4.7.0", "@progress/kendo-react-charts": "^4.7.0", "@progress/kendo-react-data-tools": "^4.7.0", "@progress/kendo-react-dateinputs": "^4.7.0", "@progress/kendo-react-dropdowns": "^4.7.0", "@progress/kendo-react-grid": "^4.7.0", "@progress/kendo-react-inputs": "^4.7.0", "@progress/kendo-react-intl": "^4.7.0", "@progress/kendo-react-treeview": "^4.7.0", "@progress/kendo-theme-material": "^3.34.0", "@types/crypto-js": "^4.0.1", "@types/js-cookie": "^2.2.6", "@types/node": "^22.15.21", "@types/react": "^17.0.5", "@types/react-dom": "^17.0.3", "@types/react-router-dom": "^5.1.7", "@types/uuid": "^8.3.3", "@vitejs/plugin-react": "^4.5.0", "babel-preset-react-app": "^10.0.1", "bootstrap": "^5.0.1", "crypto-js": "^4.0.0", "date-fns": "^2.22.1", "flag-icon-css": "^3.5.0", "framer-motion": "^4.1.17", "html-react-parser": "^1.2.7", "jsdom": "^26.1.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-redux": "^7.2.4", "react-responsive-carousel": "^3.2.20", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "redux": "^4.1.0", "redux-saga": "^1.1.3", "rxjs": "^7.1.0", "vite": "^6.3.5", "vitest": "^3.1.4"}, "browser": {"crypto": false}}