﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace WHO.MALARIA.Web.PipelineBehaviours
{
    /// <summary>
    /// Add log in database for insert, update, delete operation
    /// </summary>
    /// <typeparam name="TRequest">Object of request type</typeparam>
    /// <typeparam name="TResponse">Object of response type</typeparam>
    public class LoggingBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    {
        private readonly ILogger<LoggingBehaviour<TRequest, TResponse>> _logger;
        public LoggingBehaviour(ILogger<LoggingBehaviour<TRequest, TResponse>> logger)
        {
            _logger = logger;
        }

        /// <summary>
        ///  Adding log in database for insert, update, delete operation
        /// </summary>
        /// <param name="request">Object of request type</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <param name="next">Next request handle</param>
        /// <returns>Response of request</returns>
        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            //Request
            Type myType = request.GetType();

            IList<PropertyInfo> props = new List<PropertyInfo>(myType.GetProperties());

            Dictionary<string, object> requestObject = new Dictionary<string, object>();

            foreach (PropertyInfo prop in props)
            {
                object propValue = prop.GetValue(request, null);

                bool canLog = CanLog(prop, request, 5);

                if (canLog)
                {
                    requestObject.Add(prop.Name, propValue);
                }
            }

            _logger.LogInformation("Request Command-{command} - {@parameters}", typeof(TRequest).Name, requestObject);

            TResponse response = await next();

            return response;
        }

        /// <summary>
        /// Check whether the property is allowed to be logged
        /// </summary>
        /// <param name="prop">Object of propertyInfo</param>
        /// <param name="request">Object of request type</param>
        /// <param name="elementCount">Count of list</param>
        /// <returns>True or False</returns>
        private bool CanLog(PropertyInfo prop, TRequest request, int elementCount)
        {
            bool canLog = true;

            TypeCode typeCode = Type.GetTypeCode(prop.PropertyType);

            //TODO: Check nested object property count
            switch (typeCode)
            {
                case TypeCode.Object:
                    if (prop.PropertyType == typeof(Guid))
                    {
                        return canLog = true;
                    }
                    else if (prop.PropertyType == typeof(IFormFile))
                    {
                        return canLog = false;
                    }
                    else if (prop.PropertyType == typeof(IEnumerable<KeyValuePair<Guid, IFormFile>>))
                    {
                        return canLog = false;
                    }
                    else if (prop.PropertyType == typeof(IEnumerable<Guid>))
                    {
                        return canLog = ((IEnumerable<Guid>)prop.GetValue(request, null))?.Count() <= elementCount;
                    }
                    else if (prop.PropertyType == typeof(IEnumerable<int>))
                    {
                        return canLog = ((IEnumerable<int>)prop.GetValue(request, null))?.Count() <= elementCount;
                    }
                    else if (prop.PropertyType == typeof(IEnumerable<byte>))
                    {
                        return canLog = ((IEnumerable<byte>)prop.GetValue(request, null))?.Count() <= elementCount;
                    }
                    else if (prop.PropertyType == typeof(List<Guid>))
                    {
                        return canLog = ((List<Guid>)prop.GetValue(request, null))?.Count <= elementCount;
                    }
                    else if (prop.PropertyType == typeof(Guid[]))
                    {
                        return canLog = ((Guid[])prop.GetValue(request, null))?.Length <= elementCount;
                    }
                    else if (prop.PropertyType == typeof(int[]))
                    {
                        return canLog = ((int[])prop.GetValue(request, null))?.Length <= elementCount;
                    }
                    else if (prop.PropertyType == typeof(List<int>))
                    {
                        return canLog = ((List<int>)prop.GetValue(request, null))?.Count <= elementCount;
                    }
                    break;

                default:
                    return canLog = true;
            }

            return canLog;
        }
    }
}
