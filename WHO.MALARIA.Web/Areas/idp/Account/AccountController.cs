using System;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Duende.IdentityServer.Events;
using Duende.IdentityServer.Models;
using Duende.IdentityServer.Services;
using Duende.IdentityServer.Stores;
using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Web.Areas.idp.Helper;
using WHO.MALARIA.Web.Areas.Idp.Models;

namespace WHO.MALARIA.Web.Areas.idp.Account
{
    /// <summary>
    /// Implements a typical login/logout/provision work flow for local and external accounts.
    /// The login service encapsulates the interactions with the user data store. This data store is in-memory only and cannot be used for production!
    /// The interaction service provides a way for the UI to communicate with identity server for validation and context retrieval
    /// </summary>
    [Area("idp")]
    public class AccountController : Controller
    {
        private readonly IIdentityServerInteractionService _interaction;
        private readonly IClientStore _clientStore;
        private readonly IAuthenticationSchemeProvider _schemeProvider;
        private readonly UserManager<IdentityDto> _userManager;
        private readonly SignInManager<IdentityDto> _signInManager;
        private readonly IEventService _events;
        private readonly IOptions<IdentityOptions> _optionsAccessor;
        private readonly UrlEncoder _urlEncoder;
        private readonly IMediator _mediator;



        public AccountController(
            IIdentityServerInteractionService interaction,
            IClientStore clientStore,
            IAuthenticationSchemeProvider schemeProvider,
            UserManager<IdentityDto> userManager,
            SignInManager<IdentityDto> signInManager,
            IEventService events,
            IOptions<IdentityOptions> optionsAccessor,
            UrlEncoder urlEncoder,
            IMediator mediator
            )
        {
            _interaction = interaction;
            _clientStore = clientStore;
            _schemeProvider = schemeProvider;
            _userManager = userManager;
            _signInManager = signInManager;
            _events = events;
            _optionsAccessor = optionsAccessor;
            _urlEncoder = urlEncoder;
            _mediator = mediator;

        }

        /// <summary>
        ///  Entry point into the login work flow
        /// </summary>
        /// <param name="returnUrl">Return URL</param>
        /// <param name="isPasswordReset">Boolean status of reset password success,
        ///   Used to show password reset success message on login page as during reset password we redirect to login page
        /// </param>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> Login(string returnUrl, bool isPasswordReset)
        {

            var loginHelper = new LoginHelper(_interaction, _schemeProvider, _clientStore);

            // build a model so we know what to show on the login page
            LoginViewModel vm = await loginHelper.BuildLoginViewModelAsync(returnUrl);
            vm.IsPasswordReset = isPasswordReset;

            if (vm.IsExternalLoginOnly)
            {
                // we only have one option for logging in and it's an external provider
                return RedirectToAction("Challenge", "External", new { provider = vm.ExternalLoginScheme, returnUrl });
            }

            return View(vm);
        }


        /// <summary>
        /// Handle post-back from user name/password login
        /// </summary>
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginInputModel model, string button)
        {
            // check if we are in the context of an authorization request
            AuthorizationRequest context = await _interaction.GetAuthorizationContextAsync(model.ReturnUrl);

            var loginHelper = new LoginHelper(_interaction, _schemeProvider, _clientStore);

            // the user clicked the "cancel" button
            if (button != "login")
            {
                if (context != null)
                {
                    // if the user cancels, send a result back into IdentityServer as if they
                    // denied the consent (even if this client does not require consent).
                    // this will send back an access denied OIDC error response to the client.
                    await _interaction.GrantConsentAsync(context, new ConsentResponse { Error = AuthorizationError.AccessDenied });
                    return Redirect("~/");
                }
                else
                {
                    // since we don't have a valid context, then we just go back to the home page
                    return Redirect("~/");
                }
            }

            if (ModelState.IsValid)
            {
                //Try to locate an Identity record with the provided userName. If failed, redirect back to login page
                IdentityDto identity = await _userManager.FindByNameAsync(model.Username);

                if (identity == null)
                {
                    await _events.RaiseAsync(new UserLoginFailureEvent(model.Username, "invalid user name"));

                    LoginViewModel vm = await loginHelper.BuildLoginViewModelAsync(model);

                    vm.LoginFailedErrorMessage = "Invalid credentials";

                    return View(vm);
                }

                //Validate the provided password. If invalid, redirect to login page
                bool isPasswordValid = await _userManager.CheckPasswordAsync(identity, model.Password);
                if (isPasswordValid == false)
                {
                    await _events.RaiseAsync(new UserLoginFailureEvent(model.Username, "Invalid credentials"));

                    LoginViewModel vm = await loginHelper.BuildLoginViewModelAsync(model);
                    return View(vm);
                }

                await _signInManager.SignInAsync(identity, false);

                if (context != null)
                {
                    // we can trust model.ReturnUrl since GetAuthorizationContextAsync returned non-null
                    return Redirect(model.ReturnUrl);
                }

                // request for a local page
                if (Url.IsLocalUrl(model.ReturnUrl))
                {
                    return Redirect(model.ReturnUrl);
                }
                else if (string.IsNullOrEmpty(model.ReturnUrl))
                {
                    return Redirect("~/");
                }
                else
                {
                    // user might have clicked on a malicious link - should be logged
                    throw new Exception("Invalid return URL");
                }
            }
            else
            {
                LoginViewModel vm = await loginHelper.BuildLoginViewModelAsync(model);

                vm.LoginFailedErrorMessage = "Invalid credentials";

                return View(vm);
            }
        }

        public IActionResult AccessDenied()
        {
            return View();
        }

        public ActionResult Logout() => View();

    }
}
