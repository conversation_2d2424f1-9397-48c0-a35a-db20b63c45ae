using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle creation of assessment using CreateAssessmentCommand
    /// </summary>
    public class CreateAssessmentCommandHandler : RuleBase, IRequestHandler<CreateAssessmentCommand, Guid>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assesmentRuleChecker;      

        public CreateAssessmentCommandHandler(IMediator mediator,
                                              IUnitOfWork unitOfWork,             
                                              ICommonRuleChecker commonRuleChecker, 
                                              ITranslationService translationService,
                                              IAssessmentRuleChecker assesmentRuleChecker)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;           
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assesmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Create assessment using CreateAssessmentCommand
        /// </summary>
        /// <param name="request"> Command includes properties related to assessment create operation </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> Id of Assessment </returns>
        public async Task<Guid> Handle(CreateAssessmentCommand request, CancellationToken cancellationToken)
        {
            // Debug logging for date values
            System.Diagnostics.Debug.WriteLine($"CreateAssessment - StartDate: {request.StartDate:yyyy-MM-dd HH:mm:ss}, EndDate: {request.EndDate:yyyy-MM-dd HH:mm:ss}");
            System.Diagnostics.Debug.WriteLine($"CreateAssessment - StartDate >= EndDate: {request.StartDate >= request.EndDate}");

            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CountryId, "Country"));
            CheckRule(new DateNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.StartDate, "StartDate"));
            CheckRule(new DateNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.EndDate, "EndDate"));
            CheckRule(new DateRangeShouldBeValidRule(_translationService, request.StartDate, request.EndDate));
            
            //Below rule commented as client suggested to create multiple assessment in same year 
            //CheckRule(new ShouldBeAllowedToCreateAssessment(_translationService, _assesmentRuleChecker, request.CountryId, request.StartDate, request.EndDate));

            // create assessment
            Guid assessmentId = Guid.NewGuid();
            Assessment assessment = new Assessment(assessmentId, request.StartDate, request.EndDate, request.CountryId);

            // create user and assessment mapping

            //Manager
            if (request.Manager != null && request.Manager != Guid.Empty)
                assessment.AddAssessmentUser(assessmentId, request.Manager.Value, (int)AssessmentUserRole.Manager);

            //Editors
            if (request.Editors != null && request.Editors.Length > 0)
            {
                foreach (Guid editor in request.Editors)
                {
                    assessment.AddAssessmentUser(assessmentId, editor, (int)AssessmentUserRole.Editor);
                }
            }

            //Reviewers
            if (request.Reviewers != null && request.Reviewers.Length > 0)
            {
                foreach (Guid reviewer in request.Reviewers)
                {
                    assessment.AddAssessmentUser(assessmentId, reviewer, (int)AssessmentUserRole.Reviewer);
                }
            }

            //assessment status record
            assessment.AddAssessmentStatus(assessmentId, (int)Domain.Enum.AssessmentStatus.Created);

            _unitOfWork.AssessmentRepository.Add(assessment);
            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            IEnumerable<Guid> editorsUserIds = assessment.AssessmentUsers.Where(x => x.Role == (int)AssessmentUserRole.Editor).Select(y => y.UserId);
            IEnumerable<Guid> reviewersUserIds = assessment.AssessmentUsers.Where(x => x.Role == (int)AssessmentUserRole.Reviewer).Select(y => y.UserId);

            if (editorsUserIds.Any())
            {
                IEnumerable<string> editorsEmailAddresses = await _unitOfWork.UserRepository.GetEmailAddresses(editorsUserIds);
                await _mediator.Publish(new AssessmentAssignmentEmailNotification(editorsEmailAddresses.ToArray(),
                                                                                  AssessmentUserRole.Editor,
                                                                                  assessment.CountryId,
                                                                                  false));
            }

            if (reviewersUserIds.Any())
            {
                IEnumerable<string> reviewersEmailAddresses = await _unitOfWork.UserRepository.GetEmailAddresses(reviewersUserIds);
                await _mediator.Publish(new AssessmentAssignmentEmailNotification(reviewersEmailAddresses.ToArray(),
                                                                                  AssessmentUserRole.Reviewer,
                                                                                  assessment.CountryId,
                                                                                  false));
            }

            return assessmentId;
        }
    }
}
